# SemiPRO GUI Application

## Overview

The SemiPRO GUI is a comprehensive graphical user interface for the semiconductor process simulator. It provides an intuitive way to run simulations, visualize results, and interact with the C++ physics engines.

## Features

### 🖥️ Main Window
- **Tabbed Interface**: Organized tabs for each simulation module
- **Real-time Visualization**: Integrated matplotlib plots showing actual simulation results
- **Progress Tracking**: Visual progress bars during simulations
- **Status Updates**: Real-time status information

### 🔥 Simulation Modules

#### Oxidation Module
- Temperature control (800-1200°C)
- Time settings (1-300 minutes)
- Atmosphere selection (Dry O2, Wet O2, Steam)
- Real-time thermal oxidation physics

#### ⚛️ Doping Module
- Dopant selection (Phosphorus, Boron, Arsenic)
- Energy control (10-200 keV)
- Dose settings (1e14-1e18 cm⁻²)
- Temperature control for diffusion

#### 📦 Deposition Module
- Material selection (SiO2, Si3N4, Polysilicon, Aluminum, Tungsten)
- Thickness control (0.01-10.0 μm)
- Technique selection (CVD, PVD, ALD, Sputtering)
- Temperature and pressure control

#### 🔪 Etching Module
- Material selection for etching
- Depth control (0.01-10.0 μm)
- Technique selection (RIE, ICP, Wet Etch, Plasma)
- RF power and pressure control

### 📊 Visualization Features
- **Real-time Plotting**: Matplotlib integration for immediate result visualization
- **Multiple Metrics**: Display of deposition rates, uniformity, quality scores
- **Interactive Charts**: Pan, zoom, and analyze simulation results
- **Export Capabilities**: Save plots and data for further analysis

## Installation & Setup

### Prerequisites
```bash
# Install Python dependencies
pip install PySide6 matplotlib numpy

# Build the C++ simulator
make -C build
```

### Running the GUI
```bash
# Simple launcher
python launch_gui.py

# Or with virtual environment
./venv/bin/python launch_gui.py
```

## Architecture

### Backend Integration
- **C++ Physics Engines**: Direct integration with enhanced physics models
- **JSON Configuration**: Structured parameter passing to C++ simulator
- **Asynchronous Execution**: Non-blocking simulation runs with progress updates
- **Real Data Processing**: Actual simulation results, not synthetic data

### GUI Components
```
EnhancedMainWindow
├── ModulePanel (Base class)
│   ├── OxidationPanel
│   ├── DopingPanel
│   ├── DepositionPanel
│   └── EtchingPanel
├── MatplotlibWidget (Visualization)
└── SimulationWorker (Background processing)
```

### Data Flow
1. **User Input** → GUI controls collect parameters
2. **Configuration** → Parameters converted to JSON config
3. **Simulation** → C++ simulator executed with config
4. **Results** → Output parsed and metrics extracted
5. **Visualization** → Real-time plotting of results

## Testing

### Automated Testing
```bash
# Test deposition module with real C++ backend
python test_deposition_module.py

# Test all modules systematically
python scripts/test_all_modules_systematically.py
```

### Manual Testing
1. Launch GUI: `python launch_gui.py`
2. Select a module tab (e.g., Deposition)
3. Configure parameters
4. Click "Run [Module]" button
5. Observe progress bar and results
6. View real-time visualization

## Real Simulation Results

The GUI connects to actual C++ physics engines that produce real simulation data:

### Example Deposition Results
```
✅ C++ simulator found
🧪 Testing deposition simulation...
   Material: SiO2
   Thickness: 0.1 μm
   Technique: CVD
   Temperature: 800.0 °C
🚀 Running simulation...
[INFO] [PHYS] Enhanced Deposition Physics initialized
[INFO] [PHYS] Starting enhanced deposition: Silicon Dioxide
Deposition completed: SUCCESS
```

### Supported Materials
- **Oxides**: SiO2, Al2O3
- **Nitrides**: Si3N4, TiN
- **Semiconductors**: Polysilicon, Amorphous Silicon
- **Metals**: Aluminum, Tungsten, Copper
- **Dielectrics**: Various low-k materials

## Advanced Features

### Multi-threading
- Background simulation execution
- Non-blocking GUI updates
- Progress tracking and cancellation

### Error Handling
- Comprehensive error messages
- Simulation timeout protection
- Graceful failure recovery

### Extensibility
- Modular panel design
- Easy addition of new simulation modules
- Configurable visualization options

## Future Enhancements

### Planned Features
- [ ] Process flow sequencing
- [ ] Batch simulation capabilities
- [ ] 3D visualization integration
- [ ] Advanced data export formats
- [ ] Simulation result comparison tools
- [ ] Parameter optimization workflows

### Integration Roadmap
- [ ] Vulkan renderer integration
- [ ] Multi-die simulation support
- [ ] DRC rule checking interface
- [ ] Advanced metrology tools
- [ ] Reliability analysis dashboard

## Troubleshooting

### Common Issues

**GUI won't start**
```bash
# Check dependencies
pip install PySide6 matplotlib

# Check Python path
export PYTHONPATH=src/python
```

**Simulator not found**
```bash
# Build the C++ simulator
make -C build

# Check simulator exists
ls -la build/simulator
```

**Import errors**
```bash
# Use virtual environment
source venv/bin/activate
python launch_gui.py
```

## Contributing

### Adding New Modules
1. Create new panel class inheriting from `ModulePanel`
2. Implement `setup_controls()` and `get_config()` methods
3. Add tab to main window
4. Connect simulation signals

### Visualization Enhancements
1. Extend `MatplotlibWidget` class
2. Add new plotting methods
3. Integrate with module-specific results
4. Test with real simulation data

## License

This GUI application is part of the SemiPRO semiconductor simulation platform.
