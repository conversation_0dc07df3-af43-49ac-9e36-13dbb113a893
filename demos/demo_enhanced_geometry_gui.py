#!/usr/bin/env python3
"""
Enhanced Geometry Module GUI Demonstration
==========================================

Comprehensive demonstration of the enhanced geometry module with:
- Industrial device examples (7 real semiconductor devices)
- GUI integration with tabbed interface
- 3D visualization capabilities
- Device pattern generation
- Layer stack management

Author: Dr<PERSON>
"""

import sys
import os
import json
from datetime import datetime

# Add the src/python directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src', 'python'))

def test_industrial_examples():
    """Test all 7 industrial device examples"""
    print("=" * 60)
    print("TESTING INDUSTRIAL DEVICE EXAMPLES")
    print("=" * 60)
    
    try:
        from examples.industrial_devices import IndustrialDeviceExamples
        
        examples = IndustrialDeviceExamples(output_dir="demo_output")
        
        # Test all 7 examples
        example_methods = [
            ("180nm MOSFET Transistor", examples.example_1_mosfet_transistor),
            ("14nm FinFET 3D Transistor", examples.example_2_finfet_3d_transistor),
            ("MEMS Accelerometer", examples.example_3_mems_accelerometer),
            ("3D NAND Memory Cell Array", examples.example_4_memory_cell_array),
            ("1200V Power IGBT", examples.example_5_power_igbt),
            ("GaN RF Amplifier", examples.example_6_rf_amplifier),
            ("CMOS Image Sensor", examples.example_7_image_sensor)
        ]
        
        results = []
        for name, method in example_methods:
            print(f"\nTesting {name}...")
            try:
                result = method()
                print(f"✓ SUCCESS: {result.get('device_name', name)}")
                print(f"  Technology: {result.get('technology_node', 'N/A')}")
                print(f"  Status: {result.get('status', 'N/A')}")
                results.append({"name": name, "status": "SUCCESS", "result": result})
            except Exception as e:
                print(f"✗ FAILED: {e}")
                results.append({"name": name, "status": "FAILED", "error": str(e)})
        
        # Summary
        success_count = sum(1 for r in results if r["status"] == "SUCCESS")
        print(f"\n📊 SUMMARY: {success_count}/{len(results)} examples successful")
        
        return results
        
    except ImportError as e:
        print(f"❌ Industrial examples module not available: {e}")
        return []

def test_geometry_modules():
    """Test geometry module components"""
    print("\n" + "=" * 60)
    print("TESTING GEOMETRY MODULE COMPONENTS")
    print("=" * 60)
    
    components = [
        ("GeometryManager", "geometry.geometry_manager", "GeometryManager"),
        ("DevicePatternGenerator", "geometry.device_patterns", "DevicePatternGenerator"),
        ("LayerStackManager", "geometry.layer_stacks", "LayerStackManager"),
        ("GeometryAnalyzer", "geometry.geometry_analysis", "GeometryAnalyzer"),
        ("GeometryUtils", "geometry.geometry_utils", "GeometryUtils"),
        ("Geometry3DVisualizer", "geometry.geometry_3d_visualizer", "Geometry3DVisualizer")
    ]
    
    results = []
    for name, module_path, class_name in components:
        print(f"\nTesting {name}...")
        try:
            module = __import__(module_path, fromlist=[class_name])
            cls = getattr(module, class_name)
            print(f"✓ SUCCESS: {name} available")
            results.append({"name": name, "status": "SUCCESS"})
        except Exception as e:
            print(f"✗ FAILED: {e}")
            results.append({"name": name, "status": "FAILED", "error": str(e)})
    
    success_count = sum(1 for r in results if r["status"] == "SUCCESS")
    print(f"\n📊 SUMMARY: {success_count}/{len(results)} components available")
    
    return results

def test_gui_integration():
    """Test GUI integration"""
    print("\n" + "=" * 60)
    print("TESTING GUI INTEGRATION")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.geometry_panel import GeometryPanel, IndustrialExamplesWidget
        
        print("✓ SUCCESS: PySide6 available")
        print("✓ SUCCESS: GeometryPanel available")
        print("✓ SUCCESS: IndustrialExamplesWidget available")
        
        # Test widget creation (without showing GUI)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        panel = GeometryPanel()
        print("✓ SUCCESS: GeometryPanel created")
        
        examples_widget = IndustrialExamplesWidget()
        print("✓ SUCCESS: IndustrialExamplesWidget created")
        
        return {"status": "SUCCESS", "message": "GUI components working"}
        
    except Exception as e:
        print(f"✗ FAILED: {e}")
        return {"status": "FAILED", "error": str(e)}

def test_3d_visualization():
    """Test 3D visualization capabilities"""
    print("\n" + "=" * 60)
    print("TESTING 3D VISUALIZATION")
    print("=" * 60)
    
    try:
        from geometry.geometry_3d_visualizer import Geometry3DVisualizer
        
        visualizer = Geometry3DVisualizer()
        print("✓ SUCCESS: Geometry3DVisualizer created")
        
        # Test device data creation
        device_data = {
            'name': 'Test_MOSFET',
            'type': 'MOSFET',
            'parameters': {
                'gate_length': 0.18,
                'gate_width': 2.0,
                'source_drain_length': 1.0
            }
        }
        
        result = visualizer.create_3d_device_visualization(device_data, "demo_3d_output")
        
        if result.get('success', False):
            print("✓ SUCCESS: 3D visualization created")
            return {"status": "SUCCESS", "result": result}
        else:
            print(f"⚠ WARNING: 3D visualization failed: {result.get('error', 'Unknown error')}")
            return {"status": "WARNING", "result": result}
            
    except Exception as e:
        print(f"✗ FAILED: {e}")
        return {"status": "FAILED", "error": str(e)}

def generate_report(test_results):
    """Generate comprehensive test report"""
    print("\n" + "=" * 60)
    print("COMPREHENSIVE TEST REPORT")
    print("=" * 60)
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_results": test_results,
        "summary": {}
    }
    
    # Calculate summary statistics
    for category, results in test_results.items():
        if isinstance(results, list):
            success_count = sum(1 for r in results if r.get("status") == "SUCCESS")
            total_count = len(results)
            report["summary"][category] = {
                "success_count": success_count,
                "total_count": total_count,
                "success_rate": f"{success_count/total_count*100:.1f}%" if total_count > 0 else "0%"
            }
        else:
            report["summary"][category] = {"status": results.get("status", "UNKNOWN")}
    
    # Print summary
    print(f"\n📊 OVERALL RESULTS:")
    for category, summary in report["summary"].items():
        if "success_rate" in summary:
            print(f"  {category}: {summary['success_rate']} ({summary['success_count']}/{summary['total_count']})")
        else:
            print(f"  {category}: {summary['status']}")
    
    # Save report
    with open("demo_enhanced_geometry_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Report saved to: demo_enhanced_geometry_report.json")
    
    return report

def main():
    """Main demonstration function"""
    print("🚀 ENHANCED GEOMETRY MODULE DEMONSTRATION")
    print("=" * 60)
    print("Testing comprehensive geometry module with industrial examples")
    print("and GUI integration for semiconductor device simulation.")
    print("=" * 60)
    
    # Run all tests
    test_results = {
        "industrial_examples": test_industrial_examples(),
        "geometry_modules": test_geometry_modules(),
        "gui_integration": test_gui_integration(),
        "3d_visualization": test_3d_visualization()
    }
    
    # Generate comprehensive report
    report = generate_report(test_results)
    
    print("\n🎉 DEMONSTRATION COMPLETE!")
    print("Check the generated output directories and report file for detailed results.")

if __name__ == "__main__":
    main()
