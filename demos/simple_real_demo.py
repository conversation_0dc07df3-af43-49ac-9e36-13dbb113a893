#!/usr/bin/env python3
"""
Simple Real Semiconductor Process Demo
Shows working simulations with realistic parameters
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def run_simple_simulation(process_type, config_data, description):
    """Run a simple simulation and show results"""
    
    print(f"\n🔬 {description}")
    print("=" * 50)
    
    # Create config file
    config_file = f"config/simple_{process_type}.json"
    os.makedirs("config", exist_ok=True)
    
    with open(config_file, 'w') as f:
        json.dump(config_data, f, indent=2)
    
    print(f"📋 Configuration saved to: {config_file}")
    
    try:
        # Run actual C++ simulator
        simulator_path = project_root / "build" / "simulator"
        if not simulator_path.exists():
            print(f"❌ C++ simulator not found at {simulator_path}")
            return False
            
        print(f"🚀 Running C++ simulation...")
        start_time = time.time()
        result = subprocess.run([
            str(simulator_path),
            "--process", process_type,
            "--config", config_file
        ], capture_output=True, text=True, timeout=30)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"⏱️  Execution time: {execution_time:.3f} seconds")
        
        if result.returncode == 0:
            print("✅ SIMULATION SUCCESS")
            print("📊 Results:")
            
            # Parse and highlight key results
            for line in result.stdout.split('\n'):
                if line.strip():
                    if any(keyword in line.lower() for keyword in ['completed', 'grown', 'deposited', 'etched', 'implanted']):
                        print(f"   🎯 {line}")
                    else:
                        print(f"   {line}")
            return True
        else:
            print("❌ SIMULATION FAILED")
            if result.stderr.strip():
                print("🚨 Error details:")
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Simulation timed out")
        return False
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

def main():
    """Run simple real semiconductor process demonstrations"""
    
    print("🏭 SIMPLE REAL SEMICONDUCTOR PROCESS DEMO")
    print("🎯 Demonstrating working physics simulations")
    print("🔬 Using realistic industrial parameters")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    # Test 1: Thin Gate Oxide (working parameters)
    total_tests += 1
    gate_oxide_config = {
        "wafer": {
            "name": "demo_wafer",
            "diameter": 200.0,
            "thickness": 0.725,
            "material": "Silicon",
            "crystal_orientation": "100"
        },
        "process": {
            "operation": "oxidation",
            "temperature": 900.0,
            "time": 0.1,  # 6 minutes
            "atmosphere": "dry",
            "target_thickness": 0.005
        }
    }
    
    if run_simple_simulation("oxidation", gate_oxide_config, "Gate Oxide Growth (5nm)"):
        success_count += 1
    
    # Test 2: Ion Implantation
    total_tests += 1
    implant_config = {
        "wafer": {
            "name": "demo_wafer",
            "diameter": 200.0,
            "thickness": 0.725,
            "material": "Silicon",
            "crystal_orientation": "100"
        },
        "process": {
            "operation": "doping",
            "dopant_type": "boron",
            "concentration": 1e16,
            "energy": 50.0,
            "temperature": 1000.0,
            "time": 0.5
        }
    }
    
    if run_simple_simulation("doping", implant_config, "Boron Ion Implantation"):
        success_count += 1
    
    # Test 3: CVD Deposition
    total_tests += 1
    deposition_config = {
        "wafer": {
            "name": "demo_wafer",
            "diameter": 200.0,
            "thickness": 0.725,
            "material": "Silicon",
            "crystal_orientation": "100"
        },
        "process": {
            "operation": "deposition",
            "material": "SiO2",
            "thickness": 0.1,
            "technique": "CVD",
            "temperature": 800.0,
            "pressure": 1.0
        }
    }
    
    if run_simple_simulation("deposition", deposition_config, "CVD Silicon Dioxide Deposition"):
        success_count += 1
    
    # Test 4: Plasma Etching
    total_tests += 1
    etching_config = {
        "wafer": {
            "name": "demo_wafer",
            "diameter": 200.0,
            "thickness": 0.725,
            "material": "Silicon",
            "crystal_orientation": "100"
        },
        "process": {
            "operation": "etching",
            "target_material": "SiO2",
            "depth": 0.05,
            "technique": "RIE",
            "pressure": 50.0,
            "power": 300.0,
            "selectivity": 10.0
        }
    }
    
    if run_simple_simulation("etching", etching_config, "Reactive Ion Etching"):
        success_count += 1
    
    # Summary
    print(f"\n🏆 DEMO RESULTS SUMMARY")
    print("=" * 60)
    print(f"✅ Successful simulations: {success_count}/{total_tests}")
    print(f"📊 Success rate: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("🎉 ALL SIMULATIONS WORKING!")
        print("🔬 Real physics engines are functioning correctly")
        print("🏭 Ready for industrial applications")
    elif success_count > 0:
        print("⚠️  Some simulations working, others need calibration")
        print("🔧 Physics engines partially functional")
    else:
        print("❌ No simulations working")
        print("🚨 Physics engines need debugging")
    
    print(f"\n🚀 GUI Application Available:")
    print(f"   Run: python launch_gui.py")
    print(f"   Features: {total_tests} simulation modules with real physics")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
