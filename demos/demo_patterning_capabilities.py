#!/usr/bin/env python3
"""
SemiPRO Patterning Module Demonstration

This script demonstrates the comprehensive patterning capabilities
integrated into the SemiPRO geometry module.
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def demo_device_patterns():
    """Demonstrate device pattern library"""
    print("🎯 DEVICE PATTERN LIBRARY DEMONSTRATION")
    print("=" * 60)
    
    from geometry.patterning import DevicePatterns
    
    device_patterns = DevicePatterns()
    
    # Show available devices
    devices = device_patterns.list_available_devices()
    print(f"📱 Available Devices: {len(devices)}")
    
    for device_name in devices:
        spec = device_patterns.get_device_specification(device_name)
        if spec:
            print(f"  • {spec.name} ({spec.technology_node}) - {spec.device_type.value}")
    
    # Generate and visualize masks for different devices
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('SemiPRO Device Pattern Library - Mask Visualizations', fontsize=16)
    
    demo_devices = devices[:6]  # First 6 devices
    
    for i, device_name in enumerate(demo_devices):
        row = i // 3
        col = i % 3
        
        spec = device_patterns.get_device_specification(device_name)
        mask = device_patterns.create_device_pattern_mask(device_name, (100, 100))
        
        if mask is not None:
            im = axes[row, col].imshow(mask, cmap='viridis', interpolation='nearest')
            axes[row, col].set_title(f"{spec.name}\n({spec.technology_node})", fontsize=10)
            axes[row, col].set_xlabel('X (grid units)')
            axes[row, col].set_ylabel('Y (grid units)')
            plt.colorbar(im, ax=axes[row, col], shrink=0.8)
    
    plt.tight_layout()
    plt.savefig('device_patterns_demo.png', dpi=300, bbox_inches='tight')
    print("✅ Device pattern masks saved to 'device_patterns_demo.png'")
    
    return device_patterns

def demo_industrial_examples():
    """Demonstrate industrial examples"""
    print("\n🏭 INDUSTRIAL EXAMPLES DEMONSTRATION")
    print("=" * 60)
    
    from geometry.patterning import IndustrialDeviceExamples
    
    examples = IndustrialDeviceExamples()
    
    # Show available examples
    example_names = examples.list_examples()
    print(f"🏭 Available Industrial Examples: {len(example_names)}")
    
    # Run a few examples and show results
    demo_examples = example_names[:3]  # First 3 examples
    
    for example_name in demo_examples:
        print(f"\n🔬 Running: {example_name}")
        
        # Get example info
        info = examples.get_example_info(example_name)
        if info:
            print(f"  Manufacturer: {info['manufacturer']}")
            print(f"  Technology: {info['technology_node']}")
            print(f"  Market: {info['market_segment']}")
        
        # Run simulation
        result = examples.run_example(example_name)
        if result.get("success"):
            print(f"  ✅ Simulation completed successfully")
            print(f"  Processing Time: {result['simulation_results'].get('Processing Time', 'N/A')}")
            print(f"  Accuracy: {result['simulation_results'].get('Accuracy', 'N/A')}")
        else:
            print(f"  ❌ Simulation failed: {result.get('error', 'Unknown error')}")
    
    return examples

def demo_patterning_manager():
    """Demonstrate patterning manager capabilities"""
    print("\n🎯 PATTERNING MANAGER DEMONSTRATION")
    print("=" * 60)
    
    from geometry.patterning import PatterningManager
    
    # Initialize patterning manager
    patterning = PatterningManager(use_cython=False, database_config=None)
    
    # Create different types of patterns
    patterns_created = []
    
    # 1. MOSFET Pattern
    print("\n📱 Creating MOSFET Pattern...")
    result = patterning.create_mosfet_pattern(0.18, 2.0, 0.5)
    if result.get("success"):
        print(f"  ✅ {result['pattern_name']} created")
        print(f"  Required modules: {', '.join(result['required_modules'])}")
        patterns_created.append(result)
    
    # 2. FinFET Pattern
    print("\n📱 Creating FinFET Pattern...")
    result = patterning.create_finfet_pattern(7.0, 53.0, 24.0, 4)
    if result.get("success"):
        print(f"  ✅ {result['pattern_name']} created")
        print(f"  Required modules: {', '.join(result['required_modules'])}")
        patterns_created.append(result)
    
    # 3. MEMS Pattern
    print("\n📱 Creating MEMS Pattern...")
    mems_params = {"proof_mass_size": 100.0, "spring_width": 2.0, "spring_length": 50.0}
    result = patterning.create_mems_pattern("accelerometer", mems_params)
    if result.get("success"):
        print(f"  ✅ {result['pattern_name']} created")
        print(f"  Required modules: {', '.join(result['required_modules'])}")
        patterns_created.append(result)
    
    # Show pattern list
    all_patterns = patterning.list_patterns()
    print(f"\n📋 Total patterns created: {len(all_patterns)}")
    for pattern_name in all_patterns:
        print(f"  • {pattern_name}")
    
    # Visualize created patterns
    if patterns_created:
        fig, axes = plt.subplots(1, len(patterns_created), figsize=(15, 5))
        if len(patterns_created) == 1:
            axes = [axes]
        
        fig.suptitle('SemiPRO Patterning Manager - Created Patterns', fontsize=16)
        
        for i, pattern_result in enumerate(patterns_created):
            if 'pattern_spec' in pattern_result and hasattr(pattern_result['pattern_spec'], 'mask'):
                mask = pattern_result['pattern_spec'].mask
                if mask is not None:
                    im = axes[i].imshow(mask, cmap='plasma', interpolation='nearest')
                    axes[i].set_title(pattern_result['pattern_name'], fontsize=12)
                    axes[i].set_xlabel('X (grid units)')
                    axes[i].set_ylabel('Y (grid units)')
                    plt.colorbar(im, ax=axes[i], shrink=0.8)
        
        plt.tight_layout()
        plt.savefig('patterning_manager_demo.png', dpi=300, bbox_inches='tight')
        print("✅ Patterning manager results saved to 'patterning_manager_demo.png'")
    
    return patterning

def demo_pattern_analytics():
    """Demonstrate pattern analytics"""
    print("\n📊 PATTERN ANALYTICS DEMONSTRATION")
    print("=" * 60)
    
    from geometry.patterning import PatternAnalytics
    
    analytics = PatternAnalytics()
    
    # Get analytics summary
    summary = analytics.get_summary()
    
    if "error" in summary:
        print(f"📊 Analytics Status: {summary['error']}")
        print("ℹ️  This is expected for a fresh installation with no pattern usage data")
    else:
        print("📊 Analytics Summary:")
        print(f"  Total Patterns: {summary.get('total_patterns', 0)}")
        print(f"  Total Usage Events: {summary.get('total_usage_events', 0)}")
        print(f"  Average Success Rate: {summary.get('average_success_rate', 0):.1f}%")
        print(f"  Average Processing Time: {summary.get('average_processing_time', 0):.2f}s")
    
    return analytics

def demo_process_integration():
    """Demonstrate process flow integration"""
    print("\n🔄 PROCESS FLOW INTEGRATION DEMONSTRATION")
    print("=" * 60)
    
    from geometry.patterning import ProcessFlowIntegration
    
    process_integration = ProcessFlowIntegration()
    
    # Show available process flows
    flows = process_integration.list_available_flows()
    print(f"🔄 Available Process Flows: {len(flows)}")
    
    for flow_name in flows:
        flow = process_integration.get_process_flow("", flow_name)
        if flow:
            print(f"  • {flow.flow_name} ({flow.device_type}) - {len(flow.steps)} steps")
    
    # Show flow summary
    summary = process_integration.get_flow_summary()
    print(f"\n📋 Process Flow Summary:")
    print(f"  Total Flows: {summary['total_flows']}")
    print(f"  Device Types: {', '.join(summary['device_types'])}")
    print(f"  Average Steps: {summary['average_steps']:.1f}")
    print(f"  Average Time: {summary['average_time']:.1f} minutes")
    
    return process_integration

def main():
    """Run comprehensive patterning demonstration"""
    print("🚀 SemiPRO PATTERNING MODULE COMPREHENSIVE DEMONSTRATION")
    print("=" * 80)
    print("This demonstration showcases the complete patterning capabilities")
    print("integrated into the SemiPRO geometry module.")
    print("=" * 80)
    
    try:
        # Run all demonstrations
        device_patterns = demo_device_patterns()
        industrial_examples = demo_industrial_examples()
        patterning_manager = demo_patterning_manager()
        pattern_analytics = demo_pattern_analytics()
        process_integration = demo_process_integration()
        
        print("\n" + "=" * 80)
        print("🎉 DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print("✅ Device Pattern Library: Working")
        print("✅ Industrial Examples: Working")
        print("✅ Patterning Manager: Working")
        print("✅ Pattern Analytics: Working")
        print("✅ Process Integration: Working")
        print("\n📁 Generated Files:")
        print("  • device_patterns_demo.png - Device pattern visualizations")
        print("  • patterning_manager_demo.png - Created pattern visualizations")
        print("  • industrial_examples_output/ - Industrial example results")
        
        print("\n🎯 The SemiPRO patterning module is fully functional and ready for use!")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
