#!/usr/bin/env python3
"""
Custom Device Fabrication Flow Demonstration
Complete demonstration of seamless PostgreSQL-tracked device fabrication
"""

import sys
import os
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demonstrate_custom_device_fabrication():
    """Demonstrate custom device fabrication with seamless module integration"""
    print("Custom Device Fabrication Flow Demonstration")
    print("=" * 80)
    
    # Import Qt application
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    
    try:
        # Initialize the custom fabrication flow
        from src.python.device_fabrication_manager import CustomDeviceFabricationFlow
        from src.python.database_manager import get_database_manager
        
        fab_flow = CustomDeviceFabricationFlow()
        db = get_database_manager()
        
        print("✅ Custom device fabrication flow initialized")
        print(f"✅ Registered modules: {list(fab_flow.module_registry.keys())}")
        
        # Create wafers for fabrication
        wafer_ids = []
        for i in range(3):  # Create 3 wafers for batch processing
            wafer_name = f"CUSTOM_DEVICE_WAFER_{i+1}_{int(time.time())}"
            wafer_id = db.create_wafer(
                wafer_name=wafer_name,
                diameter_mm=200.0,
                thickness_um=775.0,
                material="silicon",
                operator_id="custom_fab_demo"
            )
            wafer_ids.append(str(wafer_id))
        
        print(f"✅ Created {len(wafer_ids)} wafers for batch fabrication")
        
        # Demonstrate different device workflows
        workflows = []
        
        # 1. CMOS Transistor Workflow
        print(f"\n🔬 Creating CMOS Transistor Workflow (14nm)...")
        cmos_workflow_id = fab_flow.create_cmos_transistor_workflow("14nm")
        workflows.append(('CMOS Transistor 14nm', cmos_workflow_id))
        
        # 2. Power Device Workflow
        print(f"🔬 Creating Power Device Workflow...")
        power_workflow_id = fab_flow.create_power_device_workflow()
        workflows.append(('Power MOSFET 600V', power_workflow_id))
        
        print(f"✅ Created {len(workflows)} device workflows")
        
        # Execute fabrication for CMOS transistor
        print(f"\n🚀 Executing CMOS Transistor Fabrication...")
        print("=" * 80)
        
        cmos_results = fab_flow.execute_seamless_fabrication(
            workflow_id=cmos_workflow_id,
            wafer_ids=wafer_ids[:2],  # Use first 2 wafers
            run_name="CMOS_14nm_Batch_Demo"
        )
        
        print(f"✅ CMOS fabrication completed")
        print(f"   Run ID: {cmos_results['run_id']}")
        print(f"   Overall Success: {cmos_results['overall_success']}")
        
        # Display detailed results
        print(f"\n📊 CMOS Fabrication Results:")
        print("-" * 60)
        
        for wafer_result in cmos_results['wafer_results']:
            wafer_id = wafer_result['wafer_id']
            success_rate = wafer_result['success_rate']
            successful_steps = sum(1 for s in wafer_result['steps'] if s.get('success', False))
            total_steps = len(wafer_result['steps'])
            
            print(f"Wafer {wafer_id[:8]}...")
            print(f"  Steps: {successful_steps}/{total_steps} successful ({success_rate:.1f}%)")
            
            # Show first few steps
            for step in wafer_result['steps'][:5]:
                status = "✅" if step.get('success', False) else "❌"
                print(f"    {status} {step['step_name']} ({step['module_id']})")
            
            if len(wafer_result['steps']) > 5:
                print(f"    ... and {len(wafer_result['steps']) - 5} more steps")
        
        # Execute fabrication for Power Device
        print(f"\n🚀 Executing Power Device Fabrication...")
        print("=" * 80)
        
        power_results = fab_flow.execute_seamless_fabrication(
            workflow_id=power_workflow_id,
            wafer_ids=wafer_ids[2:3],  # Use last wafer
            run_name="Power_MOSFET_Demo"
        )
        
        print(f"✅ Power device fabrication completed")
        print(f"   Run ID: {power_results['run_id']}")
        print(f"   Overall Success: {power_results['overall_success']}")
        
        # Get comprehensive database statistics
        print(f"\n📈 Database Integration Statistics:")
        print("=" * 80)
        
        with db.get_cursor() as cursor:
            # Fabrication workflows
            cursor.execute("""
                SELECT COUNT(*) as workflow_count 
                FROM device_fabrication.fabrication_workflows
            """)
            workflow_count = cursor.fetchone()['workflow_count']
            
            # Fabrication runs
            cursor.execute("""
                SELECT COUNT(*) as run_count,
                       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_runs
                FROM device_fabrication.fabrication_runs
            """)
            run_stats = cursor.fetchone()
            
            # Process steps
            cursor.execute("""
                SELECT COUNT(*) as total_steps,
                       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_steps,
                       COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_steps
                FROM device_fabrication.process_flow_steps
            """)
            step_stats = cursor.fetchone()
            
            # Wafer transfers
            cursor.execute("""
                SELECT COUNT(*) as transfer_count
                FROM device_fabrication.wafer_transfers
            """)
            transfer_count = cursor.fetchone()['transfer_count']
            
            # Device layers
            cursor.execute("""
                SELECT COUNT(*) as layer_count,
                       COUNT(DISTINCT wafer_id) as wafers_with_layers,
                       AVG(thickness_nm) as avg_layer_thickness
                FROM device_fabrication.device_layer_stack
            """)
            layer_stats = cursor.fetchone()
            
            print(f"Workflows Created: {workflow_count}")
            print(f"Fabrication Runs: {run_stats['run_count']} ({run_stats['completed_runs']} completed)")
            print(f"Process Steps: {step_stats['total_steps']} total")
            print(f"  ✅ Completed: {step_stats['completed_steps']}")
            print(f"  ❌ Failed: {step_stats['failed_steps']}")
            print(f"  Success Rate: {step_stats['completed_steps']/step_stats['total_steps']*100:.1f}%")
            print(f"Wafer Transfers: {transfer_count}")
            print(f"Device Layers: {layer_stats['layer_count']} layers on {layer_stats['wafers_with_layers']} wafers")
            print(f"Average Layer Thickness: {layer_stats['avg_layer_thickness']:.1f} nm")
        
        # Show module activity
        print(f"\n🔧 Module Activity Summary:")
        print("-" * 60)
        
        with db.get_cursor() as cursor:
            cursor.execute("""
                SELECT module_id,
                       COUNT(*) as total_steps,
                       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_steps,
                       AVG(success_rate) as avg_success_rate
                FROM device_fabrication.process_flow_steps
                GROUP BY module_id
                ORDER BY total_steps DESC
            """)
            
            module_activity = cursor.fetchall()
            
            for activity in module_activity:
                module = activity['module_id']
                total = activity['total_steps']
                completed = activity['completed_steps']
                success_rate = activity['avg_success_rate'] or 0
                
                print(f"{module.title():15} {completed:3d}/{total:3d} steps ({success_rate:.1f}% avg success)")
        
        # Show layer stack examples
        print(f"\n📄 Device Layer Stack Examples:")
        print("-" * 60)
        
        with db.get_cursor() as cursor:
            cursor.execute("""
                SELECT wafer_id, material, thickness_nm, layer_index
                FROM device_fabrication.device_layer_stack
                ORDER BY wafer_id, layer_index
                LIMIT 10
            """)
            
            layers = cursor.fetchall()
            current_wafer = None
            
            for layer in layers:
                if layer['wafer_id'] != current_wafer:
                    current_wafer = layer['wafer_id']
                    print(f"\nWafer {str(current_wafer)[:8]}... Layer Stack:")
                
                print(f"  Layer {layer['layer_index']}: {layer['material']} ({layer['thickness_nm']:.1f} nm)")
        
        # Final summary
        print(f"\n" + "=" * 80)
        print("CUSTOM DEVICE FABRICATION DEMONSTRATION SUMMARY")
        print("=" * 80)
        
        total_wafers = len(wafer_ids)
        total_workflows = len(workflows)
        cmos_success = cmos_results['overall_success']
        power_success = power_results['overall_success']
        
        print(f"✅ Successfully demonstrated custom device fabrication flow")
        print(f"✅ Created {total_workflows} different device workflows")
        print(f"✅ Processed {total_wafers} wafers through fabrication")
        print(f"✅ CMOS Transistor Fabrication: {'SUCCESS' if cmos_success else 'PARTIAL'}")
        print(f"✅ Power Device Fabrication: {'SUCCESS' if power_success else 'PARTIAL'}")
        print(f"✅ Complete PostgreSQL tracking and traceability")
        print(f"✅ Seamless inter-module wafer propagation")
        print(f"✅ Real-time process monitoring and quality control")
        
        print(f"\n🎯 Key Achievements:")
        print(f"   • Dedicated PostgreSQL schema for device fabrication")
        print(f"   • Seamless wafer tracking across all process modules")
        print(f"   • Custom workflow creation for different device types")
        print(f"   • Automatic module integration and parameter passing")
        print(f"   • Complete process history and quality metrics")
        print(f"   • Layer stack formation tracking")
        print(f"   • Inter-module transfer recording")
        print(f"   • Real-time fabrication run monitoring")
        
        if cmos_success and power_success:
            print(f"\n🎉 COMPLETE SUCCESS: Custom device fabrication system operational!")
            print(f"   System ready for production device fabrication workflows")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: Core functionality demonstrated")
            print(f"   Minor issues in some process steps, overall system working")
        
        # Clean up
        app.quit()
        
        return cmos_success and power_success
        
    except Exception as e:
        print(f"❌ Custom device fabrication demonstration failed: {e}")
        app.quit()
        return False

if __name__ == "__main__":
    success = demonstrate_custom_device_fabrication()
    print(f"\n{'='*80}")
    print("Custom Device Fabrication Flow Demonstration Complete!")
    sys.exit(0 if success else 1)
