#!/usr/bin/env python3
"""
Demo script for 3D oxidation visualization with industrial examples.

This script demonstrates the comprehensive 3D visualization capabilities
for various industrial oxidation processes including gate oxides,
STI oxidation, and advanced device structures.

Author: Dr. <PERSON><PERSON>
"""

import sys
import os
import traceback
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def demo_gate_oxide_3d_visualization():
    """Demo 3D visualization for gate oxide formation"""
    print("🔬 Demo: Gate Oxide 3D Visualization")
    print("-" * 50)

    try:
        from oxidation import OxidationManager
        from geometry import GeometryManager
        
        # Initialize managers
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager(wafer_diameter=300.0, wafer_thickness=0.725)  # 300mm wafer

        # Get wafer from geometry manager
        wafer = geometry_manager.wafer
        oxidation_manager.set_wafer(wafer)
        
        # Gate oxide parameters (high-quality thin oxide)
        gate_params = {
            'target_thickness': 2.5,    # nm - ultra-thin gate oxide
            'temperature': 1123.15,     # K (850°C) - low temperature for thin oxide
            'time': 1800,               # seconds (30 minutes)
            'atmosphere': 'dry_oxygen', # High quality dry oxidation
            'pressure': 1.0
        }
        
        print(f"Parameters: {gate_params['target_thickness']} nm oxide at {gate_params['temperature']-273.15:.0f}°C")
        
        # Generate 3D visualization
        viz_result = oxidation_manager.generate_3d_visualization(gate_params, resolution_x=60, resolution_y=60)
        
        if viz_result['success']:
            viz_data = viz_result['visualization_data']
            print(f"✅ Gate oxide 3D visualization generated")
            print(f"   - Thickness uniformity: ±{(viz_data['max_thickness'] - viz_data['min_thickness'])/2:.3f} nm")
            print(f"   - Stress variation: {viz_data['min_stress']:.1f} - {viz_data['max_stress']:.1f} MPa")
            
            # Generate cross-section for critical analysis
            cross_result = oxidation_manager.generate_cross_section_view(gate_params, 'x', 0.5)
            if cross_result['success']:
                print(f"✅ Cross-sectional analysis completed")
        else:
            print(f"❌ Gate oxide visualization failed: {viz_result.get('error', 'Unknown')}")
        
        return viz_result['success'] if viz_result else False
        
    except Exception as e:
        print(f"❌ Gate oxide demo error: {e}")
        traceback.print_exc()
        return False

def demo_sti_oxidation_3d_visualization():
    """Demo 3D visualization for STI (Shallow Trench Isolation) oxidation"""
    print("\n🏗️ Demo: STI Oxidation 3D Visualization")
    print("-" * 50)
    
    try:
        from oxidation import OxidationManager
        from geometry import GeometryManager
        
        # Initialize managers
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager(wafer_diameter=300.0, wafer_thickness=0.725)

        # Get wafer for STI process
        wafer = geometry_manager.wafer
        oxidation_manager.set_wafer(wafer)
        
        # STI oxidation parameters (thick isolation oxide)
        sti_params = {
            'target_thickness': 400.0,   # nm - thick STI oxide
            'temperature': 1373.15,      # K (1100°C) - high temperature for thick oxide
            'time': 14400,               # seconds (4 hours)
            'atmosphere': 'wet_oxygen',  # Wet oxidation for faster growth
            'pressure': 1.0
        }
        
        print(f"Parameters: {sti_params['target_thickness']} nm STI oxide at {sti_params['temperature']-273.15:.0f}°C")
        
        # Generate process animation to show STI growth evolution
        anim_result = oxidation_manager.generate_process_animation(sti_params, num_frames=15)
        
        if anim_result['success']:
            frames = anim_result['frames']
            print(f"✅ STI oxidation animation generated")
            print(f"   - Animation frames: {len(frames)}")
            print(f"   - Process evolution: {frames[0]['description']} → {frames[-1]['description']}")
            
            # Analyze thickness evolution
            initial_thickness = np.mean(frames[0]['thickness_map'])
            final_thickness = np.mean(frames[-1]['thickness_map'])
            print(f"   - Thickness evolution: {initial_thickness:.1f} → {final_thickness:.1f} nm")
            
            # Generate stress distribution analysis
            viz_result = oxidation_manager.generate_3d_visualization(sti_params, resolution_x=50, resolution_y=50)
            if viz_result['success']:
                viz_data = viz_result['visualization_data']
                print(f"✅ STI stress analysis completed")
                print(f"   - Peak stress: {viz_data['max_stress']:.1f} MPa")
                print(f"   - Stress gradient: {viz_data['max_stress'] - viz_data['min_stress']:.1f} MPa")
        else:
            print(f"❌ STI animation failed: {anim_result.get('error', 'Unknown')}")
        
        return anim_result['success'] if anim_result else False
        
    except Exception as e:
        print(f"❌ STI demo error: {e}")
        traceback.print_exc()
        return False

def demo_power_device_oxide_3d():
    """Demo 3D visualization for power device gate oxide"""
    print("\n⚡ Demo: Power Device Gate Oxide 3D Visualization")
    print("-" * 50)
    
    try:
        from oxidation import OxidationManager
        from geometry import GeometryManager
        
        # Initialize managers
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager(wafer_diameter=200.0, wafer_thickness=0.675)  # Thicker wafer for power

        # Get power device wafer
        wafer = geometry_manager.wafer
        oxidation_manager.set_wafer(wafer)
        
        # Power device gate oxide parameters (medium thickness, high quality)
        power_params = {
            'target_thickness': 50.0,    # nm - medium thickness for power devices
            'temperature': 1273.15,      # K (1000°C) - standard temperature
            'time': 7200,                # seconds (2 hours)
            'atmosphere': 'dry_oxygen',  # High quality dry oxidation
            'pressure': 1.0
        }
        
        print(f"Parameters: {power_params['target_thickness']} nm power oxide at {power_params['temperature']-273.15:.0f}°C")
        
        # Generate comprehensive 3D analysis
        viz_result = oxidation_manager.generate_3d_visualization(power_params, resolution_x=70, resolution_y=70)
        
        if viz_result['success']:
            viz_data = viz_result['visualization_data']
            print(f"✅ Power device 3D visualization generated")
            
            # Analyze uniformity (critical for power devices)
            thickness_map = np.array(viz_data['thickness_map'])
            thickness_std = np.std(thickness_map[thickness_map > 0])
            thickness_mean = np.mean(thickness_map[thickness_map > 0])
            uniformity = (thickness_std / thickness_mean) * 100
            
            print(f"   - Thickness uniformity: {uniformity:.2f}% variation")
            print(f"   - Mean thickness: {thickness_mean:.2f} nm")
            print(f"   - Thickness range: {viz_data['min_thickness']:.2f} - {viz_data['max_thickness']:.2f} nm")
            
            # Analyze stress distribution (critical for reliability)
            stress_map = np.array(viz_data['stress_map'])
            max_stress = np.max(stress_map[stress_map > 0])
            print(f"   - Maximum stress: {max_stress:.1f} MPa")
            
            # Generate cross-sections for detailed analysis
            for direction in ['x', 'y']:
                cross_result = oxidation_manager.generate_cross_section_view(power_params, direction, 0.5)
                if cross_result['success']:
                    print(f"✅ {direction.upper()}-direction cross-section generated")
        else:
            print(f"❌ Power device visualization failed: {viz_result.get('error', 'Unknown')}")
        
        return viz_result['success'] if viz_result else False
        
    except Exception as e:
        print(f"❌ Power device demo error: {e}")
        traceback.print_exc()
        return False

def demo_advanced_3d_features():
    """Demo advanced 3D visualization features"""
    print("\n🚀 Demo: Advanced 3D Visualization Features")
    print("-" * 50)
    
    try:
        from oxidation import OxidationManager
        from geometry import GeometryManager
        
        # Initialize managers
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager(wafer_diameter=200.0, wafer_thickness=0.5)

        # Get wafer
        wafer = geometry_manager.wafer
        oxidation_manager.set_wafer(wafer)
        
        # Advanced oxidation parameters
        advanced_params = {
            'target_thickness': 100.0,   # nm
            'temperature': 1323.15,      # K (1050°C)
            'time': 10800,               # seconds (3 hours)
            'atmosphere': 'steam',       # Steam oxidation
            'pressure': 1.0
        }
        
        print(f"Parameters: {advanced_params['target_thickness']} nm steam oxide")
        
        # Test high-resolution visualization
        print("\n📊 High-resolution 3D visualization...")
        high_res_result = oxidation_manager.generate_3d_visualization(advanced_params, resolution_x=80, resolution_y=80)
        
        if high_res_result['success']:
            viz_data = high_res_result['visualization_data']
            print(f"✅ High-resolution visualization generated")
            print(f"   - Grid points: {len(viz_data['vertices_x'])} vertices")
            print(f"   - Mesh triangles: {len(viz_data['faces']) // 3}")
            print(f"   - Color mapping: {len(viz_data['vertex_colors'])} color values")
        
        # Test detailed process animation
        print("\n🎬 Detailed process animation...")
        detailed_anim_result = oxidation_manager.generate_process_animation(advanced_params, num_frames=25)
        
        if detailed_anim_result['success']:
            frames = detailed_anim_result['frames']
            print(f"✅ Detailed animation generated")
            print(f"   - Animation frames: {len(frames)}")
            print(f"   - Time resolution: {frames[1]['time'] - frames[0]['time']:.1f}s per frame")
            
            # Analyze growth kinetics from animation
            growth_rates = []
            for i in range(1, len(frames)):
                dt = frames[i]['time'] - frames[i-1]['time']
                thickness_prev = np.mean(frames[i-1]['thickness_map'])
                thickness_curr = np.mean(frames[i]['thickness_map'])
                growth_rate = (thickness_curr - thickness_prev) / dt * 3600  # nm/h
                growth_rates.append(growth_rate)
            
            print(f"   - Growth rate evolution: {min(growth_rates):.2f} - {max(growth_rates):.2f} nm/h")
        
        # Test multiple cross-sections
        print("\n✂️ Multiple cross-sectional analysis...")
        cross_positions = [0.2, 0.5, 0.8]
        for pos in cross_positions:
            cross_result = oxidation_manager.generate_cross_section_view(advanced_params, 'x', pos)
            if cross_result['success']:
                cross_data = cross_result  # Data is directly in the result
                vertices_z = cross_data.get('vertices_z', [])
                if vertices_z:
                    avg_thickness = np.mean(vertices_z)
                    print(f"✅ Cross-section at position {pos:.1f}: avg thickness {avg_thickness:.2f} nm")
        
        print("\n✅ All advanced 3D features demonstrated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Advanced features demo error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all 3D visualization demos"""
    print("🎯 3D Oxidation Visualization Industrial Demos")
    print("=" * 60)
    
    # Run demos
    gate_success = demo_gate_oxide_3d_visualization()
    sti_success = demo_sti_oxidation_3d_visualization()
    power_success = demo_power_device_oxide_3d()
    advanced_success = demo_advanced_3d_features()
    
    print("\n" + "=" * 60)
    print("📊 Demo Results Summary:")
    print(f"   Gate Oxide 3D Demo: {'✅ SUCCESS' if gate_success else '❌ FAILED'}")
    print(f"   STI Oxidation 3D Demo: {'✅ SUCCESS' if sti_success else '❌ FAILED'}")
    print(f"   Power Device 3D Demo: {'✅ SUCCESS' if power_success else '❌ FAILED'}")
    print(f"   Advanced Features Demo: {'✅ SUCCESS' if advanced_success else '❌ FAILED'}")
    
    overall_success = gate_success and sti_success and power_success and advanced_success
    print(f"\n🎯 Overall Demo Result: {'✅ ALL DEMOS SUCCESSFUL' if overall_success else '❌ SOME DEMOS FAILED'}")
    
    if overall_success:
        print("\n🎉 3D Oxidation Visualization Demos Complete!")
        print("\n📋 Industrial Applications Demonstrated:")
        print("   🔬 Gate Oxide Formation (2.5 nm ultra-thin)")
        print("   🏗️ STI Oxidation (400 nm isolation)")
        print("   ⚡ Power Device Gate Oxide (50 nm medium thickness)")
        print("   🚀 Advanced Steam Oxidation (100 nm)")
        print("\n🎨 3D Visualization Features Demonstrated:")
        print("   📊 3D Surface Visualization")
        print("   ✂️ Cross-Sectional Analysis")
        print("   🎬 Process Animation")
        print("   📈 Stress Distribution Mapping")
        print("   🔍 Interface Quality Analysis")
        print("   📐 High-Resolution Mesh Generation")
        print("   🎯 Multi-Position Cross-Sections")
        
        print(f"\n💾 Demo completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
