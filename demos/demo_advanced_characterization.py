#!/usr/bin/env python3
"""
Comprehensive demo of advanced oxidation characterization functionality
Showcases C-V analysis, Dit calculation, breakdown prediction, and reliability analysis
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def demo_characterization_analysis():
    """Demonstrate advanced characterization analysis capabilities"""
    print("🔬 SemiPRO Advanced Oxidation Characterization Demo")
    print("=" * 60)
    
    try:
        from oxidation import OxidationManager, OxidationCharacterizationAnalyzer
        from geometry import GeometryManager
        
        # Initialize systems
        print("\n📋 Initializing characterization systems...")
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager()
        analyzer = OxidationCharacterizationAnalyzer(oxidation_manager)
        
        # Get wafer and initialize grid
        wafer = geometry_manager.wafer
        geometry_manager.initialize_grid(100, 100)
        
        print("✓ Systems initialized successfully")
        
        # Demo different oxide thicknesses
        oxide_thicknesses = [5.0, 10.0, 20.0, 50.0]  # nm
        
        print(f"\n🧪 Testing {len(oxide_thicknesses)} different oxide thicknesses...")
        
        for i, thickness in enumerate(oxide_thicknesses):
            print(f"\n{'='*50}")
            print(f"OXIDE CHARACTERIZATION #{i+1}: {thickness} nm")
            print(f"{'='*50}")
            
            # 1. C-V Analysis
            print(f"\n📊 C-V Analysis for {thickness} nm oxide:")
            cv_results = analyzer.perform_cv_analysis(
                thickness=thickness,
                area=1e-4,
                temperature=300.0,
                voltage_range=(-5.0, 5.0),
                num_points=50
            )
            
            print(f"  • Oxide Capacitance: {cv_results['oxide_capacitance']:.2e} F/cm²")
            print(f"  • Flat Band Voltage: {cv_results['flat_band_voltage']:.3f} V")
            print(f"  • Capacitance Range: {min(cv_results['capacitance']):.2e} to {max(cv_results['capacitance']):.2e} F/cm²")
            
            # 2. Interface State Density
            print(f"\n🔍 Interface State Density Analysis:")
            dit = analyzer.calculate_interface_state_density(thickness, 300.0, 1e6)
            quality_grade = 'Excellent' if dit < 1e11 else 'Good' if dit < 5e11 else 'Fair'
            
            print(f"  • Dit: {dit:.2e} states/cm²/eV")
            print(f"  • Quality Grade: {quality_grade}")
            
            # 3. Breakdown Voltage Prediction
            print(f"\n⚡ Breakdown Voltage Prediction:")
            breakdown_voltage = analyzer.predict_breakdown_voltage(thickness, dit, 300.0, 1e-4)
            breakdown_field = breakdown_voltage / (thickness * 1e-7)
            
            print(f"  • Breakdown Voltage: {breakdown_voltage:.2f} V")
            print(f"  • Breakdown Field: {breakdown_field:.2e} V/cm")
            print(f"  • Safety Margin (80%): {breakdown_voltage * 0.8:.2f} V")
            
            # 4. Reliability Analysis
            print(f"\n🕒 TDDB Reliability Analysis:")
            stress_voltage = breakdown_voltage * 0.8
            reliability_results = analyzer.perform_reliability_analysis(
                thickness, stress_voltage, 125.0, 1e6)
            
            ttb_years = reliability_results['time_to_breakdown'] / 3.15e7
            print(f"  • Time to Breakdown: {reliability_results['time_to_breakdown']:.2e} seconds")
            print(f"  • Time to Breakdown: {ttb_years:.2e} years")
            print(f"  • Stress Voltage: {stress_voltage:.2f} V")
            print(f"  • Stress Temperature: 125°C")
            
            # 5. Comprehensive Analysis Summary
            print(f"\n📈 Comprehensive Analysis:")
            process_params = {
                'target_thickness': thickness,
                'temperature': 1000.0,
                'time': 2.0,
                'pressure': 760.0
            }
            
            comprehensive_results = analyzer.perform_comprehensive_characterization(wafer, process_params)
            
            print(f"  • Final Thickness: {comprehensive_results['final_thickness']:.2f} nm")
            print(f"  • Uniformity: {comprehensive_results['uniformity']:.1f}%")
            print(f"  • Interface Roughness: {comprehensive_results['interface_roughness']:.2f} nm")
            print(f"  • Electrical Quality: {comprehensive_results['electrical_quality']:.2e} states/cm²/eV")
            print(f"  • Leakage Current: {comprehensive_results['leakage_current']:.2e} A/cm²")
            print(f"  • Reliability Factor: {comprehensive_results['reliability_factor']:.3f}")
        
        # Summary comparison
        print(f"\n{'='*60}")
        print("📊 CHARACTERIZATION COMPARISON SUMMARY")
        print(f"{'='*60}")
        
        print(f"{'Thickness (nm)':<15} {'Dit (states/cm²/eV)':<20} {'VBD (V)':<10} {'TTB (years)':<15}")
        print("-" * 60)
        
        for thickness in oxide_thicknesses:
            dit = analyzer.calculate_interface_state_density(thickness, 300.0, 1e6)
            vbd = analyzer.predict_breakdown_voltage(thickness, dit, 300.0, 1e-4)
            reliability = analyzer.perform_reliability_analysis(thickness, vbd * 0.8, 125.0, 1e6)
            ttb_years = reliability['time_to_breakdown'] / 3.15e7
            
            print(f"{thickness:<15.1f} {dit:<20.2e} {vbd:<10.2f} {ttb_years:<15.2e}")
        
        print(f"\n🎉 Advanced characterization demo completed successfully!")
        print(f"✓ C-V analysis with MOS capacitor physics")
        print(f"✓ Interface state density calculation with energy distribution")
        print(f"✓ Breakdown voltage prediction with quality factors")
        print(f"✓ TDDB reliability analysis with Weibull modeling")
        print(f"✓ Comprehensive characterization with all metrics")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error in characterization demo: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_industrial_applications():
    """Demonstrate characterization for different industrial applications"""
    print(f"\n{'='*60}")
    print("🏭 INDUSTRIAL APPLICATION CHARACTERIZATION")
    print(f"{'='*60}")
    
    try:
        from oxidation import OxidationCharacterizationAnalyzer, OxidationManager
        
        analyzer = OxidationCharacterizationAnalyzer(OxidationManager())
        
        # Industrial applications with typical oxide thicknesses
        applications = {
            "Gate Oxide (90nm CMOS)": {"thickness": 4.0, "critical_param": "leakage"},
            "Flash Memory Tunnel Oxide": {"thickness": 2.5, "critical_param": "tunneling"},
            "DRAM Capacitor Oxide": {"thickness": 1.5, "critical_param": "capacitance"},
            "STI Liner Oxide": {"thickness": 30.0, "critical_param": "stress"},
            "Power Device Gate Oxide": {"thickness": 100.0, "critical_param": "breakdown"},
            "MEMS Sacrificial Oxide": {"thickness": 500.0, "critical_param": "etch_rate"}
        }
        
        print(f"\n📋 Characterizing {len(applications)} industrial applications:")
        
        for app_name, params in applications.items():
            thickness = params["thickness"]
            critical = params["critical_param"]
            
            print(f"\n🔧 {app_name}:")
            print(f"   Thickness: {thickness} nm")
            print(f"   Critical Parameter: {critical}")
            
            # Quick characterization
            dit = analyzer.calculate_interface_state_density(thickness, 300.0, 1e6)
            vbd = analyzer.predict_breakdown_voltage(thickness, dit, 300.0, 1e-4)
            
            print(f"   Dit: {dit:.2e} states/cm²/eV")
            print(f"   Breakdown Voltage: {vbd:.2f} V")
            print(f"   Quality: {'✓ Excellent' if dit < 1e11 else '○ Good' if dit < 5e11 else '△ Fair'}")
        
        print(f"\n✅ Industrial application characterization completed!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error in industrial demo: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main demo function"""
    print("🚀 SemiPRO Advanced Oxidation Characterization Demo")
    print("=" * 60)
    print("This demo showcases the advanced characterization capabilities")
    print("including C-V analysis, Dit calculation, breakdown prediction,")
    print("and TDDB reliability analysis for semiconductor oxides.")
    print("=" * 60)
    
    # Run characterization analysis demo
    analysis_success = demo_characterization_analysis()
    
    # Run industrial applications demo
    industrial_success = demo_industrial_applications()
    
    # Final summary
    print(f"\n{'='*60}")
    print("🎯 DEMO SUMMARY")
    print(f"{'='*60}")
    print(f"Characterization Analysis: {'✅ SUCCESS' if analysis_success else '❌ FAILED'}")
    print(f"Industrial Applications: {'✅ SUCCESS' if industrial_success else '❌ FAILED'}")
    
    if analysis_success and industrial_success:
        print(f"\n🎉 ALL DEMOS COMPLETED SUCCESSFULLY!")
        print(f"\n📚 What was demonstrated:")
        print(f"  ✓ C-V analysis with MOS capacitor physics")
        print(f"  ✓ Interface state density (Dit) calculation")
        print(f"  ✓ Breakdown voltage prediction with quality factors")
        print(f"  ✓ Time-dependent dielectric breakdown (TDDB) analysis")
        print(f"  ✓ Comprehensive characterization with all metrics")
        print(f"  ✓ Industrial application examples")
        print(f"  ✓ Fallback implementation for robustness")
        print(f"\n🖥️  GUI Integration:")
        print(f"  ✓ Advanced Characterization tab in oxidation panel")
        print(f"  ✓ Interactive parameter controls")
        print(f"  ✓ Real-time visualization with matplotlib")
        print(f"  ✓ Comprehensive results display")
        
        print(f"\n🚀 Ready for next phase: 3D Visualization and Equipment Modeling!")
        return 0
    else:
        print(f"\n❌ Some demos failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
