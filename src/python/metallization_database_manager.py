#!/usr/bin/env python3
"""
Metallization Database Manager
==============================

Comprehensive database manager for metallization module with PostgreSQL integration,
process tracking, parameter storage, and result analysis capabilities.

Author: Enhanced SemiPRO Development Team
"""

import logging
import json
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import psycopg2
from psycopg2.extras import RealDictCursor, Json
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MetallizationProcess:
    """Data class for metallization process records"""
    process_id: str
    wafer_id: str
    process_name: str
    process_type: str
    metal_symbol: str
    target_thickness_nm: float
    actual_thickness_nm: Optional[float] = None
    temperature_c: Optional[float] = None
    pressure_torr: Optional[float] = None
    power_w: Optional[float] = None
    equipment_type: Optional[str] = None
    uniformity_percent: Optional[float] = None
    step_coverage_percent: Optional[float] = None
    quality_score: Optional[float] = None
    process_parameters: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None

@dataclass
class MetalProperties:
    """Data class for metal properties"""
    symbol: str
    name: str
    atomic_mass_amu: float
    density_g_cm3: float
    melting_point_c: float
    resistivity_uohm_cm: float
    thermal_conductivity_w_m_k: float
    work_function_ev: float
    young_modulus_gpa: float
    poisson_ratio: float

@dataclass
class EquipmentRecord:
    """Data class for equipment records"""
    equipment_id: str
    equipment_name: str
    manufacturer: str
    model: str
    equipment_type: str
    supported_techniques: List[str]
    max_temperature_c: float
    is_active: bool = True

class MetallizationDatabaseManager:
    """Comprehensive database manager for metallization module"""
    
    def __init__(self, connection_params: Dict[str, str]):
        """
        Initialize database manager
        
        Args:
            connection_params: Database connection parameters
                - host: Database host
                - port: Database port
                - database: Database name
                - user: Database user
                - password: Database password
        """
        self.connection_params = connection_params
        self.connection = None
        self._connect()
        
    def _connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.connection_params)
            self.connection.autocommit = False
            logger.info("Connected to metallization database successfully")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def _ensure_connection(self):
        """Ensure database connection is active"""
        if self.connection is None or self.connection.closed:
            self._connect()
    
    def initialize_schema(self, schema_file: str = None):
        """Initialize database schema from SQL file"""
        if schema_file is None:
            schema_file = "src/sql/metallization_schema.sql"
        
        try:
            with open(schema_file, 'r') as f:
                schema_sql = f.read()
            
            with self.connection.cursor() as cursor:
                cursor.execute(schema_sql)
                self.connection.commit()
                
            logger.info("Metallization database schema initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize schema: {e}")
            self.connection.rollback()
            raise
    
    def store_process(self, process: MetallizationProcess) -> str:
        """
        Store metallization process in database
        
        Args:
            process: MetallizationProcess object
            
        Returns:
            Process ID
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor() as cursor:
                # Generate UUID if not provided
                if not process.process_id:
                    process.process_id = str(uuid.uuid4())
                
                insert_sql = """
                INSERT INTO metallization_processes (
                    process_id, wafer_id, process_name, process_type, metal_symbol,
                    target_thickness_nm, actual_thickness_nm, temperature_c, pressure_torr,
                    power_w, equipment_type, uniformity_percent, step_coverage_percent,
                    quality_score, process_parameters
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
                """
                
                cursor.execute(insert_sql, (
                    process.process_id, process.wafer_id, process.process_name,
                    process.process_type, process.metal_symbol, process.target_thickness_nm,
                    process.actual_thickness_nm, process.temperature_c, process.pressure_torr,
                    process.power_w, process.equipment_type, process.uniformity_percent,
                    process.step_coverage_percent, process.quality_score,
                    Json(process.process_parameters) if process.process_parameters else None
                ))
                
                self.connection.commit()
                logger.info(f"Stored metallization process: {process.process_id}")
                return process.process_id
                
        except Exception as e:
            logger.error(f"Failed to store process: {e}")
            self.connection.rollback()
            raise
    
    def get_process(self, process_id: str) -> Optional[MetallizationProcess]:
        """
        Retrieve metallization process by ID
        
        Args:
            process_id: Process ID
            
        Returns:
            MetallizationProcess object or None
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(
                    "SELECT * FROM metallization_processes WHERE process_id = %s",
                    (process_id,)
                )
                
                row = cursor.fetchone()
                if row:
                    return MetallizationProcess(**dict(row))
                return None
                
        except Exception as e:
            logger.error(f"Failed to retrieve process {process_id}: {e}")
            raise
    
    def get_processes_by_wafer(self, wafer_id: str) -> List[MetallizationProcess]:
        """
        Get all metallization processes for a wafer
        
        Args:
            wafer_id: Wafer ID
            
        Returns:
            List of MetallizationProcess objects
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(
                    "SELECT * FROM metallization_processes WHERE wafer_id = %s ORDER BY created_at",
                    (wafer_id,)
                )
                
                rows = cursor.fetchall()
                return [MetallizationProcess(**dict(row)) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to retrieve processes for wafer {wafer_id}: {e}")
            raise
    
    def store_metal_properties(self, metal: MetalProperties) -> str:
        """
        Store metal properties in database
        
        Args:
            metal: MetalProperties object
            
        Returns:
            Metal ID
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor() as cursor:
                metal_id = str(uuid.uuid4())
                
                insert_sql = """
                INSERT INTO metal_properties (
                    metal_id, symbol, name, atomic_mass_amu, density_g_cm3,
                    melting_point_c, resistivity_uohm_cm, thermal_conductivity_w_m_k,
                    work_function_ev, young_modulus_gpa, poisson_ratio
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
                ON CONFLICT (symbol) DO UPDATE SET
                    name = EXCLUDED.name,
                    atomic_mass_amu = EXCLUDED.atomic_mass_amu,
                    density_g_cm3 = EXCLUDED.density_g_cm3,
                    melting_point_c = EXCLUDED.melting_point_c,
                    resistivity_uohm_cm = EXCLUDED.resistivity_uohm_cm,
                    thermal_conductivity_w_m_k = EXCLUDED.thermal_conductivity_w_m_k,
                    work_function_ev = EXCLUDED.work_function_ev,
                    young_modulus_gpa = EXCLUDED.young_modulus_gpa,
                    poisson_ratio = EXCLUDED.poisson_ratio,
                    updated_at = CURRENT_TIMESTAMP
                """
                
                cursor.execute(insert_sql, (
                    metal_id, metal.symbol, metal.name, metal.atomic_mass_amu,
                    metal.density_g_cm3, metal.melting_point_c, metal.resistivity_uohm_cm,
                    metal.thermal_conductivity_w_m_k, metal.work_function_ev,
                    metal.young_modulus_gpa, metal.poisson_ratio
                ))
                
                self.connection.commit()
                logger.info(f"Stored metal properties for: {metal.symbol}")
                return metal_id
                
        except Exception as e:
            logger.error(f"Failed to store metal properties: {e}")
            self.connection.rollback()
            raise
    
    def get_metal_properties(self, symbol: str) -> Optional[MetalProperties]:
        """
        Get metal properties by symbol
        
        Args:
            symbol: Metal symbol (e.g., 'Cu', 'Al')
            
        Returns:
            MetalProperties object or None
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(
                    "SELECT * FROM metal_properties WHERE symbol = %s",
                    (symbol,)
                )
                
                row = cursor.fetchone()
                if row:
                    # Remove database-specific fields
                    metal_data = dict(row)
                    metal_data.pop('metal_id', None)
                    metal_data.pop('additional_properties', None)
                    metal_data.pop('data_source', None)
                    metal_data.pop('reference_temperature_c', None)
                    metal_data.pop('created_at', None)
                    metal_data.pop('updated_at', None)
                    
                    return MetalProperties(**metal_data)
                return None
                
        except Exception as e:
            logger.error(f"Failed to retrieve metal properties for {symbol}: {e}")
            raise
    
    def get_available_metals(self) -> List[str]:
        """
        Get list of available metals
        
        Returns:
            List of metal symbols
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT symbol FROM metal_properties ORDER BY symbol")
                rows = cursor.fetchall()
                return [row[0] for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to retrieve available metals: {e}")
            raise
    
    def store_equipment(self, equipment: EquipmentRecord) -> str:
        """
        Store equipment record in database
        
        Args:
            equipment: EquipmentRecord object
            
        Returns:
            Equipment ID
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor() as cursor:
                if not equipment.equipment_id:
                    equipment.equipment_id = str(uuid.uuid4())
                
                insert_sql = """
                INSERT INTO metallization_equipment (
                    equipment_id, equipment_name, manufacturer, model, equipment_type,
                    supported_techniques, max_temperature_c, is_active
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s
                )
                """
                
                cursor.execute(insert_sql, (
                    equipment.equipment_id, equipment.equipment_name, equipment.manufacturer,
                    equipment.model, equipment.equipment_type, equipment.supported_techniques,
                    equipment.max_temperature_c, equipment.is_active
                ))
                
                self.connection.commit()
                logger.info(f"Stored equipment: {equipment.equipment_name}")
                return equipment.equipment_id
                
        except Exception as e:
            logger.error(f"Failed to store equipment: {e}")
            self.connection.rollback()
            raise
    
    def get_process_statistics(self, metal: str = None, process_type: str = None) -> Dict[str, Any]:
        """
        Get process statistics with optional filtering
        
        Args:
            metal: Optional metal filter
            process_type: Optional process type filter
            
        Returns:
            Dictionary with statistics
        """
        self._ensure_connection()
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                where_conditions = []
                params = []
                
                if metal:
                    where_conditions.append("metal_symbol = %s")
                    params.append(metal)
                
                if process_type:
                    where_conditions.append("process_type = %s")
                    params.append(process_type)
                
                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)
                
                stats_sql = f"""
                SELECT 
                    COUNT(*) as total_processes,
                    AVG(actual_thickness_nm) as avg_thickness,
                    AVG(uniformity_percent) as avg_uniformity,
                    AVG(step_coverage_percent) as avg_step_coverage,
                    AVG(quality_score) as avg_quality_score,
                    MIN(created_at) as first_process,
                    MAX(created_at) as last_process
                FROM metallization_processes
                {where_clause}
                """
                
                cursor.execute(stats_sql, params)
                stats = cursor.fetchone()
                
                return dict(stats) if stats else {}
                
        except Exception as e:
            logger.error(f"Failed to get process statistics: {e}")
            raise
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")
