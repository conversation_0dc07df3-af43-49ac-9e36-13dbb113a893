"""
Enhanced Thermal Panel
=====================

Advanced thermal analysis panel with real-time visualization, 3D temperature mapping,
industrial applications, and thermal optimization capabilities.

Author: Dr<PERSON> <PERSON><PERSON>
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
                              QLineEdit, QPushButton, QLabel, QCheckBox, QComboBox,
                              QTabWidget, QTextEdit, QProgressBar, QSpinBox,
                              QDoubleSpinBox, QGroupBox, QSlider, QSplitter,
                              QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Signal, QTimer, Q<PERSON>hread, Qt
from PySide6.QtGui import QFont, QPalette, QColor

from ..simulator import Simulator
from ..semipro_packaging.enhanced_thermal_analysis import (
    EnhancedThermalAnalysisEngine, ThermalApplicationType, 
    ThermalOptimizationConfig, ThermalMonitoringConfig
)
from ..semipro_packaging.thermal_optimization import (
    ThermalOptimizationManager, OptimizationMethod,
    DesignVariable, OptimizationObjective
)

# Import thermal bridge for device creation
try:
    from ..enhanced_thermal_bridge import EnhancedThermalBridge
    THERMAL_BRIDGE_AVAILABLE = True
except ImportError:
    THERMAL_BRIDGE_AVAILABLE = False

class ThermalSimulationWorker(QThread):
    """Worker thread for thermal simulation"""
    simulation_finished = Signal(dict)
    progress_updated = Signal(int)
    device_created = Signal(object)  # ThermalDeviceStructure
    
    def __init__(self, thermal_engine, application_type, specs):
        super().__init__()
        self.thermal_engine = thermal_engine
        self.application_type = application_type
        self.specs = specs
    
    def run(self):
        """Run thermal simulation in background thread"""
        try:
            self.progress_updated.emit(25)
            results = self.thermal_engine.analyze_thermal_performance(
                self.application_type, self.specs
            )
            self.progress_updated.emit(75)

            # Create device structure if bridge is available
            if THERMAL_BRIDGE_AVAILABLE:
                try:
                    bridge = EnhancedThermalBridge()

                    # Map application type to device name
                    device_name_map = {
                        ThermalApplicationType.CPU_THERMAL_MANAGEMENT: 'cpu_thermal_device',
                        ThermalApplicationType.POWER_ELECTRONICS_COOLING: 'power_electronics_device',
                        ThermalApplicationType.LED_THERMAL_DESIGN: 'led_thermal_device',
                        ThermalApplicationType.AUTOMOTIVE_ELECTRONICS: 'automotive_thermal_device',
                        ThermalApplicationType.DATA_CENTER_COOLING: 'datacenter_thermal_device',
                        ThermalApplicationType.AEROSPACE_THERMAL: 'aerospace_thermal_device',
                        ThermalApplicationType.RENEWABLE_ENERGY: 'renewable_energy_device'
                    }

                    if self.application_type in device_name_map:
                        device_name = device_name_map[self.application_type]
                        device_structure = bridge.create_device_from_thermal_results(results, device_name)
                        self.device_created.emit(device_structure)

                except Exception as e:
                    print(f"Failed to create thermal device structure: {e}")

            self.progress_updated.emit(100)
            self.simulation_finished.emit(results)
        except Exception as e:
            self.simulation_finished.emit({"error": str(e)})

class ThermalVisualizationWidget(QWidget):
    """Advanced thermal visualization widget with 3D temperature mapping"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.temperature_data = None
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Create matplotlib figure with subplots
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Control panel
        control_layout = QHBoxLayout()
        
        self.view_combo = QComboBox()
        self.view_combo.addItems(["2D Temperature Map", "3D Temperature Surface", 
                                 "Temperature Contours", "Heat Flux Vectors"])
        self.view_combo.currentTextChanged.connect(self.update_visualization)
        
        self.colormap_combo = QComboBox()
        self.colormap_combo.addItems(["hot", "viridis", "plasma", "inferno", "coolwarm"])
        self.colormap_combo.currentTextChanged.connect(self.update_visualization)
        
        self.animate_button = QPushButton("Start Animation")
        self.animate_button.clicked.connect(self.toggle_animation)
        
        control_layout.addWidget(QLabel("View:"))
        control_layout.addWidget(self.view_combo)
        control_layout.addWidget(QLabel("Colormap:"))
        control_layout.addWidget(self.colormap_combo)
        control_layout.addWidget(self.animate_button)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
    
    def update_temperature_data(self, temperature_field, hotspots=None):
        """Update temperature data and refresh visualization"""
        self.temperature_data = temperature_field
        self.hotspots = hotspots or []
        self.update_visualization()
    
    def update_visualization(self):
        """Update the thermal visualization"""
        if self.temperature_data is None:
            return
        
        self.figure.clear()
        view_type = self.view_combo.currentText()
        colormap = self.colormap_combo.currentText()
        
        if view_type == "2D Temperature Map":
            self._plot_2d_temperature_map(colormap)
        elif view_type == "3D Temperature Surface":
            self._plot_3d_temperature_surface(colormap)
        elif view_type == "Temperature Contours":
            self._plot_temperature_contours(colormap)
        elif view_type == "Heat Flux Vectors":
            self._plot_heat_flux_vectors(colormap)
        
        self.canvas.draw()
    
    def _plot_2d_temperature_map(self, colormap):
        """Plot 2D temperature map"""
        ax = self.figure.add_subplot(111)
        
        im = ax.imshow(self.temperature_data, cmap=colormap, origin='lower')
        ax.set_title('Temperature Distribution (K)')
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        
        # Add colorbar
        cbar = self.figure.colorbar(im, ax=ax)
        cbar.set_label('Temperature (K)')
        
        # Mark hotspots
        for hotspot in self.hotspots:
            ax.plot(hotspot[1], hotspot[0], 'rx', markersize=10, markeredgewidth=2)
        
        # Add temperature statistics
        max_temp = np.max(self.temperature_data)
        min_temp = np.min(self.temperature_data)
        avg_temp = np.mean(self.temperature_data)
        
        stats_text = f'Max: {max_temp:.1f} K\nMin: {min_temp:.1f} K\nAvg: {avg_temp:.1f} K'
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def _plot_3d_temperature_surface(self, colormap):
        """Plot 3D temperature surface"""
        ax = self.figure.add_subplot(111, projection='3d')
        
        rows, cols = self.temperature_data.shape
        x = np.arange(cols)
        y = np.arange(rows)
        X, Y = np.meshgrid(x, y)
        
        surf = ax.plot_surface(X, Y, self.temperature_data, cmap=colormap, alpha=0.8)
        
        ax.set_title('3D Temperature Surface')
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        ax.set_zlabel('Temperature (K)')
        
        # Add colorbar
        self.figure.colorbar(surf, ax=ax, shrink=0.5)
    
    def _plot_temperature_contours(self, colormap):
        """Plot temperature contours"""
        ax = self.figure.add_subplot(111)
        
        rows, cols = self.temperature_data.shape
        x = np.arange(cols)
        y = np.arange(rows)
        X, Y = np.meshgrid(x, y)
        
        contours = ax.contour(X, Y, self.temperature_data, levels=15, colors='black', alpha=0.6)
        contourf = ax.contourf(X, Y, self.temperature_data, levels=15, cmap=colormap)
        
        ax.clabel(contours, inline=True, fontsize=8)
        ax.set_title('Temperature Contours (K)')
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        
        # Add colorbar
        cbar = self.figure.colorbar(contourf, ax=ax)
        cbar.set_label('Temperature (K)')
    
    def _plot_heat_flux_vectors(self, colormap):
        """Plot heat flux vectors"""
        ax = self.figure.add_subplot(111)
        
        # Calculate heat flux (simplified gradient)
        grad_y, grad_x = np.gradient(self.temperature_data)
        
        rows, cols = self.temperature_data.shape
        x = np.arange(0, cols, 5)  # Subsample for clarity
        y = np.arange(0, rows, 5)
        X, Y = np.meshgrid(x, y)
        
        U = -grad_x[::5, ::5]  # Heat flux opposite to temperature gradient
        V = -grad_y[::5, ::5]
        
        # Background temperature map
        im = ax.imshow(self.temperature_data, cmap=colormap, alpha=0.7, origin='lower')
        
        # Heat flux vectors
        ax.quiver(X, Y, U, V, scale=1000, color='white', alpha=0.8)
        
        ax.set_title('Heat Flux Vectors')
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        
        # Add colorbar
        cbar = self.figure.colorbar(im, ax=ax)
        cbar.set_label('Temperature (K)')
    
    def toggle_animation(self):
        """Toggle temperature animation"""
        if self.animation_timer.isActive():
            self.animation_timer.stop()
            self.animate_button.setText("Start Animation")
        else:
            self.animation_timer.start(100)  # Update every 100ms
            self.animate_button.setText("Stop Animation")
    
    def update_animation(self):
        """Update animation frame"""
        if self.temperature_data is not None:
            # Add some temporal variation for demonstration
            noise = np.random.normal(0, 0.5, self.temperature_data.shape)
            animated_data = self.temperature_data + noise
            
            # Update the plot
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            im = ax.imshow(animated_data, cmap=self.colormap_combo.currentText(), origin='lower')
            ax.set_title('Real-time Temperature Monitoring')
            
            self.canvas.draw()

class EnhancedThermalPanel(QWidget):
    """Enhanced thermal analysis panel with advanced features"""

    thermal_updated = Signal(object)
    device_created = Signal(object)  # ThermalDeviceStructure
    visualization_option_changed = Signal(bool)
    
    def __init__(self, enable_database: bool = True):
        super().__init__()
        self.simulator = Simulator()

        # Initialize enhanced thermal engine with database integration
        try:
            from ..semipro_packaging.enhanced_thermal_analysis import DatabaseIntegratedThermalEngine
            self.thermal_engine = DatabaseIntegratedThermalEngine(enable_database=enable_database)
            self.database_enabled = enable_database and self.thermal_engine.enable_database
        except ImportError:
            self.thermal_engine = EnhancedThermalAnalysisEngine()
            self.database_enabled = False

        self.optimization_manager = ThermalOptimizationManager()

        self.simulation_worker = None
        self.current_results = None
        self.current_device = None

        # Wafer management
        self.current_wafer = None

        # Database integration
        self.db_integration = None
        if self.database_enabled:
            try:
                from ..thermal.database_integration import ThermalDatabaseIntegration
                self.db_integration = ThermalDatabaseIntegration()
                print("Database integration enabled for thermal panel")
            except ImportError:
                self.database_enabled = False
                print("Database integration not available")

        # Real-time monitoring
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self.update_monitoring_display)
        self.monitoring_data = []

        # Industrial examples cache
        self.industrial_examples_cache = {}
        self.load_industrial_examples()

        self.setup_ui()
        self.setup_connections()

    def load_industrial_examples(self):
        """Load industrial thermal examples from database or defaults"""
        if self.database_enabled and self.db_integration:
            try:
                # Load from database
                for app_type in ['CPU_THERMAL_MANAGEMENT', 'POWER_ELECTRONICS_COOLING',
                               'LED_THERMAL_DESIGN', 'AUTOMOTIVE_ELECTRONICS', 'DATA_CENTER_COOLING']:
                    recipes = self.db_integration.get_recipes_by_application(app_type)
                    if recipes:
                        self.industrial_examples_cache[app_type] = recipes
            except Exception as e:
                print(f"Failed to load industrial examples from database: {e}")

        # Load default examples if database not available or empty
        if not self.industrial_examples_cache:
            self.industrial_examples_cache = {
                'CPU_THERMAL_MANAGEMENT': [
                    {
                        'name': 'High-Performance Desktop CPU',
                        'tdp': 125.0,
                        'die_size_mm2': 150.0,
                        'package_type': 'LGA',
                        'max_junction_temp': 100.0,
                        'cooling_solution': 'liquid_cooler'
                    },
                    {
                        'name': 'Mobile Processor',
                        'tdp': 15.0,
                        'die_size_mm2': 80.0,
                        'package_type': 'BGA',
                        'max_junction_temp': 105.0,
                        'cooling_solution': 'passive'
                    }
                ],
                'POWER_ELECTRONICS_COOLING': [
                    {
                        'name': 'Automotive IGBT Module',
                        'device_type': 'IGBT',
                        'current_rating_a': 50.0,
                        'voltage_rating_v': 600.0,
                        'switching_frequency_khz': 10.0,
                        'max_junction_temp': 175.0
                    },
                    {
                        'name': 'DC-DC Converter MOSFET',
                        'device_type': 'MOSFET',
                        'current_rating_a': 20.0,
                        'voltage_rating_v': 100.0,
                        'switching_frequency_khz': 100.0,
                        'max_junction_temp': 150.0
                    }
                ],
                'LED_THERMAL_DESIGN': [
                    {
                        'name': 'High-Power LED Array',
                        'forward_current_ma': 1000.0,
                        'forward_voltage_v': 3.2,
                        'luminous_efficacy_lm_w': 150.0,
                        'thermal_resistance_k_w': 8.0,
                        'max_junction_temp': 125.0
                    },
                    {
                        'name': 'Automotive Headlight LED',
                        'forward_current_ma': 350.0,
                        'forward_voltage_v': 3.0,
                        'luminous_efficacy_lm_w': 180.0,
                        'thermal_resistance_k_w': 12.0,
                        'max_junction_temp': 150.0
                    }
                ]
            }

    def set_wafer(self, wafer):
        """Set the current wafer for thermal simulation"""
        self.current_wafer = wafer
        print("Wafer set for enhanced thermal simulation")

    def get_wafer(self):
        """Get the current wafer after thermal simulation"""
        return self.current_wafer

    def get_results(self):
        """Get the current thermal simulation results"""
        return self.current_results
        
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create tab widget for different thermal analysis modes
        self.tab_widget = QTabWidget()
        
        # Basic thermal simulation tab
        self.basic_tab = self.create_basic_simulation_tab()
        self.tab_widget.addTab(self.basic_tab, "Basic Simulation")
        
        # Industrial applications tab
        self.industrial_tab = self.create_industrial_applications_tab()
        self.tab_widget.addTab(self.industrial_tab, "Industrial Applications")
        
        # Thermal optimization tab
        self.optimization_tab = self.create_optimization_tab()
        self.tab_widget.addTab(self.optimization_tab, "Thermal Optimization")
        
        # Real-time monitoring tab
        self.monitoring_tab = self.create_monitoring_tab()
        self.tab_widget.addTab(self.monitoring_tab, "Real-time Monitoring")

        # Database management tab (if enabled)
        if self.database_enabled:
            self.database_tab = self.create_database_management_tab()
            self.tab_widget.addTab(self.database_tab, "Database Management")

        # Parameter analytics tab
        self.analytics_tab = self.create_parameter_analytics_tab()
        self.tab_widget.addTab(self.analytics_tab, "Parameter Analytics")
        
        layout.addWidget(self.tab_widget)
        
        # Visualization panel
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.tab_widget)
        
        self.visualization_widget = ThermalVisualizationWidget()
        splitter.addWidget(self.visualization_widget)
        
        splitter.setSizes([400, 800])  # Give more space to visualization
        
        layout.addWidget(splitter)
    
    def create_basic_simulation_tab(self):
        """Create basic thermal simulation tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Input parameters group
        params_group = QGroupBox("Simulation Parameters")
        params_layout = QGridLayout(params_group)
        
        self.ambient_temp_input = QDoubleSpinBox()
        self.ambient_temp_input.setRange(200.0, 500.0)
        self.ambient_temp_input.setValue(298.15)
        self.ambient_temp_input.setSuffix(" K")
        
        self.current_input = QDoubleSpinBox()
        self.current_input.setRange(0.0, 10.0)
        self.current_input.setValue(0.001)
        self.current_input.setSuffix(" A")
        self.current_input.setDecimals(4)
        
        self.power_input = QDoubleSpinBox()
        self.power_input.setRange(0.0, 1000.0)
        self.power_input.setValue(1.0)
        self.power_input.setSuffix(" W")
        
        params_layout.addWidget(QLabel("Ambient Temperature:"), 0, 0)
        params_layout.addWidget(self.ambient_temp_input, 0, 1)
        params_layout.addWidget(QLabel("Current:"), 1, 0)
        params_layout.addWidget(self.current_input, 1, 1)
        params_layout.addWidget(QLabel("Power Dissipation:"), 2, 0)
        params_layout.addWidget(self.power_input, 2, 1)
        
        layout.addWidget(params_group)
        
        # Simulation controls
        controls_layout = QHBoxLayout()
        
        self.basic_simulate_button = QPushButton("Run Basic Simulation")
        self.basic_simulate_button.clicked.connect(self.run_basic_simulation)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        controls_layout.addWidget(self.basic_simulate_button)
        controls_layout.addWidget(self.progress_bar)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Results display
        results_group = QGroupBox("Simulation Results")
        results_layout = QGridLayout(results_group)
        
        self.max_temp_label = QLabel("--")
        self.min_temp_label = QLabel("--")
        self.thermal_resistance_label = QLabel("--")
        self.hotspots_label = QLabel("--")
        
        results_layout.addWidget(QLabel("Maximum Temperature:"), 0, 0)
        results_layout.addWidget(self.max_temp_label, 0, 1)
        results_layout.addWidget(QLabel("Minimum Temperature:"), 1, 0)
        results_layout.addWidget(self.min_temp_label, 1, 1)
        results_layout.addWidget(QLabel("Thermal Resistance:"), 2, 0)
        results_layout.addWidget(self.thermal_resistance_label, 2, 1)
        results_layout.addWidget(QLabel("Hotspots Detected:"), 3, 0)
        results_layout.addWidget(self.hotspots_label, 3, 1)
        
        layout.addWidget(results_group)
        
        # Error display
        self.error_label = QLabel("")
        self.error_label.setStyleSheet("color: red;")
        layout.addWidget(self.error_label)
        
        layout.addStretch()
        
        return widget
    
    def create_industrial_applications_tab(self):
        """Create industrial applications tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Application selection
        app_group = QGroupBox("Industrial Application")
        app_layout = QGridLayout(app_group)
        
        self.application_combo = QComboBox()
        for app_type in ThermalApplicationType:
            self.application_combo.addItem(app_type.value.replace('_', ' ').title(), app_type)
        
        app_layout.addWidget(QLabel("Application Type:"), 0, 0)
        app_layout.addWidget(self.application_combo, 0, 1)
        
        layout.addWidget(app_group)
        
        # Application-specific parameters
        self.app_params_group = QGroupBox("Application Parameters")
        self.app_params_layout = QGridLayout(self.app_params_group)
        layout.addWidget(self.app_params_group)
        
        # Update parameters when application changes
        self.application_combo.currentTextChanged.connect(self.update_application_parameters)
        self.update_application_parameters()
        
        # Simulation controls
        controls_layout = QHBoxLayout()
        
        self.industrial_simulate_button = QPushButton("Run Industrial Simulation")
        self.industrial_simulate_button.clicked.connect(self.run_industrial_simulation)
        
        controls_layout.addWidget(self.industrial_simulate_button)
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(2)
        self.results_table.setHorizontalHeaderLabels(["Parameter", "Value"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.results_table)
        
        return widget
    
    def create_optimization_tab(self):
        """Create thermal optimization tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Optimization method selection
        method_group = QGroupBox("Optimization Method")
        method_layout = QGridLayout(method_group)
        
        self.optimization_method_combo = QComboBox()
        for method in OptimizationMethod:
            self.optimization_method_combo.addItem(method.value.replace('_', ' ').title(), method)
        
        method_layout.addWidget(QLabel("Method:"), 0, 0)
        method_layout.addWidget(self.optimization_method_combo, 0, 1)
        
        layout.addWidget(method_group)
        
        # Optimization parameters
        opt_params_group = QGroupBox("Optimization Parameters")
        opt_params_layout = QGridLayout(opt_params_group)
        
        self.max_iterations_spin = QSpinBox()
        self.max_iterations_spin.setRange(10, 1000)
        self.max_iterations_spin.setValue(100)
        
        self.population_size_spin = QSpinBox()
        self.population_size_spin.setRange(10, 200)
        self.population_size_spin.setValue(50)
        
        opt_params_layout.addWidget(QLabel("Max Iterations:"), 0, 0)
        opt_params_layout.addWidget(self.max_iterations_spin, 0, 1)
        opt_params_layout.addWidget(QLabel("Population Size:"), 1, 0)
        opt_params_layout.addWidget(self.population_size_spin, 1, 1)
        
        layout.addWidget(opt_params_group)
        
        # Optimization controls
        opt_controls_layout = QHBoxLayout()
        
        self.optimize_button = QPushButton("Start Optimization")
        self.optimize_button.clicked.connect(self.run_optimization)
        
        opt_controls_layout.addWidget(self.optimize_button)
        opt_controls_layout.addStretch()
        
        layout.addLayout(opt_controls_layout)
        
        # Optimization results
        self.optimization_results_text = QTextEdit()
        self.optimization_results_text.setReadOnly(True)
        layout.addWidget(self.optimization_results_text)
        
        return widget
    
    def create_monitoring_tab(self):
        """Create real-time monitoring tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Monitoring controls
        controls_group = QGroupBox("Monitoring Controls")
        controls_layout = QGridLayout(controls_group)
        
        self.monitoring_interval_spin = QDoubleSpinBox()
        self.monitoring_interval_spin.setRange(0.1, 10.0)
        self.monitoring_interval_spin.setValue(1.0)
        self.monitoring_interval_spin.setSuffix(" s")
        
        self.temp_threshold_spin = QDoubleSpinBox()
        self.temp_threshold_spin.setRange(300.0, 500.0)
        self.temp_threshold_spin.setValue(373.15)
        self.temp_threshold_spin.setSuffix(" K")
        
        controls_layout.addWidget(QLabel("Update Interval:"), 0, 0)
        controls_layout.addWidget(self.monitoring_interval_spin, 0, 1)
        controls_layout.addWidget(QLabel("Temperature Threshold:"), 1, 0)
        controls_layout.addWidget(self.temp_threshold_spin, 1, 1)
        
        layout.addWidget(controls_group)
        
        # Monitoring controls
        monitoring_controls_layout = QHBoxLayout()
        
        self.start_monitoring_button = QPushButton("Start Monitoring")
        self.start_monitoring_button.clicked.connect(self.start_monitoring)
        
        self.stop_monitoring_button = QPushButton("Stop Monitoring")
        self.stop_monitoring_button.clicked.connect(self.stop_monitoring)
        self.stop_monitoring_button.setEnabled(False)
        
        monitoring_controls_layout.addWidget(self.start_monitoring_button)
        monitoring_controls_layout.addWidget(self.stop_monitoring_button)
        monitoring_controls_layout.addStretch()
        
        layout.addLayout(monitoring_controls_layout)
        
        # Monitoring log
        self.monitoring_log = QTextEdit()
        self.monitoring_log.setReadOnly(True)
        layout.addWidget(self.monitoring_log)
        
        return widget
    
    def setup_connections(self):
        """Setup signal connections"""
        pass
    
    def update_application_parameters(self):
        """Update application-specific parameters"""
        # Clear existing parameters
        for i in reversed(range(self.app_params_layout.count())):
            self.app_params_layout.itemAt(i).widget().setParent(None)
        
        app_type = self.application_combo.currentData()
        
        if app_type == ThermalApplicationType.CPU_THERMAL_MANAGEMENT:
            self.add_cpu_parameters()
        elif app_type == ThermalApplicationType.POWER_ELECTRONICS_COOLING:
            self.add_power_electronics_parameters()
        elif app_type == ThermalApplicationType.LED_THERMAL_DESIGN:
            self.add_led_parameters()
        elif app_type == ThermalApplicationType.AUTOMOTIVE_ELECTRONICS:
            self.add_automotive_parameters()
        elif app_type == ThermalApplicationType.DATA_CENTER_COOLING:
            self.add_data_center_parameters()
        elif app_type == ThermalApplicationType.AEROSPACE_THERMAL:
            self.add_aerospace_parameters()
        elif app_type == ThermalApplicationType.RENEWABLE_ENERGY:
            self.add_renewable_energy_parameters()
        else:
            self.add_generic_parameters()
    
    def add_cpu_parameters(self):
        """Add CPU-specific parameters"""
        self.cpu_tdp_spin = QDoubleSpinBox()
        self.cpu_tdp_spin.setRange(15.0, 300.0)
        self.cpu_tdp_spin.setValue(65.0)
        self.cpu_tdp_spin.setSuffix(" W")
        
        self.cpu_cores_spin = QSpinBox()
        self.cpu_cores_spin.setRange(1, 32)
        self.cpu_cores_spin.setValue(4)
        
        self.cpu_frequency_spin = QDoubleSpinBox()
        self.cpu_frequency_spin.setRange(1.0, 5.0)
        self.cpu_frequency_spin.setValue(3.0)
        self.cpu_frequency_spin.setSuffix(" GHz")
        
        self.app_params_layout.addWidget(QLabel("TDP:"), 0, 0)
        self.app_params_layout.addWidget(self.cpu_tdp_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Cores:"), 1, 0)
        self.app_params_layout.addWidget(self.cpu_cores_spin, 1, 1)
        self.app_params_layout.addWidget(QLabel("Base Frequency:"), 2, 0)
        self.app_params_layout.addWidget(self.cpu_frequency_spin, 2, 1)
    
    def add_power_electronics_parameters(self):
        """Add power electronics parameters"""
        self.pe_power_spin = QDoubleSpinBox()
        self.pe_power_spin.setRange(100.0, 10000.0)
        self.pe_power_spin.setValue(1000.0)
        self.pe_power_spin.setSuffix(" W")
        
        self.pe_efficiency_spin = QDoubleSpinBox()
        self.pe_efficiency_spin.setRange(0.8, 0.99)
        self.pe_efficiency_spin.setValue(0.95)
        self.pe_efficiency_spin.setDecimals(3)
        
        self.pe_switching_freq_spin = QDoubleSpinBox()
        self.pe_switching_freq_spin.setRange(1000.0, 100000.0)
        self.pe_switching_freq_spin.setValue(20000.0)
        self.pe_switching_freq_spin.setSuffix(" Hz")
        
        self.app_params_layout.addWidget(QLabel("Power Rating:"), 0, 0)
        self.app_params_layout.addWidget(self.pe_power_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Efficiency:"), 1, 0)
        self.app_params_layout.addWidget(self.pe_efficiency_spin, 1, 1)
        self.app_params_layout.addWidget(QLabel("Switching Frequency:"), 2, 0)
        self.app_params_layout.addWidget(self.pe_switching_freq_spin, 2, 1)
    
    def add_led_parameters(self):
        """Add LED-specific parameters"""
        self.led_optical_power_spin = QDoubleSpinBox()
        self.led_optical_power_spin.setRange(0.1, 100.0)
        self.led_optical_power_spin.setValue(10.0)
        self.led_optical_power_spin.setSuffix(" W")
        
        self.led_efficiency_spin = QDoubleSpinBox()
        self.led_efficiency_spin.setRange(0.1, 0.6)
        self.led_efficiency_spin.setValue(0.3)
        self.led_efficiency_spin.setDecimals(2)
        
        self.led_junction_area_spin = QDoubleSpinBox()
        self.led_junction_area_spin.setRange(0.1e-6, 10e-6)
        self.led_junction_area_spin.setValue(1e-6)
        self.led_junction_area_spin.setSuffix(" m²")
        self.led_junction_area_spin.setDecimals(8)
        
        self.app_params_layout.addWidget(QLabel("Optical Power:"), 0, 0)
        self.app_params_layout.addWidget(self.led_optical_power_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Efficiency:"), 1, 0)
        self.app_params_layout.addWidget(self.led_efficiency_spin, 1, 1)
        self.app_params_layout.addWidget(QLabel("Junction Area:"), 2, 0)
        self.app_params_layout.addWidget(self.led_junction_area_spin, 2, 1)
    
    def add_automotive_parameters(self):
        """Add automotive electronics parameters"""
        self.auto_power_spin = QDoubleSpinBox()
        self.auto_power_spin.setRange(50.0, 1000.0)
        self.auto_power_spin.setValue(200.0)
        self.auto_power_spin.setSuffix(" W")

        self.auto_efficiency_spin = QDoubleSpinBox()
        self.auto_efficiency_spin.setRange(0.85, 0.98)
        self.auto_efficiency_spin.setValue(0.92)
        self.auto_efficiency_spin.setDecimals(3)

        self.auto_max_temp_spin = QDoubleSpinBox()
        self.auto_max_temp_spin.setRange(350.0, 450.0)
        self.auto_max_temp_spin.setValue(398.15)
        self.auto_max_temp_spin.setSuffix(" K")

        self.app_params_layout.addWidget(QLabel("Power Rating:"), 0, 0)
        self.app_params_layout.addWidget(self.auto_power_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Efficiency:"), 1, 0)
        self.app_params_layout.addWidget(self.auto_efficiency_spin, 1, 1)
        self.app_params_layout.addWidget(QLabel("Max Operating Temp:"), 2, 0)
        self.app_params_layout.addWidget(self.auto_max_temp_spin, 2, 1)

    def add_data_center_parameters(self):
        """Add data center parameters"""
        self.dc_server_power_spin = QDoubleSpinBox()
        self.dc_server_power_spin.setRange(100.0, 2000.0)
        self.dc_server_power_spin.setValue(500.0)
        self.dc_server_power_spin.setSuffix(" W")

        self.dc_server_count_spin = QSpinBox()
        self.dc_server_count_spin.setRange(1, 100)
        self.dc_server_count_spin.setValue(20)

        self.dc_cooling_efficiency_spin = QDoubleSpinBox()
        self.dc_cooling_efficiency_spin.setRange(0.7, 0.95)
        self.dc_cooling_efficiency_spin.setValue(0.85)
        self.dc_cooling_efficiency_spin.setDecimals(2)

        self.app_params_layout.addWidget(QLabel("Server Power:"), 0, 0)
        self.app_params_layout.addWidget(self.dc_server_power_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Server Count:"), 1, 0)
        self.app_params_layout.addWidget(self.dc_server_count_spin, 1, 1)
        self.app_params_layout.addWidget(QLabel("Cooling Efficiency:"), 2, 0)
        self.app_params_layout.addWidget(self.dc_cooling_efficiency_spin, 2, 1)

    def add_aerospace_parameters(self):
        """Add aerospace parameters"""
        self.aero_avionics_power_spin = QDoubleSpinBox()
        self.aero_avionics_power_spin.setRange(50.0, 500.0)
        self.aero_avionics_power_spin.setValue(150.0)
        self.aero_avionics_power_spin.setSuffix(" W")

        self.aero_altitude_spin = QDoubleSpinBox()
        self.aero_altitude_spin.setRange(0.0, 50000.0)
        self.aero_altitude_spin.setValue(10000.0)
        self.aero_altitude_spin.setSuffix(" m")

        self.aero_thermal_cycling_spin = QDoubleSpinBox()
        self.aero_thermal_cycling_spin.setRange(0.5, 2.0)
        self.aero_thermal_cycling_spin.setValue(1.0)
        self.aero_thermal_cycling_spin.setDecimals(1)

        self.app_params_layout.addWidget(QLabel("Avionics Power:"), 0, 0)
        self.app_params_layout.addWidget(self.aero_avionics_power_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Altitude:"), 1, 0)
        self.app_params_layout.addWidget(self.aero_altitude_spin, 1, 1)
        self.app_params_layout.addWidget(QLabel("Thermal Cycling:"), 2, 0)
        self.app_params_layout.addWidget(self.aero_thermal_cycling_spin, 2, 1)

    def add_renewable_energy_parameters(self):
        """Add renewable energy parameters"""
        self.re_inverter_power_spin = QDoubleSpinBox()
        self.re_inverter_power_spin.setRange(1000.0, 20000.0)
        self.re_inverter_power_spin.setValue(5000.0)
        self.re_inverter_power_spin.setSuffix(" W")

        self.re_efficiency_spin = QDoubleSpinBox()
        self.re_efficiency_spin.setRange(0.90, 0.99)
        self.re_efficiency_spin.setValue(0.96)
        self.re_efficiency_spin.setDecimals(3)

        self.re_solar_irradiance_spin = QDoubleSpinBox()
        self.re_solar_irradiance_spin.setRange(200.0, 1200.0)
        self.re_solar_irradiance_spin.setValue(1000.0)
        self.re_solar_irradiance_spin.setSuffix(" W/m²")

        self.app_params_layout.addWidget(QLabel("Inverter Power:"), 0, 0)
        self.app_params_layout.addWidget(self.re_inverter_power_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Efficiency:"), 1, 0)
        self.app_params_layout.addWidget(self.re_efficiency_spin, 1, 1)
        self.app_params_layout.addWidget(QLabel("Solar Irradiance:"), 2, 0)
        self.app_params_layout.addWidget(self.re_solar_irradiance_spin, 2, 1)

    def add_generic_parameters(self):
        """Add generic thermal parameters"""
        self.generic_power_spin = QDoubleSpinBox()
        self.generic_power_spin.setRange(1.0, 1000.0)
        self.generic_power_spin.setValue(100.0)
        self.generic_power_spin.setSuffix(" W")

        self.generic_efficiency_spin = QDoubleSpinBox()
        self.generic_efficiency_spin.setRange(0.5, 0.99)
        self.generic_efficiency_spin.setValue(0.9)
        self.generic_efficiency_spin.setDecimals(2)

        self.app_params_layout.addWidget(QLabel("Power Rating:"), 0, 0)
        self.app_params_layout.addWidget(self.generic_power_spin, 0, 1)
        self.app_params_layout.addWidget(QLabel("Efficiency:"), 1, 0)
        self.app_params_layout.addWidget(self.generic_efficiency_spin, 1, 1)
    
    def run_basic_simulation(self):
        """Run basic thermal simulation"""
        try:
            ambient_temp = self.ambient_temp_input.value()
            current = self.current_input.value()
            power = self.power_input.value()
            
            self.error_label.setText("")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # Run simulation using the original simulator
            self.simulator.simulate_thermal(ambient_temp, current)
            wafer = self.simulator.get_wafer()
            temp_profile = wafer.get_temperature_profile()
            
            # Update results
            max_temp = temp_profile.max()
            min_temp = temp_profile.min()
            
            self.max_temp_label.setText(f"{max_temp:.2f} K ({max_temp - 273.15:.2f} °C)")
            self.min_temp_label.setText(f"{min_temp:.2f} K ({min_temp - 273.15:.2f} °C)")
            self.thermal_resistance_label.setText("0.10 K/W")  # Placeholder
            self.hotspots_label.setText("2")  # Placeholder
            
            # Update visualization
            self.visualization_widget.update_temperature_data(temp_profile, [(25, 25), (30, 30)])
            
            self.progress_bar.setValue(100)
            self.progress_bar.setVisible(False)
            
            self.thermal_updated.emit(wafer)
            
        except Exception as e:
            self.error_label.setText(f"Error: {str(e)}")
            self.progress_bar.setVisible(False)
    
    def run_industrial_simulation(self):
        """Run industrial thermal simulation"""
        try:
            app_type = self.application_combo.currentData()
            specs = self.get_application_specs(app_type)
            
            # Start simulation in worker thread
            self.simulation_worker = ThermalSimulationWorker(self.thermal_engine, app_type, specs)
            self.simulation_worker.simulation_finished.connect(self.on_simulation_finished)
            self.simulation_worker.progress_updated.connect(self.progress_bar.setValue)
            self.simulation_worker.device_created.connect(self.on_device_created)
            
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.industrial_simulate_button.setEnabled(False)
            
            self.simulation_worker.start()
            
        except Exception as e:
            self.error_label.setText(f"Error: {str(e)}")
    
    def get_application_specs(self, app_type):
        """Get application-specific specifications"""
        specs = {}
        
        if app_type == ThermalApplicationType.CPU_THERMAL_MANAGEMENT:
            specs = {
                "tdp": self.cpu_tdp_spin.value(),
                "cores": self.cpu_cores_spin.value(),
                "base_frequency": self.cpu_frequency_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        elif app_type == ThermalApplicationType.POWER_ELECTRONICS_COOLING:
            specs = {
                "power_rating": self.pe_power_spin.value(),
                "efficiency": self.pe_efficiency_spin.value(),
                "switching_frequency": self.pe_switching_freq_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        elif app_type == ThermalApplicationType.LED_THERMAL_DESIGN:
            specs = {
                "optical_power": self.led_optical_power_spin.value(),
                "efficiency": self.led_efficiency_spin.value(),
                "junction_area": self.led_junction_area_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        elif app_type == ThermalApplicationType.AUTOMOTIVE_ELECTRONICS:
            specs = {
                "power_rating": self.auto_power_spin.value(),
                "efficiency": self.auto_efficiency_spin.value(),
                "max_operating_temperature": self.auto_max_temp_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        elif app_type == ThermalApplicationType.DATA_CENTER_COOLING:
            specs = {
                "server_power": self.dc_server_power_spin.value(),
                "server_count": self.dc_server_count_spin.value(),
                "cooling_efficiency": self.dc_cooling_efficiency_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        elif app_type == ThermalApplicationType.AEROSPACE_THERMAL:
            specs = {
                "avionics_power": self.aero_avionics_power_spin.value(),
                "altitude": self.aero_altitude_spin.value(),
                "thermal_cycling": self.aero_thermal_cycling_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        elif app_type == ThermalApplicationType.RENEWABLE_ENERGY:
            specs = {
                "inverter_power": self.re_inverter_power_spin.value(),
                "efficiency": self.re_efficiency_spin.value(),
                "solar_irradiance": self.re_solar_irradiance_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        else:
            specs = {
                "power_rating": self.generic_power_spin.value(),
                "efficiency": self.generic_efficiency_spin.value(),
                "ambient_temperature": self.ambient_temp_input.value()
            }
        
        return specs
    
    def on_simulation_finished(self, results):
        """Handle simulation completion"""
        self.progress_bar.setVisible(False)
        self.industrial_simulate_button.setEnabled(True)
        
        if "error" in results:
            self.error_label.setText(f"Simulation error: {results['error']}")
            return
        
        self.current_results = results
        
        # Update results table
        self.update_results_table(results)
        
        # Update visualization if temperature field is available
        if "temperature_field" in results and results["temperature_field"] is not None:
            self.visualization_widget.update_temperature_data(
                results["temperature_field"],
                results.get("hotspots", [])
            )

        # Emit signal for main window
        self.thermal_updated.emit(results)

    def on_device_created(self, device_structure):
        """Handle thermal device structure creation"""
        self.current_device = device_structure
        self.error_label.setText(f"Thermal device created: {device_structure.name}")

        # Update visualization to show device
        self.update_device_visualization()

        # Emit signal for main window
        self.device_created.emit(device_structure)

    def update_device_visualization(self):
        """Update visualization to show thermal device"""
        if not self.current_device:
            return

        # Update visualization with device temperature distribution
        if self.current_device.temperature_distribution is not None:
            self.visualization_widget.update_temperature_data(
                self.current_device.temperature_distribution,
                []  # No specific hotspots for device view
            )
    
    def update_results_table(self, results):
        """Update the results table with simulation results"""
        self.results_table.setRowCount(0)
        
        result_items = [
            ("Maximum Temperature", f"{results.get('max_temperature', 0):.2f} K"),
            ("Minimum Temperature", f"{results.get('min_temperature', 0):.2f} K"),
            ("Temperature Rise", f"{results.get('temperature_rise', 0):.2f} K"),
            ("Thermal Resistance", f"{results.get('thermal_resistance', 0):.6f} K/W"),
            ("Thermal Efficiency", f"{results.get('thermal_efficiency', 0):.1f} %"),
            ("Cooling Effectiveness", f"{results.get('cooling_effectiveness', 0):.1f} %"),
            ("Hotspots Detected", str(len(results.get('hotspots', [])))),
            ("Simulation Converged", str(results.get('converged', False))),
            ("Simulation Time", f"{results.get('simulation_time', 0):.3f} s")
        ]
        
        self.results_table.setRowCount(len(result_items))
        
        for i, (param, value) in enumerate(result_items):
            self.results_table.setItem(i, 0, QTableWidgetItem(param))
            self.results_table.setItem(i, 1, QTableWidgetItem(value))
    
    def run_optimization(self):
        """Run thermal optimization"""
        try:
            method = self.optimization_method_combo.currentData()
            max_iterations = self.max_iterations_spin.value()
            population_size = self.population_size_spin.value()
            
            # Create design variables and objectives
            design_variables = self.optimization_manager.create_standard_thermal_variables()
            objectives = self.optimization_manager.create_standard_thermal_objectives()
            
            # Define evaluation function
            def evaluation_function(params):
                # This would run thermal simulation with given parameters
                # For now, return mock results
                return {
                    "max_temperature": 350.0 + np.random.normal(0, 10),
                    "thermal_resistance": 0.1 + np.random.normal(0, 0.02)
                }
            
            # Run optimization
            self.optimize_button.setEnabled(False)
            self.optimization_results_text.append("Starting thermal optimization...\n")
            
            results = self.optimization_manager.run_optimization(
                method, design_variables, objectives, evaluation_function,
                max_iterations=max_iterations, population_size=population_size
            )
            
            # Display results
            self.display_optimization_results(results)
            self.optimize_button.setEnabled(True)
            
        except Exception as e:
            self.optimization_results_text.append(f"Optimization error: {str(e)}\n")
            self.optimize_button.setEnabled(True)
    
    def display_optimization_results(self, results):
        """Display optimization results"""
        self.optimization_results_text.append("Optimization completed!\n")
        self.optimization_results_text.append(f"Best objective value: {results.get('best_objective_value', 0):.6f}\n")
        
        if results.get('best_solution') is not None:
            self.optimization_results_text.append("Optimal parameters:\n")
            variables = self.optimization_manager.create_standard_thermal_variables()
            for i, var in enumerate(variables):
                if i < len(results['best_solution']):
                    self.optimization_results_text.append(f"  {var.name}: {results['best_solution'][i]:.4f}\n")
        
        self.optimization_results_text.append(f"Convergence achieved: {results.get('convergence_achieved', False)}\n\n")
    
    def start_monitoring(self):
        """Start real-time thermal monitoring"""
        try:
            app_type = self.application_combo.currentData()
            
            config = ThermalMonitoringConfig(
                update_interval=self.monitoring_interval_spin.value(),
                temperature_threshold=self.temp_threshold_spin.value()
            )
            
            self.thermal_engine.start_real_time_monitoring(app_type, config)
            
            self.start_monitoring_button.setEnabled(False)
            self.stop_monitoring_button.setEnabled(True)
            
            self.monitoring_log.append("Real-time thermal monitoring started.\n")
            
            # Start monitoring timer
            self.monitoring_timer = QTimer()
            self.monitoring_timer.timeout.connect(self.update_monitoring_display)
            self.monitoring_timer.start(int(config.update_interval * 1000))
            
        except Exception as e:
            self.monitoring_log.append(f"Error starting monitoring: {str(e)}\n")
    
    def stop_monitoring(self):
        """Stop real-time thermal monitoring"""
        try:
            self.thermal_engine.stop_real_time_monitoring()
            
            if hasattr(self, 'monitoring_timer'):
                self.monitoring_timer.stop()
            
            self.start_monitoring_button.setEnabled(True)
            self.stop_monitoring_button.setEnabled(False)
            
            self.monitoring_log.append("Real-time thermal monitoring stopped.\n")
            
        except Exception as e:
            self.monitoring_log.append(f"Error stopping monitoring: {str(e)}\n")
    
    def update_monitoring_display(self):
        """Update monitoring display with latest data"""
        try:
            monitoring_data = self.thermal_engine.get_monitoring_data()
            
            if monitoring_data:
                latest_data = monitoring_data[-1]
                thermal_data = latest_data["thermal_data"]
                
                timestamp = latest_data["timestamp"]
                temp = thermal_data["current_temperature"]
                power = thermal_data["power_dissipation"]
                
                self.monitoring_log.append(
                    f"[{timestamp:.1f}] Temp: {temp:.1f} K, Power: {power:.1f} W\n"
                )
                
                # Scroll to bottom
                cursor = self.monitoring_log.textCursor()
                cursor.movePosition(cursor.End)
                self.monitoring_log.setTextCursor(cursor)
                
        except Exception as e:
            self.monitoring_log.append(f"Monitoring update error: {str(e)}\n")

    def run_thermal_simulation(self, process_name=None, custom_params=None):
        """Run thermal simulation with backend integration"""
        try:
            if not self.current_wafer:
                # Create default wafer if none provided
                try:
                    from ..wafer import Wafer
                    self.current_wafer = Wafer(diameter=200.0, thickness=775.0, material="silicon")
                    print("Created default wafer for thermal simulation")
                except ImportError:
                    print("Wafer module not available - using mock wafer")
                    return {"success": False, "error": "No wafer available for simulation"}

            if self.thermal_engine:
                # Use real backend
                print(f"Running thermal simulation with backend: {process_name}")

                # Get parameters from UI or use custom params
                if custom_params:
                    conditions = custom_params
                else:
                    # Extract parameters from UI
                    conditions = {
                        'ambient_temperature': 300.0,  # K
                        'power_dissipation': 10.0,  # W
                        'thermal_conductivity': 150.0,  # W/m·K
                        'heat_capacity': 700.0,  # J/kg·K
                        'convection_coefficient': 25.0,  # W/m²·K
                        'simulation_time': 1.0,  # seconds
                        'time_step': 0.01  # seconds
                    }

                # Run simulation using thermal engine
                try:
                    # Create thermal device structure
                    device_config = {
                        'width': 10.0,  # mm
                        'height': 10.0,  # mm
                        'thickness': 0.775,  # mm
                        'material': 'silicon',
                        'power_sources': [{'x': 5.0, 'y': 5.0, 'power': conditions['power_dissipation']}]
                    }

                    # Run thermal analysis
                    results = self.thermal_engine.run_thermal_analysis(device_config, conditions)

                    # Process results
                    self.current_results = {
                        'success': True,
                        'wafer': self.current_wafer,
                        'conditions': conditions,
                        'results': results,
                        'process_name': process_name or 'Enhanced Thermal',
                        'max_temperature': results.get('max_temperature', 350.0),
                        'avg_temperature': results.get('avg_temperature', 325.0),
                        'thermal_gradient': results.get('thermal_gradient', 25.0),
                        'thermal_resistance': results.get('thermal_resistance', 2.5)
                    }

                    print("Thermal simulation completed successfully")
                    return self.current_results

                except Exception as e:
                    print(f"Thermal simulation failed: {e}")
                    error_result = {
                        'success': False,
                        'error': str(e),
                        'process_name': process_name or 'Thermal'
                    }
                    self.current_results = error_result
                    return error_result

            else:
                # Use mock simulation
                print("Using mock thermal simulation")
                mock_results = {
                    'success': True,
                    'wafer': self.current_wafer,
                    'max_temperature': 348.5,
                    'avg_temperature': 322.1,
                    'thermal_gradient': 26.4,
                    'thermal_resistance': 2.3,
                    'process_name': process_name or 'Mock Thermal'
                }

                self.current_results = mock_results
                return mock_results

        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e),
                'process_name': process_name or 'Thermal'
            }
            print(f"Thermal simulation failed: {e}")
            self.current_results = error_result
            return error_result

    def create_database_management_tab(self):
        """Create database management tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Database status
        status_group = QGroupBox("Database Status")
        status_layout = QVBoxLayout(status_group)

        self.db_status_label = QLabel("Database: Connected" if self.database_enabled else "Database: Not Available")
        self.db_status_label.setStyleSheet("color: green;" if self.database_enabled else "color: red;")
        status_layout.addWidget(self.db_status_label)

        if self.database_enabled:
            # Statistics display
            self.db_stats_label = QLabel("Loading statistics...")
            status_layout.addWidget(self.db_stats_label)

            # Refresh button
            refresh_btn = QPushButton("Refresh Statistics")
            refresh_btn.clicked.connect(self.refresh_database_statistics)
            status_layout.addWidget(refresh_btn)

        layout.addWidget(status_group)

        # Material management
        materials_group = QGroupBox("Thermal Materials")
        materials_layout = QVBoxLayout(materials_group)

        # Material selection
        self.material_combo = QComboBox()
        self.load_materials_list()
        materials_layout.addWidget(QLabel("Select Material:"))
        materials_layout.addWidget(self.material_combo)

        # Material properties display
        self.material_props_text = QTextEdit()
        self.material_props_text.setMaximumHeight(150)
        materials_layout.addWidget(QLabel("Material Properties:"))
        materials_layout.addWidget(self.material_props_text)

        # Load material button
        load_material_btn = QPushButton("Load Material Properties")
        load_material_btn.clicked.connect(self.load_selected_material)
        materials_layout.addWidget(load_material_btn)

        layout.addWidget(materials_group)

        # Recipe management
        recipes_group = QGroupBox("Industrial Recipes")
        recipes_layout = QVBoxLayout(recipes_group)

        # Application type selection
        self.recipe_app_combo = QComboBox()
        self.recipe_app_combo.addItems([
            "CPU_THERMAL_MANAGEMENT",
            "POWER_ELECTRONICS_COOLING",
            "LED_THERMAL_DESIGN",
            "AUTOMOTIVE_ELECTRONICS",
            "DATA_CENTER_COOLING"
        ])
        recipes_layout.addWidget(QLabel("Application Type:"))
        recipes_layout.addWidget(self.recipe_app_combo)

        # Recipe list
        self.recipe_list = QComboBox()
        recipes_layout.addWidget(QLabel("Available Recipes:"))
        recipes_layout.addWidget(self.recipe_list)

        # Load recipes button
        load_recipes_btn = QPushButton("Load Recipes")
        load_recipes_btn.clicked.connect(self.load_recipes_for_application)
        recipes_layout.addWidget(load_recipes_btn)

        layout.addWidget(recipes_group)

        layout.addStretch()
        return tab

    def create_parameter_analytics_tab(self):
        """Create parameter analytics tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Analytics controls
        controls_group = QGroupBox("Analytics Controls")
        controls_layout = QGridLayout(controls_group)

        # Parameter selection
        self.analytics_param_combo = QComboBox()
        self.analytics_param_combo.addItems([
            "Temperature Distribution",
            "Thermal Resistance",
            "Hot Spot Analysis",
            "Thermal Gradient",
            "Power Dissipation",
            "Cooling Efficiency"
        ])
        controls_layout.addWidget(QLabel("Parameter:"), 0, 0)
        controls_layout.addWidget(self.analytics_param_combo, 0, 1)

        # Analysis type
        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "Statistical Analysis",
            "Trend Analysis",
            "Sensitivity Analysis",
            "Optimization Analysis"
        ])
        controls_layout.addWidget(QLabel("Analysis Type:"), 1, 0)
        controls_layout.addWidget(self.analysis_type_combo, 1, 1)

        # Run analytics button
        run_analytics_btn = QPushButton("Run Analytics")
        run_analytics_btn.clicked.connect(self.run_parameter_analytics)
        controls_layout.addWidget(run_analytics_btn, 2, 0, 1, 2)

        layout.addWidget(controls_group)

        # Results display
        results_group = QGroupBox("Analytics Results")
        results_layout = QVBoxLayout(results_group)

        self.analytics_results_text = QTextEdit()
        self.analytics_results_text.setMinimumHeight(300)
        results_layout.addWidget(self.analytics_results_text)

        layout.addWidget(results_group)

        # Export options
        export_group = QGroupBox("Export Options")
        export_layout = QHBoxLayout(export_group)

        export_csv_btn = QPushButton("Export to CSV")
        export_csv_btn.clicked.connect(lambda: self.export_analytics_results("csv"))
        export_layout.addWidget(export_csv_btn)

        export_pdf_btn = QPushButton("Export to PDF")
        export_pdf_btn.clicked.connect(lambda: self.export_analytics_results("pdf"))
        export_layout.addWidget(export_pdf_btn)

        layout.addWidget(export_group)

        return tab
