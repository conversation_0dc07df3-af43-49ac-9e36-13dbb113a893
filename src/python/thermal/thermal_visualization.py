"""
Thermal Visualization Components
===============================

Comprehensive visualization system for thermal analysis including device cross-sections,
3D thermal maps, wafer-level analysis, and parameter analytics visualization.

Author: Dr<PERSON> <PERSON>
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as patches
from mpl_toolkits.mplot3d import Axes3D
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ThermalVisualizationEngine:
    """Advanced thermal visualization engine"""
    
    def __init__(self):
        """Initialize thermal visualization engine"""
        self.figure = None
        self.canvas = None
        self.custom_colormaps = self._create_custom_colormaps()
        
    def _create_custom_colormaps(self) -> Dict[str, LinearSegmentedColormap]:
        """Create custom colormaps for thermal visualization"""
        colormaps = {}
        
        # Thermal colormap (blue to red)
        thermal_colors = ['#000080', '#0000FF', '#00FFFF', '#00FF00', '#FFFF00', '#FF8000', '#FF0000', '#800000']
        colormaps['thermal'] = LinearSegmentedColormap.from_list('thermal', thermal_colors)
        
        # Hot spot colormap (black to white to red)
        hotspot_colors = ['#000000', '#400040', '#800080', '#FF00FF', '#FF8080', '#FFFF00', '#FF0000']
        colormaps['hotspot'] = LinearSegmentedColormap.from_list('hotspot', hotspot_colors)
        
        # Gradient colormap (for thermal gradients)
        gradient_colors = ['#000040', '#000080', '#0040FF', '#00FFFF', '#FFFF00', '#FF4000', '#FF0000']
        colormaps['gradient'] = LinearSegmentedColormap.from_list('gradient', gradient_colors)
        
        return colormaps
    
    def create_2d_temperature_map(self, temperature_data: np.ndarray, 
                                 title: str = "Temperature Distribution",
                                 colormap: str = 'thermal',
                                 show_contours: bool = True,
                                 show_hotspots: bool = True,
                                 hotspots: Optional[List[Dict]] = None) -> Figure:
        """Create 2D temperature distribution map"""
        try:
            fig = Figure(figsize=(10, 8))
            ax = fig.add_subplot(111)
            
            # Create temperature map
            if isinstance(temperature_data, list):
                temperature_data = np.array(temperature_data)
            
            # Use custom colormap if available
            cmap = self.custom_colormaps.get(colormap, plt.cm.hot)
            
            im = ax.imshow(temperature_data, cmap=cmap, aspect='equal', origin='lower')
            
            # Add colorbar
            cbar = fig.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('Temperature (K)', rotation=270, labelpad=20)
            
            # Add contour lines if requested
            if show_contours:
                contours = ax.contour(temperature_data, colors='black', alpha=0.3, linewidths=0.5)
                ax.clabel(contours, inline=True, fontsize=8, fmt='%.0f')
            
            # Mark hotspots if provided
            if show_hotspots and hotspots:
                for hotspot in hotspots:
                    x, y = hotspot.get('x', 0), hotspot.get('y', 0)
                    # Convert normalized coordinates to array indices
                    if 0 <= x <= 1 and 0 <= y <= 1:
                        x_idx = int(x * temperature_data.shape[1])
                        y_idx = int(y * temperature_data.shape[0])
                        ax.plot(x_idx, y_idx, 'wo', markersize=8, markeredgecolor='black', markeredgewidth=2)
                        ax.annotate(f'{hotspot.get("temperature", 0):.1f}K', 
                                  (x_idx, y_idx), xytext=(5, 5), textcoords='offset points',
                                  fontsize=8, color='white', weight='bold')
            
            ax.set_title(title, fontsize=14, weight='bold')
            ax.set_xlabel('X Position')
            ax.set_ylabel('Y Position')
            
            # Add statistics text
            stats_text = f'Max: {np.max(temperature_data):.1f}K\n'
            stats_text += f'Min: {np.min(temperature_data):.1f}K\n'
            stats_text += f'Avg: {np.mean(temperature_data):.1f}K\n'
            stats_text += f'Std: {np.std(temperature_data):.1f}K'
            
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            fig.tight_layout()
            return fig
            
        except Exception as e:
            logger.error(f"Failed to create 2D temperature map: {e}")
            # Return empty figure on error
            fig = Figure(figsize=(10, 8))
            ax = fig.add_subplot(111)
            ax.text(0.5, 0.5, f'Error creating visualization:\n{str(e)}', 
                   ha='center', va='center', transform=ax.transAxes)
            return fig
    
    def create_3d_temperature_surface(self, temperature_data: np.ndarray,
                                     title: str = "3D Temperature Surface",
                                     colormap: str = 'thermal') -> Figure:
        """Create 3D temperature surface plot"""
        try:
            fig = Figure(figsize=(12, 9))
            ax = fig.add_subplot(111, projection='3d')
            
            if isinstance(temperature_data, list):
                temperature_data = np.array(temperature_data)
            
            # Create coordinate grids
            rows, cols = temperature_data.shape
            x = np.linspace(0, cols-1, cols)
            y = np.linspace(0, rows-1, rows)
            X, Y = np.meshgrid(x, y)
            
            # Use custom colormap if available
            cmap = self.custom_colormaps.get(colormap, plt.cm.hot)
            
            # Create 3D surface
            surf = ax.plot_surface(X, Y, temperature_data, cmap=cmap, 
                                 alpha=0.9, linewidth=0, antialiased=True)
            
            # Add colorbar
            cbar = fig.colorbar(surf, ax=ax, shrink=0.6, aspect=20)
            cbar.set_label('Temperature (K)', rotation=270, labelpad=20)
            
            # Customize 3D plot
            ax.set_title(title, fontsize=14, weight='bold')
            ax.set_xlabel('X Position')
            ax.set_ylabel('Y Position')
            ax.set_zlabel('Temperature (K)')
            
            # Set viewing angle
            ax.view_init(elev=30, azim=45)
            
            fig.tight_layout()
            return fig
            
        except Exception as e:
            logger.error(f"Failed to create 3D temperature surface: {e}")
            fig = Figure(figsize=(12, 9))
            ax = fig.add_subplot(111)
            ax.text(0.5, 0.5, f'Error creating 3D visualization:\n{str(e)}', 
                   ha='center', va='center', transform=ax.transAxes)
            return fig
    
    def create_device_cross_section(self, device_data: Dict[str, Any],
                                   temperature_profile: Optional[np.ndarray] = None,
                                   title: str = "Device Cross-Section") -> Figure:
        """Create device cross-section with thermal overlay"""
        try:
            fig = Figure(figsize=(12, 8))
            ax = fig.add_subplot(111)
            
            # Device dimensions
            device_width = device_data.get('width_um', 100.0)
            device_height = device_data.get('height_um', 50.0)
            
            # Draw device layers
            layers = device_data.get('layers', [])
            y_offset = 0
            
            for i, layer in enumerate(layers):
                layer_height = layer.get('thickness_um', 5.0)
                layer_material = layer.get('material', 'Unknown')
                
                # Color based on material
                material_colors = {
                    'Silicon': '#C0C0C0',
                    'SiO2': '#E0E0FF',
                    'Metal': '#FFD700',
                    'Polysilicon': '#808080',
                    'Nitride': '#90EE90'
                }
                color = material_colors.get(layer_material, '#CCCCCC')
                
                # Draw layer rectangle
                rect = patches.Rectangle((0, y_offset), device_width, layer_height,
                                       linewidth=1, edgecolor='black', facecolor=color, alpha=0.7)
                ax.add_patch(rect)
                
                # Add layer label
                ax.text(device_width/2, y_offset + layer_height/2, 
                       f'{layer_material}\n{layer_height:.1f}μm',
                       ha='center', va='center', fontsize=8, weight='bold')
                
                y_offset += layer_height
            
            # Overlay temperature profile if provided
            if temperature_profile is not None:
                # Create temperature overlay
                temp_overlay = ax.imshow(temperature_profile, extent=[0, device_width, 0, device_height],
                                       cmap=self.custom_colormaps.get('thermal', plt.cm.hot),
                                       alpha=0.5, aspect='auto')
                
                # Add temperature colorbar
                cbar = fig.colorbar(temp_overlay, ax=ax, shrink=0.8)
                cbar.set_label('Temperature (K)', rotation=270, labelpad=20)
            
            # Add device features (contacts, gates, etc.)
            features = device_data.get('features', [])
            for feature in features:
                feature_type = feature.get('type', 'contact')
                x, y = feature.get('position', [0, 0])
                width, height = feature.get('size', [5, 2])
                
                if feature_type == 'contact':
                    contact = patches.Rectangle((x, y), width, height,
                                              linewidth=2, edgecolor='red', facecolor='gold', alpha=0.8)
                    ax.add_patch(contact)
                    ax.text(x + width/2, y + height/2, 'Contact', ha='center', va='center', fontsize=6)
                
                elif feature_type == 'gate':
                    gate = patches.Rectangle((x, y), width, height,
                                           linewidth=2, edgecolor='blue', facecolor='lightblue', alpha=0.8)
                    ax.add_patch(gate)
                    ax.text(x + width/2, y + height/2, 'Gate', ha='center', va='center', fontsize=6)
            
            ax.set_xlim(0, device_width)
            ax.set_ylim(0, device_height)
            ax.set_xlabel('Width (μm)')
            ax.set_ylabel('Height (μm)')
            ax.set_title(title, fontsize=14, weight='bold')
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            
            fig.tight_layout()
            return fig
            
        except Exception as e:
            logger.error(f"Failed to create device cross-section: {e}")
            fig = Figure(figsize=(12, 8))
            ax = fig.add_subplot(111)
            ax.text(0.5, 0.5, f'Error creating cross-section:\n{str(e)}', 
                   ha='center', va='center', transform=ax.transAxes)
            return fig

    def create_wafer_thermal_map(self, wafer_data: Dict[str, Any],
                                temperature_data: Optional[np.ndarray] = None,
                                title: str = "Wafer Thermal Map") -> Figure:
        """Create wafer-level thermal map"""
        try:
            fig = Figure(figsize=(10, 10))
            ax = fig.add_subplot(111)

            # Wafer parameters
            wafer_diameter = wafer_data.get('diameter_mm', 300.0)
            die_size = wafer_data.get('die_size_mm', [10.0, 10.0])
            exclusion_edge = wafer_data.get('exclusion_edge_mm', 3.0)

            # Draw wafer circle
            wafer_circle = patches.Circle((0, 0), wafer_diameter/2,
                                        linewidth=2, edgecolor='black', facecolor='lightgray', alpha=0.3)
            ax.add_patch(wafer_circle)

            # Draw exclusion zone
            exclusion_circle = patches.Circle((0, 0), wafer_diameter/2 - exclusion_edge,
                                            linewidth=1, edgecolor='red', facecolor='none', linestyle='--')
            ax.add_patch(exclusion_circle)

            # Calculate die positions
            usable_radius = wafer_diameter/2 - exclusion_edge
            dies_x = int(2 * usable_radius / die_size[0])
            dies_y = int(2 * usable_radius / die_size[1])

            # Draw dies with temperature coloring
            die_temps = []
            for i in range(-dies_x//2, dies_x//2 + 1):
                for j in range(-dies_y//2, dies_y//2 + 1):
                    x_center = i * die_size[0]
                    y_center = j * die_size[1]

                    # Check if die is within usable area
                    if np.sqrt(x_center**2 + y_center**2) <= usable_radius:
                        # Calculate temperature for this die
                        if temperature_data is not None:
                            # Map die position to temperature data
                            temp_i = int((i + dies_x//2) * temperature_data.shape[0] / dies_x)
                            temp_j = int((j + dies_y//2) * temperature_data.shape[1] / dies_y)
                            temp_i = max(0, min(temp_i, temperature_data.shape[0] - 1))
                            temp_j = max(0, min(temp_j, temperature_data.shape[1] - 1))
                            die_temp = temperature_data[temp_i, temp_j]
                        else:
                            # Default temperature with radial variation
                            distance_from_center = np.sqrt(x_center**2 + y_center**2)
                            die_temp = 298.15 + 20 * (distance_from_center / usable_radius)

                        die_temps.append(die_temp)

                        # Color die based on temperature
                        temp_norm = (die_temp - 298.15) / 50.0  # Normalize to 0-1
                        temp_norm = max(0, min(temp_norm, 1))
                        color = plt.cm.hot(temp_norm)

                        # Draw die rectangle
                        die_rect = patches.Rectangle((x_center - die_size[0]/2, y_center - die_size[1]/2),
                                                   die_size[0], die_size[1],
                                                   linewidth=0.5, edgecolor='black', facecolor=color, alpha=0.8)
                        ax.add_patch(die_rect)

            # Add colorbar for temperature
            if die_temps:
                sm = plt.cm.ScalarMappable(cmap=plt.cm.hot,
                                         norm=plt.Normalize(vmin=min(die_temps), vmax=max(die_temps)))
                sm.set_array([])
                cbar = fig.colorbar(sm, ax=ax, shrink=0.8)
                cbar.set_label('Temperature (K)', rotation=270, labelpad=20)

            # Add wafer information
            info_text = f'Wafer: {wafer_diameter}mm diameter\n'
            info_text += f'Die size: {die_size[0]}×{die_size[1]}mm\n'
            info_text += f'Dies on wafer: {len(die_temps)}\n'
            if die_temps:
                info_text += f'Temp range: {min(die_temps):.1f}-{max(die_temps):.1f}K'

            ax.text(-wafer_diameter/2 + 5, wafer_diameter/2 - 20, info_text, fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            ax.set_xlim(-wafer_diameter/2 - 10, wafer_diameter/2 + 10)
            ax.set_ylim(-wafer_diameter/2 - 10, wafer_diameter/2 + 10)
            ax.set_xlabel('X Position (mm)')
            ax.set_ylabel('Y Position (mm)')
            ax.set_title(title, fontsize=14, weight='bold')
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)

            fig.tight_layout()
            return fig

        except Exception as e:
            logger.error(f"Failed to create wafer thermal map: {e}")
            fig = Figure(figsize=(10, 10))
            ax = fig.add_subplot(111)
            ax.text(0.5, 0.5, f'Error creating wafer map:\n{str(e)}',
                   ha='center', va='center', transform=ax.transAxes)
            return fig
