"""
Thermal Module Database Integration
==================================

Database integration layer for thermal module providing comprehensive
data storage, retrieval, and management for thermal processes, materials,
equipment, and simulation results.

Author: Dr<PERSON>
"""

import logging
import uuid
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import psycopg2
from psycopg2.extras import RealDictCursor, Json
import pickle
import gzip

from ..database_manager import get_database_manager

logger = logging.getLogger(__name__)

class ThermalDatabaseIntegration:
    """Database integration for thermal module"""
    
    def __init__(self):
        """Initialize thermal database integration"""
        self.db = get_database_manager()
        self.schema_name = "thermal"
        self.ensure_schema_exists()
    
    def ensure_schema_exists(self):
        """Ensure thermal schema exists in database"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("CREATE SCHEMA IF NOT EXISTS thermal")
                cursor.execute("SET search_path TO thermal, public")
            logger.info("Thermal schema initialized")
        except Exception as e:
            logger.error(f"Failed to initialize thermal schema: {e}")
    
    # =====================================================
    # THERMAL MATERIALS MANAGEMENT
    # =====================================================
    
    def add_thermal_material(self, material_data: Dict[str, Any]) -> str:
        """Add thermal material to database"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO thermal.thermal_materials (
                        material_name, chemical_formula, material_class, crystal_structure,
                        thermal_conductivity_w_mk, specific_heat_j_kg_k, density_kg_m3,
                        thermal_expansion_per_k, melting_point_c, emissivity, absorptivity,
                        reflectivity, thermal_conductivity_vs_temp, specific_heat_vs_temp,
                        thermal_expansion_vs_temp, max_processing_temp_c, thermal_stability_rating,
                        oxidation_resistance, interface_thermal_resistance, surface_roughness_nm,
                        data_source, measurement_method, temperature_range_c, uncertainty_percent
                    ) VALUES (
                        %(material_name)s, %(chemical_formula)s, %(material_class)s, %(crystal_structure)s,
                        %(thermal_conductivity_w_mk)s, %(specific_heat_j_kg_k)s, %(density_kg_m3)s,
                        %(thermal_expansion_per_k)s, %(melting_point_c)s, %(emissivity)s, %(absorptivity)s,
                        %(reflectivity)s, %(thermal_conductivity_vs_temp)s, %(specific_heat_vs_temp)s,
                        %(thermal_expansion_vs_temp)s, %(max_processing_temp_c)s, %(thermal_stability_rating)s,
                        %(oxidation_resistance)s, %(interface_thermal_resistance)s, %(surface_roughness_nm)s,
                        %(data_source)s, %(measurement_method)s, %(temperature_range_c)s, %(uncertainty_percent)s
                    ) RETURNING material_id
                """, material_data)
                
                material_id = cursor.fetchone()['material_id']
                logger.info(f"Added thermal material: {material_data['material_name']} with ID: {material_id}")
                return str(material_id)
                
        except Exception as e:
            logger.error(f"Failed to add thermal material: {e}")
            raise
    
    def get_thermal_material(self, material_name: str) -> Optional[Dict[str, Any]]:
        """Get thermal material properties by name"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.thermal_materials 
                    WHERE material_name = %s
                """, (material_name,))
                
                result = cursor.fetchone()
                if result:
                    return dict(result)
                return None
                
        except Exception as e:
            logger.error(f"Failed to get thermal material {material_name}: {e}")
            return None
    
    def get_materials_by_class(self, material_class: str) -> List[Dict[str, Any]]:
        """Get thermal materials by class"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.thermal_materials 
                    WHERE material_class = %s
                    ORDER BY material_name
                """, (material_class,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"Failed to get materials by class {material_class}: {e}")
            return []
    
    def interpolate_material_properties(self, material_name: str, temperature: float) -> Optional[Dict[str, Any]]:
        """Interpolate material properties at given temperature"""
        material = self.get_thermal_material(material_name)
        if not material:
            return None
        
        # Check if temperature is within valid range
        temp_range = material.get('temperature_range_c', [25.0, 1000.0])
        if temperature < temp_range[0] or temperature > temp_range[1]:
            logger.warning(f"Temperature {temperature}°C outside valid range {temp_range} for {material_name}")
        
        # Interpolate temperature-dependent properties if available
        result = material.copy()
        
        # Interpolate thermal conductivity
        if material.get('thermal_conductivity_vs_temp'):
            try:
                temp_data = json.loads(material['thermal_conductivity_vs_temp'])
                result['thermal_conductivity_w_mk'] = self._interpolate_property(temp_data, temperature)
            except:
                pass
        
        # Interpolate specific heat
        if material.get('specific_heat_vs_temp'):
            try:
                temp_data = json.loads(material['specific_heat_vs_temp'])
                result['specific_heat_j_kg_k'] = self._interpolate_property(temp_data, temperature)
            except:
                pass
        
        # Interpolate thermal expansion
        if material.get('thermal_expansion_vs_temp'):
            try:
                temp_data = json.loads(material['thermal_expansion_vs_temp'])
                result['thermal_expansion_per_k'] = self._interpolate_property(temp_data, temperature)
            except:
                pass
        
        return result
    
    def _interpolate_property(self, temp_data: List[Dict], temperature: float) -> float:
        """Interpolate property value at given temperature"""
        if not temp_data or len(temp_data) < 2:
            return temp_data[0]['value'] if temp_data else 0.0
        
        # Sort by temperature
        temp_data = sorted(temp_data, key=lambda x: x['temp_c'])
        
        # Find interpolation points
        if temperature <= temp_data[0]['temp_c']:
            return temp_data[0]['value']
        if temperature >= temp_data[-1]['temp_c']:
            return temp_data[-1]['value']
        
        # Linear interpolation
        for i in range(len(temp_data) - 1):
            if temp_data[i]['temp_c'] <= temperature <= temp_data[i+1]['temp_c']:
                t1, v1 = temp_data[i]['temp_c'], temp_data[i]['value']
                t2, v2 = temp_data[i+1]['temp_c'], temp_data[i+1]['value']
                
                # Linear interpolation
                return v1 + (v2 - v1) * (temperature - t1) / (t2 - t1)
        
        return temp_data[0]['value']
    
    # =====================================================
    # THERMAL EQUIPMENT MANAGEMENT
    # =====================================================
    
    def add_thermal_equipment(self, equipment_data: Dict[str, Any]) -> str:
        """Add thermal equipment to database"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO thermal.thermal_equipment (
                        equipment_name, manufacturer, model, serial_number, equipment_type,
                        max_temperature_c, min_temperature_c, temperature_uniformity_percent,
                        heating_rate_c_per_sec, cooling_rate_c_per_sec, chamber_volume_liters,
                        wafer_capacity, wafer_sizes_mm, atmosphere_control, vacuum_capability,
                        max_vacuum_torr, throughput_wph, uptime_percent, temperature_stability_c,
                        status, supported_atmospheres, equipment_config, calibration_data
                    ) VALUES (
                        %(equipment_name)s, %(manufacturer)s, %(model)s, %(serial_number)s, %(equipment_type)s,
                        %(max_temperature_c)s, %(min_temperature_c)s, %(temperature_uniformity_percent)s,
                        %(heating_rate_c_per_sec)s, %(cooling_rate_c_per_sec)s, %(chamber_volume_liters)s,
                        %(wafer_capacity)s, %(wafer_sizes_mm)s, %(atmosphere_control)s, %(vacuum_capability)s,
                        %(max_vacuum_torr)s, %(throughput_wph)s, %(uptime_percent)s, %(temperature_stability_c)s,
                        %(status)s, %(supported_atmospheres)s, %(equipment_config)s, %(calibration_data)s
                    ) RETURNING equipment_id
                """, equipment_data)
                
                equipment_id = cursor.fetchone()['equipment_id']
                logger.info(f"Added thermal equipment: {equipment_data['equipment_name']} with ID: {equipment_id}")
                return str(equipment_id)
                
        except Exception as e:
            logger.error(f"Failed to add thermal equipment: {e}")
            raise
    
    def get_thermal_equipment(self, equipment_id: str) -> Optional[Dict[str, Any]]:
        """Get thermal equipment by ID"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.thermal_equipment 
                    WHERE equipment_id = %s
                """, (equipment_id,))
                
                result = cursor.fetchone()
                if result:
                    return dict(result)
                return None
                
        except Exception as e:
            logger.error(f"Failed to get thermal equipment {equipment_id}: {e}")
            return None
    
    def get_equipment_by_type(self, equipment_type: str) -> List[Dict[str, Any]]:
        """Get thermal equipment by type"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.thermal_equipment 
                    WHERE equipment_type = %s AND status = 'operational'
                    ORDER BY equipment_name
                """, (equipment_type,))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"Failed to get equipment by type {equipment_type}: {e}")
            return []
    
    def update_equipment_status(self, equipment_id: str, status: str, notes: str = None):
        """Update equipment status"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    UPDATE thermal.thermal_equipment 
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE equipment_id = %s
                """, (status, equipment_id))
                
                logger.info(f"Updated equipment {equipment_id} status to {status}")
                
        except Exception as e:
            logger.error(f"Failed to update equipment status: {e}")
            raise

    # =====================================================
    # THERMAL PROCESS MANAGEMENT
    # =====================================================

    def create_thermal_process(self, process_data: Dict[str, Any]) -> str:
        """Create thermal process record"""
        try:
            process_id = str(uuid.uuid4())
            process_data['process_id'] = process_id

            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO thermal.thermal_processes (
                        process_id, process_name, process_type, wafer_id, equipment_id,
                        target_temperature_c, ramp_up_rate_c_per_sec, hold_time_seconds,
                        ramp_down_rate_c_per_sec, atmosphere_type, gas_flow_rates,
                        pressure_torr, temperature_profile, power_profile, boundary_conditions,
                        feedback_control, control_algorithm, operator_id, recipe_id,
                        batch_id, lot_id, notes
                    ) VALUES (
                        %(process_id)s, %(process_name)s, %(process_type)s, %(wafer_id)s, %(equipment_id)s,
                        %(target_temperature_c)s, %(ramp_up_rate_c_per_sec)s, %(hold_time_seconds)s,
                        %(ramp_down_rate_c_per_sec)s, %(atmosphere_type)s, %(gas_flow_rates)s,
                        %(pressure_torr)s, %(temperature_profile)s, %(power_profile)s, %(boundary_conditions)s,
                        %(feedback_control)s, %(control_algorithm)s, %(operator_id)s, %(recipe_id)s,
                        %(batch_id)s, %(lot_id)s, %(notes)s
                    )
                """, process_data)

                logger.info(f"Created thermal process: {process_data['process_name']} with ID: {process_id}")
                return process_id

        except Exception as e:
            logger.error(f"Failed to create thermal process: {e}")
            raise

    def start_thermal_process(self, process_id: str):
        """Mark thermal process as started"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    UPDATE thermal.thermal_processes
                    SET start_time = CURRENT_TIMESTAMP
                    WHERE process_id = %s
                """, (process_id,))

                logger.info(f"Started thermal process: {process_id}")

        except Exception as e:
            logger.error(f"Failed to start thermal process: {e}")
            raise

    def complete_thermal_process(self, process_id: str, results: Dict[str, Any]):
        """Complete thermal process with results"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    UPDATE thermal.thermal_processes
                    SET end_time = CURRENT_TIMESTAMP,
                        total_duration_seconds = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - start_time)),
                        actual_temperature_c = %(actual_temperature_c)s,
                        process_success = %(process_success)s,
                        uniformity_achieved_percent = %(uniformity_achieved_percent)s,
                        temperature_deviation_c = %(temperature_deviation_c)s
                    WHERE process_id = %s
                """, {**results, 'process_id': process_id})

                logger.info(f"Completed thermal process: {process_id}")

        except Exception as e:
            logger.error(f"Failed to complete thermal process: {e}")
            raise

    def get_thermal_process(self, process_id: str) -> Optional[Dict[str, Any]]:
        """Get thermal process by ID"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.thermal_processes
                    WHERE process_id = %s
                """, (process_id,))

                result = cursor.fetchone()
                if result:
                    return dict(result)
                return None

        except Exception as e:
            logger.error(f"Failed to get thermal process {process_id}: {e}")
            return None

    def get_processes_by_wafer(self, wafer_id: str) -> List[Dict[str, Any]]:
        """Get thermal processes for a wafer"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.thermal_processes
                    WHERE wafer_id = %s
                    ORDER BY created_at
                """, (wafer_id,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Failed to get processes for wafer {wafer_id}: {e}")
            return []

    # =====================================================
    # THERMAL SIMULATION RESULTS MANAGEMENT
    # =====================================================

    def save_thermal_simulation_results(self, process_id: str, results: Dict[str, Any]) -> str:
        """Save thermal simulation results"""
        try:
            result_id = str(uuid.uuid4())

            # Compress large arrays for storage
            compressed_data = {}
            for field in ['temperature_field', 'temperature_gradient', 'heat_flux_field', 'thermal_stress_field']:
                if field in results and results[field] is not None:
                    if isinstance(results[field], np.ndarray):
                        compressed_data[field] = gzip.compress(pickle.dumps(results[field]))
                    else:
                        compressed_data[field] = gzip.compress(pickle.dumps(np.array(results[field])))

            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO thermal.thermal_simulation_results (
                        result_id, process_id, simulation_engine, simulation_version,
                        grid_resolution, convergence_criteria, max_temperature_c,
                        min_temperature_c, avg_temperature_c, temperature_uniformity_percent,
                        temperature_field, temperature_gradient, heat_flux_field,
                        thermal_stress_field, max_thermal_stress_pa, stress_concentration_factor,
                        hot_spots, hot_spot_count, max_hot_spot_temp_c,
                        junction_to_case_resistance, junction_to_ambient_resistance,
                        effective_thermal_conductivity, thermal_time_constant_s,
                        settling_time_s, overshoot_percent, simulation_accuracy_percent,
                        convergence_achieved, computation_time_seconds,
                        experimental_correlation, validation_points
                    ) VALUES (
                        %(result_id)s, %(process_id)s, %(simulation_engine)s, %(simulation_version)s,
                        %(grid_resolution)s, %(convergence_criteria)s, %(max_temperature_c)s,
                        %(min_temperature_c)s, %(avg_temperature_c)s, %(temperature_uniformity_percent)s,
                        %(temperature_field)s, %(temperature_gradient)s, %(heat_flux_field)s,
                        %(thermal_stress_field)s, %(max_thermal_stress_pa)s, %(stress_concentration_factor)s,
                        %(hot_spots)s, %(hot_spot_count)s, %(max_hot_spot_temp_c)s,
                        %(junction_to_case_resistance)s, %(junction_to_ambient_resistance)s,
                        %(effective_thermal_conductivity)s, %(thermal_time_constant_s)s,
                        %(settling_time_s)s, %(overshoot_percent)s, %(simulation_accuracy_percent)s,
                        %(convergence_achieved)s, %(computation_time_seconds)s,
                        %(experimental_correlation)s, %(validation_points)s
                    )
                """, {
                    'result_id': result_id,
                    'process_id': process_id,
                    'simulation_engine': results.get('simulation_engine', 'advanced_thermal_engine'),
                    'simulation_version': results.get('simulation_version', '1.0'),
                    'grid_resolution': results.get('grid_resolution', '100x100'),
                    'convergence_criteria': results.get('convergence_criteria', 1e-6),
                    'max_temperature_c': results.get('max_temperature_c'),
                    'min_temperature_c': results.get('min_temperature_c'),
                    'avg_temperature_c': results.get('avg_temperature_c'),
                    'temperature_uniformity_percent': results.get('temperature_uniformity_percent'),
                    'temperature_field': compressed_data.get('temperature_field'),
                    'temperature_gradient': compressed_data.get('temperature_gradient'),
                    'heat_flux_field': compressed_data.get('heat_flux_field'),
                    'thermal_stress_field': compressed_data.get('thermal_stress_field'),
                    'max_thermal_stress_pa': results.get('max_thermal_stress_pa'),
                    'stress_concentration_factor': results.get('stress_concentration_factor'),
                    'hot_spots': Json(results.get('hot_spots', [])),
                    'hot_spot_count': results.get('hot_spot_count', 0),
                    'max_hot_spot_temp_c': results.get('max_hot_spot_temp_c'),
                    'junction_to_case_resistance': results.get('junction_to_case_resistance'),
                    'junction_to_ambient_resistance': results.get('junction_to_ambient_resistance'),
                    'effective_thermal_conductivity': results.get('effective_thermal_conductivity'),
                    'thermal_time_constant_s': results.get('thermal_time_constant_s'),
                    'settling_time_s': results.get('settling_time_s'),
                    'overshoot_percent': results.get('overshoot_percent'),
                    'simulation_accuracy_percent': results.get('simulation_accuracy_percent'),
                    'convergence_achieved': results.get('convergence_achieved', True),
                    'computation_time_seconds': results.get('computation_time_seconds'),
                    'experimental_correlation': results.get('experimental_correlation'),
                    'validation_points': Json(results.get('validation_points', []))
                })

                logger.info(f"Saved thermal simulation results with ID: {result_id}")
                return result_id

        except Exception as e:
            logger.error(f"Failed to save thermal simulation results: {e}")
            raise

    def get_thermal_simulation_results(self, result_id: str) -> Optional[Dict[str, Any]]:
        """Get thermal simulation results by ID"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.thermal_simulation_results
                    WHERE result_id = %s
                """, (result_id,))

                result = cursor.fetchone()
                if result:
                    result_dict = dict(result)

                    # Decompress large arrays
                    for field in ['temperature_field', 'temperature_gradient', 'heat_flux_field', 'thermal_stress_field']:
                        if result_dict.get(field):
                            try:
                                result_dict[field] = pickle.loads(gzip.decompress(result_dict[field]))
                            except:
                                result_dict[field] = None

                    return result_dict
                return None

        except Exception as e:
            logger.error(f"Failed to get thermal simulation results {result_id}: {e}")
            return None

    def get_results_by_process(self, process_id: str) -> List[Dict[str, Any]]:
        """Get thermal simulation results for a process"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT result_id, process_id, simulation_engine, max_temperature_c,
                           temperature_uniformity_percent, convergence_achieved,
                           computation_time_seconds, created_at
                    FROM thermal.thermal_simulation_results
                    WHERE process_id = %s
                    ORDER BY created_at DESC
                """, (process_id,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Failed to get results for process {process_id}: {e}")
            return []

    # =====================================================
    # INDUSTRIAL THERMAL RECIPES MANAGEMENT
    # =====================================================

    def add_thermal_recipe(self, recipe_data: Dict[str, Any]) -> str:
        """Add industrial thermal recipe"""
        try:
            recipe_id = str(uuid.uuid4())
            recipe_data['recipe_id'] = recipe_id

            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO thermal.industrial_thermal_recipes (
                        recipe_id, recipe_name, recipe_version, application_type, device_type,
                        process_type, target_temperature_c, ramp_up_rate_c_per_sec,
                        hold_time_seconds, ramp_down_rate_c_per_sec, atmosphere_type,
                        gas_composition, pressure_torr, temperature_uniformity_spec_percent,
                        max_temperature_deviation_c, thermal_budget_c_s, required_equipment_type,
                        min_heating_rate_c_per_sec, min_cooling_rate_c_per_sec,
                        atmosphere_control_required, target_thermal_resistance,
                        target_junction_temp_c, reliability_target_hours, validation_status,
                        validation_runs, success_rate_percent, fab_name, technology_node_nm,
                        industry_standard, recipe_description, safety_notes, troubleshooting_guide
                    ) VALUES (
                        %(recipe_id)s, %(recipe_name)s, %(recipe_version)s, %(application_type)s, %(device_type)s,
                        %(process_type)s, %(target_temperature_c)s, %(ramp_up_rate_c_per_sec)s,
                        %(hold_time_seconds)s, %(ramp_down_rate_c_per_sec)s, %(atmosphere_type)s,
                        %(gas_composition)s, %(pressure_torr)s, %(temperature_uniformity_spec_percent)s,
                        %(max_temperature_deviation_c)s, %(thermal_budget_c_s)s, %(required_equipment_type)s,
                        %(min_heating_rate_c_per_sec)s, %(min_cooling_rate_c_per_sec)s,
                        %(atmosphere_control_required)s, %(target_thermal_resistance)s,
                        %(target_junction_temp_c)s, %(reliability_target_hours)s, %(validation_status)s,
                        %(validation_runs)s, %(success_rate_percent)s, %(fab_name)s, %(technology_node_nm)s,
                        %(industry_standard)s, %(recipe_description)s, %(safety_notes)s, %(troubleshooting_guide)s
                    )
                """, recipe_data)

                logger.info(f"Added thermal recipe: {recipe_data['recipe_name']} with ID: {recipe_id}")
                return recipe_id

        except Exception as e:
            logger.error(f"Failed to add thermal recipe: {e}")
            raise

    def get_thermal_recipe(self, recipe_id: str) -> Optional[Dict[str, Any]]:
        """Get thermal recipe by ID"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.industrial_thermal_recipes
                    WHERE recipe_id = %s
                """, (recipe_id,))

                result = cursor.fetchone()
                if result:
                    return dict(result)
                return None

        except Exception as e:
            logger.error(f"Failed to get thermal recipe {recipe_id}: {e}")
            return None

    def get_recipes_by_application(self, application_type: str) -> List[Dict[str, Any]]:
        """Get thermal recipes by application type"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.industrial_thermal_recipes
                    WHERE application_type = %s AND validation_status = 'validated'
                    ORDER BY recipe_name
                """, (application_type,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Failed to get recipes for application {application_type}: {e}")
            return []

    def get_recipes_by_device_type(self, device_type: str) -> List[Dict[str, Any]]:
        """Get thermal recipes by device type"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    SELECT * FROM thermal.industrial_thermal_recipes
                    WHERE device_type = %s AND validation_status = 'validated'
                    ORDER BY success_rate_percent DESC
                """, (device_type,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Failed to get recipes for device type {device_type}: {e}")
            return []

    # =====================================================
    # THERMAL MONITORING DATA MANAGEMENT
    # =====================================================

    def add_monitoring_data(self, process_id: str, monitoring_data: Dict[str, Any]) -> str:
        """Add thermal monitoring data point"""
        try:
            monitoring_id = str(uuid.uuid4())

            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO thermal.thermal_monitoring (
                        monitoring_id, process_id, measurement_time, elapsed_time_seconds,
                        setpoint_temperature_c, actual_temperature_c, temperature_error_c,
                        temperature_rate_c_per_sec, zone_temperatures, temperature_uniformity,
                        heater_power_w, total_energy_consumed_j, power_efficiency_percent,
                        gas_flow_rates, chamber_pressure_torr, oxygen_partial_pressure_torr,
                        equipment_status, alarm_codes, vibration_level, control_output_percent,
                        pid_parameters
                    ) VALUES (
                        %(monitoring_id)s, %(process_id)s, %(measurement_time)s, %(elapsed_time_seconds)s,
                        %(setpoint_temperature_c)s, %(actual_temperature_c)s, %(temperature_error_c)s,
                        %(temperature_rate_c_per_sec)s, %(zone_temperatures)s, %(temperature_uniformity)s,
                        %(heater_power_w)s, %(total_energy_consumed_j)s, %(power_efficiency_percent)s,
                        %(gas_flow_rates)s, %(chamber_pressure_torr)s, %(oxygen_partial_pressure_torr)s,
                        %(equipment_status)s, %(alarm_codes)s, %(vibration_level)s, %(control_output_percent)s,
                        %(pid_parameters)s
                    )
                """, {
                    'monitoring_id': monitoring_id,
                    'process_id': process_id,
                    'measurement_time': monitoring_data.get('measurement_time', datetime.now()),
                    'elapsed_time_seconds': monitoring_data.get('elapsed_time_seconds'),
                    'setpoint_temperature_c': monitoring_data.get('setpoint_temperature_c'),
                    'actual_temperature_c': monitoring_data.get('actual_temperature_c'),
                    'temperature_error_c': monitoring_data.get('temperature_error_c'),
                    'temperature_rate_c_per_sec': monitoring_data.get('temperature_rate_c_per_sec'),
                    'zone_temperatures': Json(monitoring_data.get('zone_temperatures', {})),
                    'temperature_uniformity': monitoring_data.get('temperature_uniformity'),
                    'heater_power_w': monitoring_data.get('heater_power_w'),
                    'total_energy_consumed_j': monitoring_data.get('total_energy_consumed_j'),
                    'power_efficiency_percent': monitoring_data.get('power_efficiency_percent'),
                    'gas_flow_rates': Json(monitoring_data.get('gas_flow_rates', {})),
                    'chamber_pressure_torr': monitoring_data.get('chamber_pressure_torr'),
                    'oxygen_partial_pressure_torr': monitoring_data.get('oxygen_partial_pressure_torr'),
                    'equipment_status': monitoring_data.get('equipment_status', 'normal'),
                    'alarm_codes': monitoring_data.get('alarm_codes', []),
                    'vibration_level': monitoring_data.get('vibration_level'),
                    'control_output_percent': monitoring_data.get('control_output_percent'),
                    'pid_parameters': Json(monitoring_data.get('pid_parameters', {}))
                })

                return monitoring_id

        except Exception as e:
            logger.error(f"Failed to add monitoring data: {e}")
            raise

    def get_monitoring_data(self, process_id: str,
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get thermal monitoring data for a process"""
        try:
            with self.db.get_cursor() as cursor:
                query = """
                    SELECT * FROM thermal.thermal_monitoring
                    WHERE process_id = %s
                """
                params = [process_id]

                if start_time:
                    query += " AND measurement_time >= %s"
                    params.append(start_time)

                if end_time:
                    query += " AND measurement_time <= %s"
                    params.append(end_time)

                query += " ORDER BY measurement_time"

                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"Failed to get monitoring data for process {process_id}: {e}")
            return []

    # =====================================================
    # UTILITY METHODS
    # =====================================================

    def get_thermal_statistics(self) -> Dict[str, Any]:
        """Get thermal module statistics"""
        try:
            with self.db.get_cursor() as cursor:
                stats = {}

                # Material count by class
                cursor.execute("""
                    SELECT material_class, COUNT(*) as count
                    FROM thermal.thermal_materials
                    GROUP BY material_class
                """)
                stats['materials_by_class'] = dict(cursor.fetchall())

                # Equipment count by type
                cursor.execute("""
                    SELECT equipment_type, COUNT(*) as count
                    FROM thermal.thermal_equipment
                    GROUP BY equipment_type
                """)
                stats['equipment_by_type'] = dict(cursor.fetchall())

                # Process count by type
                cursor.execute("""
                    SELECT process_type, COUNT(*) as count
                    FROM thermal.thermal_processes
                    GROUP BY process_type
                """)
                stats['processes_by_type'] = dict(cursor.fetchall())

                # Recipe count by application
                cursor.execute("""
                    SELECT application_type, COUNT(*) as count
                    FROM thermal.industrial_thermal_recipes
                    GROUP BY application_type
                """)
                stats['recipes_by_application'] = dict(cursor.fetchall())

                return stats

        except Exception as e:
            logger.error(f"Failed to get thermal statistics: {e}")
            return {}

    def cleanup_old_monitoring_data(self, days_to_keep: int = 30):
        """Clean up old monitoring data"""
        try:
            with self.db.get_cursor() as cursor:
                cursor.execute("""
                    DELETE FROM thermal.thermal_monitoring
                    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '%s days'
                """, (days_to_keep,))

                deleted_count = cursor.rowcount
                logger.info(f"Cleaned up {deleted_count} old monitoring records")
                return deleted_count

        except Exception as e:
            logger.error(f"Failed to cleanup monitoring data: {e}")
            return 0
