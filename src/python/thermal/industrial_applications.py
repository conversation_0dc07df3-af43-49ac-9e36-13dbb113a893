"""
Industrial Thermal Applications
==============================

Real industrial thermal examples including CPU cooling, power electronics,
LED thermal management, and data center applications with actual specifications
and performance data.

Author: Dr. <PERSON><PERSON><PERSON>
"""

import numpy as np
from typing import Dict, List, Any, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class IndustrialApplicationType(Enum):
    """Industrial thermal application types"""
    CPU_COOLING = "cpu_cooling"
    GPU_COOLING = "gpu_cooling"
    POWER_ELECTRONICS = "power_electronics"
    LED_THERMAL_MANAGEMENT = "led_thermal_management"
    AUTOMOTIVE_ELECTRONICS = "automotive_electronics"
    DATA_CENTER_COOLING = "data_center_cooling"
    BATTERY_THERMAL_MANAGEMENT = "battery_thermal_management"
    RF_POWER_AMPLIFIER = "rf_power_amplifier"
    MEMS_THERMAL = "mems_thermal"

class IndustrialThermalApplications:
    """Industrial thermal applications with real-world examples"""
    
    def __init__(self):
        """Initialize industrial thermal applications"""
        self.applications = self._load_industrial_applications()
    
    def _load_industrial_applications(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load real industrial thermal applications"""
        return {
            IndustrialApplicationType.CPU_COOLING.value: [
                {
                    "name": "Intel Core i9-13900K Desktop CPU",
                    "manufacturer": "Intel",
                    "specifications": {
                        "tdp": 125.0,  # Watts
                        "max_tdp": 253.0,  # Watts (PL2)
                        "die_size_mm2": 257.0,
                        "package_type": "LGA1700",
                        "max_junction_temp": 100.0,  # °C
                        "base_clock_ghz": 3.0,
                        "boost_clock_ghz": 5.8,
                        "cores": 24,
                        "process_node_nm": 10
                    },
                    "thermal_characteristics": {
                        "junction_to_case_resistance": 0.15,  # K/W
                        "case_to_ambient_resistance": 0.3,  # K/W (with good cooler)
                        "power_density_w_cm2": 48.6,
                        "hotspot_locations": [
                            {"x": 0.3, "y": 0.3, "relative_temp_increase": 15.0},
                            {"x": 0.7, "y": 0.3, "relative_temp_increase": 12.0},
                            {"x": 0.3, "y": 0.7, "relative_temp_increase": 10.0},
                            {"x": 0.7, "y": 0.7, "relative_temp_increase": 8.0}
                        ]
                    },
                    "cooling_solutions": [
                        {
                            "type": "air_cooler",
                            "thermal_resistance": 0.25,  # K/W
                            "max_tdp_rating": 180.0,
                            "noise_level_db": 35.0
                        },
                        {
                            "type": "liquid_cooler_240mm",
                            "thermal_resistance": 0.15,  # K/W
                            "max_tdp_rating": 300.0,
                            "noise_level_db": 28.0
                        },
                        {
                            "type": "liquid_cooler_360mm",
                            "thermal_resistance": 0.12,  # K/W
                            "max_tdp_rating": 400.0,
                            "noise_level_db": 25.0
                        }
                    ],
                    "industrial_context": {
                        "applications": ["Gaming", "Content Creation", "Workstations"],
                        "market_segment": "High-Performance Desktop",
                        "thermal_challenges": ["High power density", "Boost clock thermal limits", "Package constraints"],
                        "standards": ["JEDEC JESD51", "Intel Thermal Specifications"]
                    }
                },
                {
                    "name": "AMD Ryzen 9 7950X Desktop CPU",
                    "manufacturer": "AMD",
                    "specifications": {
                        "tdp": 170.0,  # Watts
                        "max_tdp": 230.0,  # Watts (PPT)
                        "die_size_mm2": 70.0,  # Per CCD
                        "package_type": "AM5",
                        "max_junction_temp": 95.0,  # °C
                        "base_clock_ghz": 4.5,
                        "boost_clock_ghz": 5.7,
                        "cores": 16,
                        "process_node_nm": 5
                    },
                    "thermal_characteristics": {
                        "junction_to_case_resistance": 0.18,  # K/W
                        "case_to_ambient_resistance": 0.28,  # K/W
                        "power_density_w_cm2": 242.8,  # Very high due to small die
                        "hotspot_locations": [
                            {"x": 0.25, "y": 0.5, "relative_temp_increase": 20.0},  # CCD1
                            {"x": 0.75, "y": 0.5, "relative_temp_increase": 18.0}   # CCD2
                        ]
                    },
                    "cooling_solutions": [
                        {
                            "type": "high_end_air_cooler",
                            "thermal_resistance": 0.22,  # K/W
                            "max_tdp_rating": 200.0,
                            "noise_level_db": 38.0
                        },
                        {
                            "type": "liquid_cooler_280mm",
                            "thermal_resistance": 0.14,  # K/W
                            "max_tdp_rating": 350.0,
                            "noise_level_db": 30.0
                        }
                    ],
                    "industrial_context": {
                        "applications": ["Professional Workstations", "Server", "High-Performance Computing"],
                        "market_segment": "Enthusiast/Professional",
                        "thermal_challenges": ["Extreme power density", "Multi-die thermal management", "Precision boost algorithms"],
                        "standards": ["JEDEC JESD51", "AMD Thermal Specifications"]
                    }
                }
            ],
            
            IndustrialApplicationType.GPU_COOLING.value: [
                {
                    "name": "NVIDIA RTX 4090 Graphics Card",
                    "manufacturer": "NVIDIA",
                    "specifications": {
                        "tdp": 450.0,  # Watts
                        "die_size_mm2": 608.0,
                        "memory_size_gb": 24,
                        "memory_type": "GDDR6X",
                        "max_gpu_temp": 83.0,  # °C
                        "base_clock_mhz": 2230,
                        "boost_clock_mhz": 2520,
                        "process_node_nm": 4,
                        "transistors_billion": 76.3
                    },
                    "thermal_characteristics": {
                        "junction_to_case_resistance": 0.08,  # K/W
                        "power_density_w_cm2": 74.0,
                        "memory_power_w": 80.0,
                        "hotspot_locations": [
                            {"x": 0.5, "y": 0.5, "relative_temp_increase": 25.0},  # GPU core
                            {"x": 0.2, "y": 0.8, "relative_temp_increase": 15.0},  # Memory controller 1
                            {"x": 0.8, "y": 0.8, "relative_temp_increase": 15.0},  # Memory controller 2
                            {"x": 0.2, "y": 0.2, "relative_temp_increase": 12.0},  # Memory controller 3
                            {"x": 0.8, "y": 0.2, "relative_temp_increase": 12.0}   # Memory controller 4
                        ]
                    },
                    "cooling_solutions": [
                        {
                            "type": "triple_fan_air_cooler",
                            "thermal_resistance": 0.12,  # K/W
                            "fan_count": 3,
                            "noise_level_db": 45.0,
                            "max_fan_rpm": 2500
                        },
                        {
                            "type": "liquid_cooler_custom",
                            "thermal_resistance": 0.06,  # K/W
                            "pump_flow_rate_lpm": 15.0,
                            "noise_level_db": 35.0
                        }
                    ],
                    "industrial_context": {
                        "applications": ["Gaming", "AI/ML Training", "Professional Rendering", "Cryptocurrency Mining"],
                        "market_segment": "High-End Consumer/Professional",
                        "thermal_challenges": ["Extreme power density", "Memory thermal management", "Multi-GPU configurations"],
                        "standards": ["PCI-SIG Thermal Specifications", "NVIDIA Thermal Guidelines"]
                    }
                }
            ],
            
            IndustrialApplicationType.POWER_ELECTRONICS.value: [
                {
                    "name": "Infineon IGBT Module FF450R12ME4",
                    "manufacturer": "Infineon",
                    "specifications": {
                        "device_type": "IGBT",
                        "voltage_rating_v": 1200,
                        "current_rating_a": 450,
                        "switching_frequency_khz": 20.0,
                        "package_type": "FF",
                        "max_junction_temp": 175.0,  # °C
                        "vce_sat_v": 1.7,  # At rated current
                        "switching_energy_mj": 180.0  # Per switch
                    },
                    "thermal_characteristics": {
                        "junction_to_case_resistance": 0.028,  # K/W per IGBT
                        "case_to_heatsink_resistance": 0.005,  # K/W with TIM
                        "power_losses": {
                            "conduction_loss_w": 765.0,  # At rated current
                            "switching_loss_w": 72.0,    # At 20kHz
                            "total_loss_w": 837.0
                        },
                        "hotspot_locations": [
                            {"x": 0.3, "y": 0.5, "relative_temp_increase": 30.0},  # IGBT 1
                            {"x": 0.7, "y": 0.5, "relative_temp_increase": 28.0}   # IGBT 2
                        ]
                    },
                    "cooling_solutions": [
                        {
                            "type": "forced_air_heatsink",
                            "thermal_resistance": 0.15,  # K/W
                            "airflow_cfm": 200,
                            "heatsink_mass_kg": 2.5
                        },
                        {
                            "type": "liquid_cooled_baseplate",
                            "thermal_resistance": 0.08,  # K/W
                            "coolant_flow_rate_lpm": 10.0,
                            "coolant_temp_c": 65.0
                        }
                    ],
                    "industrial_context": {
                        "applications": ["Motor Drives", "UPS Systems", "Renewable Energy Inverters", "Traction Drives"],
                        "market_segment": "Industrial Power Electronics",
                        "thermal_challenges": ["High power density", "Thermal cycling", "Reliability requirements"],
                        "standards": ["IEC 60747-9", "JEDEC JESD51-14"]
                    }
                }
            ],
            
            IndustrialApplicationType.LED_THERMAL_MANAGEMENT.value: [
                {
                    "name": "Cree XHP70.2 High-Power LED",
                    "manufacturer": "Cree",
                    "specifications": {
                        "forward_current_ma": 1050,  # Per die
                        "forward_voltage_v": 3.2,
                        "luminous_flux_lm": 2546,
                        "luminous_efficacy_lm_w": 190.0,
                        "max_junction_temp": 150.0,  # °C
                        "thermal_resistance_k_w": 1.48,  # Junction to solder point
                        "die_count": 4,
                        "package_size_mm": [7.0, 7.0]
                    },
                    "thermal_characteristics": {
                        "electrical_power_w": 13.44,  # 4 dies × 3.36W
                        "optical_power_w": 3.73,
                        "heat_generation_w": 9.71,
                        "thermal_droop_percent_per_c": 0.2,
                        "color_shift_nm_per_c": 0.1,
                        "hotspot_locations": [
                            {"x": 0.25, "y": 0.25, "relative_temp_increase": 20.0},  # Die 1
                            {"x": 0.75, "y": 0.25, "relative_temp_increase": 18.0},  # Die 2
                            {"x": 0.25, "y": 0.75, "relative_temp_increase": 18.0},  # Die 3
                            {"x": 0.75, "y": 0.75, "relative_temp_increase": 16.0}   # Die 4
                        ]
                    },
                    "cooling_solutions": [
                        {
                            "type": "mcpcb_passive",
                            "thermal_resistance": 8.0,  # K/W (MCPCB to ambient)
                            "pcb_thickness_mm": 1.6,
                            "thermal_via_count": 25
                        },
                        {
                            "type": "active_cooling_fan",
                            "thermal_resistance": 4.0,  # K/W
                            "airflow_cfm": 50,
                            "noise_level_db": 30.0
                        },
                        {
                            "type": "liquid_cooling_plate",
                            "thermal_resistance": 2.0,  # K/W
                            "coolant_flow_rate_lpm": 2.0,
                            "pump_power_w": 5.0
                        }
                    ],
                    "industrial_context": {
                        "applications": ["Automotive Headlights", "Stadium Lighting", "Industrial Lighting", "Horticultural Lighting"],
                        "market_segment": "High-Power LED Lighting",
                        "thermal_challenges": ["Thermal droop", "Color stability", "Long-term reliability"],
                        "standards": ["IES LM-80", "JEDEC JESD51-51", "IEC 62717"]
                    }
                }
            ]
        }
    
    def get_application_by_type(self, app_type: IndustrialApplicationType) -> List[Dict[str, Any]]:
        """Get applications by type"""
        return self.applications.get(app_type.value, [])
    
    def get_application_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """Get specific application by name"""
        for app_list in self.applications.values():
            for app in app_list:
                if app["name"] == name:
                    return app
        return None
    
    def get_all_applications(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all industrial applications"""
        return self.applications
    
    def calculate_thermal_performance(self, application: Dict[str, Any], 
                                    cooling_solution: str,
                                    ambient_temp: float = 25.0) -> Dict[str, Any]:
        """Calculate thermal performance for given application and cooling solution"""
        try:
            specs = application.get("specifications", {})
            thermal_chars = application.get("thermal_characteristics", {})
            cooling_solutions = application.get("cooling_solutions", [])
            
            # Find cooling solution
            cooling_data = None
            for solution in cooling_solutions:
                if solution.get("type") == cooling_solution:
                    cooling_data = solution
                    break
            
            if not cooling_data:
                return {"error": f"Cooling solution '{cooling_solution}' not found"}
            
            # Calculate temperatures
            power_dissipation = specs.get("tdp", thermal_chars.get("heat_generation_w", 100.0))
            thermal_resistance = cooling_data.get("thermal_resistance", 1.0)
            
            junction_temp = ambient_temp + (power_dissipation * thermal_resistance)
            max_temp = specs.get("max_junction_temp", specs.get("max_gpu_temp", 100.0))
            
            # Calculate performance metrics
            thermal_margin = max_temp - junction_temp
            thermal_utilization = (junction_temp - ambient_temp) / (max_temp - ambient_temp) * 100
            
            # Calculate hotspot temperatures
            hotspots = []
            for hotspot in thermal_chars.get("hotspot_locations", []):
                hotspot_temp = junction_temp + hotspot.get("relative_temp_increase", 0)
                hotspots.append({
                    "x": hotspot.get("x", 0),
                    "y": hotspot.get("y", 0),
                    "temperature": hotspot_temp,
                    "area": 1.0
                })
            
            return {
                "junction_temperature": junction_temp,
                "max_temperature": max_temp,
                "thermal_margin": thermal_margin,
                "thermal_utilization_percent": thermal_utilization,
                "meets_requirements": junction_temp <= max_temp,
                "power_dissipation": power_dissipation,
                "thermal_resistance": thermal_resistance,
                "cooling_solution": cooling_solution,
                "hotspots": hotspots,
                "performance_rating": "Excellent" if thermal_margin > 20 else 
                                   "Good" if thermal_margin > 10 else 
                                   "Marginal" if thermal_margin > 0 else "Inadequate"
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate thermal performance: {e}")
            return {"error": str(e)}
    
    def generate_thermal_report(self, application_name: str, 
                               cooling_solution: str,
                               ambient_temp: float = 25.0) -> str:
        """Generate comprehensive thermal report"""
        application = self.get_application_by_name(application_name)
        if not application:
            return f"Application '{application_name}' not found"
        
        performance = self.calculate_thermal_performance(application, cooling_solution, ambient_temp)
        if "error" in performance:
            return f"Error calculating performance: {performance['error']}"
        
        report = f"""
THERMAL ANALYSIS REPORT
======================

Application: {application['name']}
Manufacturer: {application.get('manufacturer', 'Unknown')}
Cooling Solution: {cooling_solution}
Ambient Temperature: {ambient_temp}°C

THERMAL PERFORMANCE
------------------
Junction Temperature: {performance['junction_temperature']:.1f}°C
Maximum Temperature: {performance['max_temperature']:.1f}°C
Thermal Margin: {performance['thermal_margin']:.1f}°C
Thermal Utilization: {performance['thermal_utilization_percent']:.1f}%
Performance Rating: {performance['performance_rating']}
Requirements Met: {'Yes' if performance['meets_requirements'] else 'No'}

POWER CHARACTERISTICS
--------------------
Power Dissipation: {performance['power_dissipation']:.1f}W
Thermal Resistance: {performance['thermal_resistance']:.3f}K/W

HOT SPOT ANALYSIS
----------------
Number of Hot Spots: {len(performance['hotspots'])}
"""
        
        for i, hotspot in enumerate(performance['hotspots'][:5]):  # Show first 5
            report += f"Hot Spot {i+1}: ({hotspot['x']:.2f}, {hotspot['y']:.2f}) - {hotspot['temperature']:.1f}°C\n"
        
        # Add industrial context
        context = application.get("industrial_context", {})
        if context:
            report += f"""
INDUSTRIAL CONTEXT
-----------------
Applications: {', '.join(context.get('applications', []))}
Market Segment: {context.get('market_segment', 'Unknown')}
Thermal Challenges: {', '.join(context.get('thermal_challenges', []))}
Standards: {', '.join(context.get('standards', []))}
"""
        
        return report
