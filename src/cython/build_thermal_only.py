#!/usr/bin/env python3
"""
Build script for thermal modules only
"""

import os
import sys
from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy as np

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
cpp_dir = os.path.join(project_root, "src", "cpp")

# Common configuration
common_include_dirs = [
    current_dir,
    cpp_dir,
    os.path.join(cpp_dir, "core"),
    os.path.join(cpp_dir, "modules"),
    np.get_include(),
    "/usr/include/eigen3",
]

common_library_dirs = [
    "/usr/lib",
    "/usr/local/lib",
]

common_libraries = [
    "stdc++",
    "m",
]

common_extra_compile_args = [
    "-std=c++17",
    "-O3",
    "-fPIC",
    "-DWITH_OPENCV=0",
    "-DWITH_CUDA=0",
]

common_extra_link_args = [
    "-std=c++17",
]

# Define thermal extensions only
thermal_extensions = [
    Extension(
        "thermal",
        ["thermal.pyx"],
        language="c++",
        include_dirs=common_include_dirs + [os.path.join(cpp_dir, "modules/thermal")],
        library_dirs=common_library_dirs,
        libraries=common_libraries,
        extra_compile_args=common_extra_compile_args,
        extra_link_args=common_extra_link_args,
    ),
    Extension(
        "advanced_thermal",
        ["advanced_thermal.pyx"],
        language="c++",
        include_dirs=common_include_dirs + [os.path.join(cpp_dir, "modules/thermal")],
        library_dirs=common_library_dirs,
        libraries=common_libraries,
        extra_compile_args=common_extra_compile_args,
        extra_link_args=common_extra_link_args,
    ),
]

if __name__ == "__main__":
    setup(
        name="SemiPRO Thermal Modules",
        ext_modules=cythonize(thermal_extensions, compiler_directives={'language_level': 3}),
        zip_safe=False,
    )
