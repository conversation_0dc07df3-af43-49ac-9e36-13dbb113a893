-- =====================================================
-- THERMAL MODULE DATABASE SCHEMA
-- =====================================================
-- Comprehensive PostgreSQL schema for thermal processing module
-- Supporting industrial thermal management, materials database,
-- equipment tracking, and advanced thermal simulations
--
-- Author: Dr. <PERSON><PERSON><PERSON> Mohammed
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create thermal schema
CREATE SCHEMA IF NOT EXISTS thermal;
SET search_path TO thermal, public;

-- =====================================================
-- THERMAL EQUIPMENT AND FACILITIES
-- =====================================================

-- Thermal processing equipment database
CREATE TABLE thermal_equipment (
    equipment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Equipment identification
    equipment_name VARCHAR(255) NOT NULL,
    manufacturer VARCHAR(255),
    model VARCHAR(255),
    serial_number VARCHAR(100),
    equipment_type VARCHAR(50) NOT NULL, -- 'RTP', 'FURNACE', 'LASER_ANNEALER', 'FLASH_ANNEALER'
    
    -- Technical specifications
    max_temperature_c DECIMAL(8,2) NOT NULL,
    min_temperature_c DECIMAL(8,2) DEFAULT 25.0,
    temperature_uniformity_percent DECIMAL(5,2),
    heating_rate_c_per_sec DECIMAL(8,3),
    cooling_rate_c_per_sec DECIMAL(8,3),
    
    -- Chamber specifications
    chamber_volume_liters DECIMAL(10,3),
    wafer_capacity INTEGER DEFAULT 1,
    wafer_sizes_mm INTEGER[] NOT NULL, -- Supported wafer sizes
    atmosphere_control BOOLEAN DEFAULT TRUE,
    vacuum_capability BOOLEAN DEFAULT FALSE,
    max_vacuum_torr DECIMAL(12,6),
    
    -- Performance characteristics
    throughput_wph DECIMAL(8,2), -- Wafers per hour
    uptime_percent DECIMAL(5,2),
    temperature_stability_c DECIMAL(6,3),
    
    -- Status and maintenance
    status VARCHAR(50) DEFAULT 'operational', -- 'operational', 'maintenance', 'down'
    last_maintenance_date DATE,
    next_maintenance_date DATE,
    calibration_due_date DATE,
    
    -- Configuration and capabilities
    supported_atmospheres TEXT[], -- 'N2', 'O2', 'Ar', 'H2', 'forming_gas'
    equipment_config JSONB,
    calibration_data JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- THERMAL MATERIALS DATABASE
-- =====================================================

-- Comprehensive thermal materials properties database
CREATE TABLE thermal_materials (
    material_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Material identification
    material_name VARCHAR(100) NOT NULL UNIQUE,
    chemical_formula VARCHAR(100),
    material_class VARCHAR(50), -- 'semiconductor', 'metal', 'dielectric', 'polymer', 'ceramic'
    crystal_structure VARCHAR(50), -- 'diamond', 'zincblende', 'wurtzite', 'fcc', 'bcc'
    
    -- Thermal properties
    thermal_conductivity_w_mk DECIMAL(12,6) NOT NULL,
    specific_heat_j_kg_k DECIMAL(10,3) NOT NULL,
    density_kg_m3 DECIMAL(10,3) NOT NULL,
    thermal_expansion_per_k DECIMAL(15,9) NOT NULL,
    melting_point_c DECIMAL(8,2),
    
    -- Optical properties for thermal radiation
    emissivity DECIMAL(6,4) DEFAULT 0.5,
    absorptivity DECIMAL(6,4) DEFAULT 0.5,
    reflectivity DECIMAL(6,4) DEFAULT 0.5,
    
    -- Temperature-dependent properties (JSON arrays)
    thermal_conductivity_vs_temp JSONB, -- [{temp_c: value, conductivity: value}, ...]
    specific_heat_vs_temp JSONB,
    thermal_expansion_vs_temp JSONB,
    
    -- Processing characteristics
    max_processing_temp_c DECIMAL(8,2),
    thermal_stability_rating VARCHAR(50), -- 'excellent', 'good', 'fair', 'poor'
    oxidation_resistance VARCHAR(50),
    
    -- Interface properties
    interface_thermal_resistance DECIMAL(15,9), -- K·m²/W
    surface_roughness_nm DECIMAL(10,3),
    
    -- Data source and validation
    data_source VARCHAR(255),
    measurement_method VARCHAR(255),
    temperature_range_c DECIMAL(6,2)[] NOT NULL, -- [min, max] valid temperature range
    uncertainty_percent DECIMAL(5,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- THERMAL INTERFACE MATERIALS
-- =====================================================

-- Thermal interface materials (TIMs) database
CREATE TABLE thermal_interface_materials (
    tim_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- TIM identification
    tim_name VARCHAR(100) NOT NULL,
    manufacturer VARCHAR(255),
    product_code VARCHAR(100),
    tim_type VARCHAR(50), -- 'thermal_paste', 'thermal_pad', 'phase_change', 'liquid_metal'
    
    -- Thermal properties
    thermal_conductivity_w_mk DECIMAL(10,4) NOT NULL,
    thermal_resistance_k_w DECIMAL(12,6), -- Total thermal resistance
    bond_line_thickness_um DECIMAL(8,2),
    
    -- Physical properties
    viscosity_pa_s DECIMAL(12,6),
    density_g_cm3 DECIMAL(8,4),
    operating_temp_range_c DECIMAL(6,2)[] NOT NULL,
    
    -- Application properties
    pump_out_resistance VARCHAR(50), -- 'excellent', 'good', 'fair', 'poor'
    reworkability VARCHAR(50),
    cure_time_hours DECIMAL(8,2),
    shelf_life_months INTEGER,
    
    -- Performance data
    contact_resistance DECIMAL(15,9), -- K·m²/W
    aging_characteristics JSONB,
    reliability_data JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- THERMAL PROCESSES AND SIMULATIONS
-- =====================================================

-- Central table for all thermal processes
CREATE TABLE thermal_processes (
    process_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Process identification
    process_name VARCHAR(255) NOT NULL,
    process_type VARCHAR(50) NOT NULL, -- 'RTP', 'OXIDATION', 'ANNEALING', 'DIFFUSION', 'SINTERING'
    wafer_id UUID, -- Reference to wafer (from main schema)
    equipment_id UUID REFERENCES thermal_equipment(equipment_id),

    -- Process parameters
    target_temperature_c DECIMAL(8,2) NOT NULL,
    actual_temperature_c DECIMAL(8,2),
    ramp_up_rate_c_per_sec DECIMAL(8,3),
    hold_time_seconds INTEGER NOT NULL,
    ramp_down_rate_c_per_sec DECIMAL(8,3),

    -- Atmosphere control
    atmosphere_type VARCHAR(50), -- 'N2', 'O2', 'Ar', 'H2', 'forming_gas', 'vacuum'
    gas_flow_rates JSONB, -- {gas_name: flow_rate_sccm}
    pressure_torr DECIMAL(12,6),

    -- Advanced parameters
    temperature_profile JSONB, -- Time-temperature profile
    power_profile JSONB, -- Power dissipation profile
    boundary_conditions JSONB, -- Thermal boundary conditions

    -- Process control
    feedback_control BOOLEAN DEFAULT TRUE,
    control_algorithm VARCHAR(50), -- 'PID', 'fuzzy', 'adaptive'
    setpoint_tracking JSONB,

    -- Quality and results
    process_success BOOLEAN,
    uniformity_achieved_percent DECIMAL(5,2),
    temperature_deviation_c DECIMAL(6,3),

    -- Timing
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    total_duration_seconds INTEGER,

    -- Metadata
    operator_id VARCHAR(100),
    recipe_id UUID, -- Reference to industrial recipes
    batch_id VARCHAR(100),
    lot_id VARCHAR(100),
    notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- THERMAL SIMULATION RESULTS
-- =====================================================

-- Detailed thermal simulation results
CREATE TABLE thermal_simulation_results (
    result_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    process_id UUID NOT NULL REFERENCES thermal_processes(process_id) ON DELETE CASCADE,

    -- Simulation metadata
    simulation_engine VARCHAR(50), -- 'advanced_thermal_engine', 'finite_element', 'monte_carlo'
    simulation_version VARCHAR(50),
    grid_resolution VARCHAR(50), -- '100x100', '200x200', 'adaptive'
    convergence_criteria DECIMAL(12,9),

    -- Temperature results
    max_temperature_c DECIMAL(8,2),
    min_temperature_c DECIMAL(8,2),
    avg_temperature_c DECIMAL(8,2),
    temperature_uniformity_percent DECIMAL(5,2),

    -- Spatial temperature data (compressed for large datasets)
    temperature_field BYTEA, -- Compressed 2D temperature field
    temperature_gradient BYTEA, -- Compressed temperature gradient field
    heat_flux_field BYTEA, -- Compressed heat flux field

    -- Thermal stress analysis
    thermal_stress_field BYTEA, -- Compressed thermal stress field
    max_thermal_stress_pa DECIMAL(15,6),
    stress_concentration_factor DECIMAL(8,4),

    -- Hot spot analysis
    hot_spots JSONB, -- [{x: coord, y: coord, temperature: value, area: value}, ...]
    hot_spot_count INTEGER DEFAULT 0,
    max_hot_spot_temp_c DECIMAL(8,2),

    -- Thermal resistance analysis
    junction_to_case_resistance DECIMAL(12,6), -- K/W
    junction_to_ambient_resistance DECIMAL(12,6), -- K/W
    effective_thermal_conductivity DECIMAL(12,6), -- W/(m·K)

    -- Transient analysis (if applicable)
    thermal_time_constant_s DECIMAL(12,6),
    settling_time_s DECIMAL(12,6),
    overshoot_percent DECIMAL(5,2),

    -- Quality metrics
    simulation_accuracy_percent DECIMAL(5,2),
    convergence_achieved BOOLEAN DEFAULT TRUE,
    computation_time_seconds DECIMAL(10,3),

    -- Validation against experimental data
    experimental_correlation DECIMAL(6,4), -- R-squared value
    validation_points JSONB, -- Comparison points with experimental data

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- REAL-TIME MONITORING DATA
-- =====================================================

-- Real-time thermal process monitoring
CREATE TABLE thermal_monitoring (
    monitoring_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    process_id UUID NOT NULL REFERENCES thermal_processes(process_id) ON DELETE CASCADE,

    -- Timestamp
    measurement_time TIMESTAMP WITH TIME ZONE NOT NULL,
    elapsed_time_seconds INTEGER NOT NULL,

    -- Temperature measurements
    setpoint_temperature_c DECIMAL(8,2),
    actual_temperature_c DECIMAL(8,2),
    temperature_error_c DECIMAL(8,2),
    temperature_rate_c_per_sec DECIMAL(8,3),

    -- Multi-zone temperatures (for multi-zone equipment)
    zone_temperatures JSONB, -- {zone_id: temperature_c}
    temperature_uniformity DECIMAL(5,2),

    -- Power and energy
    heater_power_w DECIMAL(10,2),
    total_energy_consumed_j DECIMAL(15,3),
    power_efficiency_percent DECIMAL(5,2),

    -- Atmosphere monitoring
    gas_flow_rates JSONB, -- {gas_name: actual_flow_sccm}
    chamber_pressure_torr DECIMAL(12,6),
    oxygen_partial_pressure_torr DECIMAL(15,9),

    -- Equipment status
    equipment_status VARCHAR(50), -- 'normal', 'warning', 'alarm', 'fault'
    alarm_codes TEXT[],
    vibration_level DECIMAL(8,4),

    -- Process control
    control_output_percent DECIMAL(5,2),
    pid_parameters JSONB, -- {kp: value, ki: value, kd: value}

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDUSTRIAL THERMAL RECIPES
-- =====================================================

-- Proven industrial thermal processing recipes
CREATE TABLE industrial_thermal_recipes (
    recipe_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Recipe identification
    recipe_name VARCHAR(255) NOT NULL,
    recipe_version VARCHAR(50),
    application_type VARCHAR(100), -- 'CPU_COOLING', 'POWER_ELECTRONICS', 'LED_THERMAL', etc.
    device_type VARCHAR(100), -- 'MOSFET', 'IGBT', 'LED', 'CPU', 'GPU'

    -- Process specification
    process_type VARCHAR(50) NOT NULL,
    target_temperature_c DECIMAL(8,2) NOT NULL,
    ramp_up_rate_c_per_sec DECIMAL(8,3),
    hold_time_seconds INTEGER NOT NULL,
    ramp_down_rate_c_per_sec DECIMAL(8,3),

    -- Atmosphere specification
    atmosphere_type VARCHAR(50),
    gas_composition JSONB, -- {gas_name: percentage}
    pressure_torr DECIMAL(12,6),

    -- Quality specifications
    temperature_uniformity_spec_percent DECIMAL(5,2),
    max_temperature_deviation_c DECIMAL(6,3),
    thermal_budget_c_s DECIMAL(15,3), -- Temperature-time integral

    -- Equipment requirements
    required_equipment_type VARCHAR(50),
    min_heating_rate_c_per_sec DECIMAL(8,3),
    min_cooling_rate_c_per_sec DECIMAL(8,3),
    atmosphere_control_required BOOLEAN DEFAULT TRUE,

    -- Performance targets
    target_thermal_resistance DECIMAL(12,6), -- K/W
    target_junction_temp_c DECIMAL(8,2),
    reliability_target_hours INTEGER,

    -- Validation data
    validation_status VARCHAR(50), -- 'validated', 'under_test', 'preliminary'
    validation_runs INTEGER DEFAULT 0,
    success_rate_percent DECIMAL(5,2),

    -- Industrial data
    fab_name VARCHAR(255),
    technology_node_nm INTEGER,
    industry_standard VARCHAR(100), -- 'JEDEC', 'IPC', 'SEMI'

    -- Documentation
    recipe_description TEXT,
    safety_notes TEXT,
    troubleshooting_guide TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- THERMAL DEVICE APPLICATIONS
-- =====================================================

-- Industrial thermal device applications database
CREATE TABLE thermal_device_applications (
    application_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),

    -- Application identification
    application_name VARCHAR(255) NOT NULL,
    device_category VARCHAR(100), -- 'CPU', 'GPU', 'Power_MOSFET', 'LED', 'IGBT'
    industry_sector VARCHAR(100), -- 'automotive', 'data_center', 'consumer', 'industrial'

    -- Thermal specifications
    max_junction_temp_c DECIMAL(8,2) NOT NULL,
    thermal_design_power_w DECIMAL(10,2) NOT NULL,
    package_thermal_resistance DECIMAL(12,6), -- K/W

    -- Operating conditions
    ambient_temp_range_c DECIMAL(6,2)[] NOT NULL,
    cooling_method VARCHAR(100), -- 'air_cooling', 'liquid_cooling', 'heat_sink', 'TEC'
    power_dissipation_profile JSONB,

    -- Performance requirements
    reliability_target_fit DECIMAL(12,6), -- Failures in time
    operating_lifetime_hours INTEGER,
    thermal_cycling_spec JSONB,

    -- Design constraints
    package_size_mm DECIMAL(6,2)[] NOT NULL, -- [length, width, height]
    weight_constraint_g DECIMAL(8,3),
    cost_target_usd DECIMAL(10,2),

    -- Simulation parameters
    default_simulation_config JSONB,
    recommended_materials JSONB,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Equipment indexes
CREATE INDEX idx_thermal_equipment_type ON thermal_equipment(equipment_type);
CREATE INDEX idx_thermal_equipment_status ON thermal_equipment(status);
CREATE INDEX idx_thermal_equipment_maintenance ON thermal_equipment(next_maintenance_date);

-- Materials indexes
CREATE INDEX idx_thermal_materials_class ON thermal_materials(material_class);
CREATE INDEX idx_thermal_materials_conductivity ON thermal_materials(thermal_conductivity_w_mk);
CREATE INDEX idx_thermal_materials_temp_range ON thermal_materials USING GIN(temperature_range_c);

-- Process indexes
CREATE INDEX idx_thermal_processes_type ON thermal_processes(process_type);
CREATE INDEX idx_thermal_processes_wafer ON thermal_processes(wafer_id);
CREATE INDEX idx_thermal_processes_equipment ON thermal_processes(equipment_id);
CREATE INDEX idx_thermal_processes_time ON thermal_processes(start_time);
CREATE INDEX idx_thermal_processes_batch ON thermal_processes(batch_id);

-- Results indexes
CREATE INDEX idx_thermal_results_process ON thermal_simulation_results(process_id);
CREATE INDEX idx_thermal_results_max_temp ON thermal_simulation_results(max_temperature_c);
CREATE INDEX idx_thermal_results_uniformity ON thermal_simulation_results(temperature_uniformity_percent);

-- Monitoring indexes
CREATE INDEX idx_thermal_monitoring_process ON thermal_monitoring(process_id);
CREATE INDEX idx_thermal_monitoring_time ON thermal_monitoring(measurement_time);
CREATE INDEX idx_thermal_monitoring_temp ON thermal_monitoring(actual_temperature_c);

-- Recipe indexes
CREATE INDEX idx_thermal_recipes_application ON industrial_thermal_recipes(application_type);
CREATE INDEX idx_thermal_recipes_device ON industrial_thermal_recipes(device_type);
CREATE INDEX idx_thermal_recipes_validation ON industrial_thermal_recipes(validation_status);

-- Application indexes
CREATE INDEX idx_thermal_applications_category ON thermal_device_applications(device_category);
CREATE INDEX idx_thermal_applications_industry ON thermal_device_applications(industry_sector);

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample thermal materials
INSERT INTO thermal_materials (material_name, chemical_formula, material_class,
                              thermal_conductivity_w_mk, specific_heat_j_kg_k, density_kg_m3,
                              thermal_expansion_per_k, temperature_range_c) VALUES
('Silicon', 'Si', 'semiconductor', 150.0, 700.0, 2330.0, 2.6e-6, ARRAY[25.0, 1400.0]),
('Copper', 'Cu', 'metal', 400.0, 385.0, 8960.0, 16.5e-6, ARRAY[25.0, 1000.0]),
('Aluminum', 'Al', 'metal', 237.0, 897.0, 2700.0, 23.1e-6, ARRAY[25.0, 600.0]),
('Silicon Dioxide', 'SiO2', 'dielectric', 1.4, 730.0, 2200.0, 0.5e-6, ARRAY[25.0, 1200.0]),
('Gallium Nitride', 'GaN', 'semiconductor', 130.0, 490.0, 6150.0, 5.6e-6, ARRAY[25.0, 1500.0]),
('Silicon Carbide', 'SiC', 'semiconductor', 490.0, 675.0, 3210.0, 4.2e-6, ARRAY[25.0, 1800.0]);

-- Insert sample thermal interface materials
INSERT INTO thermal_interface_materials (tim_name, manufacturer, tim_type,
                                       thermal_conductivity_w_mk, operating_temp_range_c) VALUES
('Arctic Silver 5', 'Arctic Silver', 'thermal_paste', 8.7, ARRAY[-50.0, 130.0]),
('Thermal Grizzly Kryonaut', 'Thermal Grizzly', 'thermal_paste', 12.5, ARRAY[-250.0, 350.0]),
('Bergquist Gap Pad', 'Henkel', 'thermal_pad', 3.0, ARRAY[-40.0, 150.0]),
('Indium Foil', 'Indium Corporation', 'phase_change', 86.0, ARRAY[25.0, 200.0]);

-- Insert sample equipment
INSERT INTO thermal_equipment (equipment_name, manufacturer, equipment_type,
                             max_temperature_c, wafer_sizes_mm, supported_atmospheres) VALUES
('RTP-1000', 'Applied Materials', 'RTP', 1200.0, ARRAY[200, 300], ARRAY['N2', 'O2', 'Ar']),
('Furnace-2000', 'Tokyo Electron', 'FURNACE', 1100.0, ARRAY[200, 300], ARRAY['N2', 'O2', 'H2']),
('Laser Annealer LA-300', 'Ultratech', 'LASER_ANNEALER', 1500.0, ARRAY[200, 300], ARRAY['N2', 'Ar']);

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON SCHEMA thermal IS 'Comprehensive thermal module database schema for SemiPRO simulator';
COMMENT ON TABLE thermal_equipment IS 'Thermal processing equipment specifications and operational data';
COMMENT ON TABLE thermal_materials IS 'Comprehensive thermal materials properties database';
COMMENT ON TABLE thermal_interface_materials IS 'Thermal interface materials (TIMs) database';
COMMENT ON TABLE thermal_processes IS 'Central table for all thermal processes with comprehensive parameters';
COMMENT ON TABLE thermal_simulation_results IS 'Detailed thermal simulation results and analysis data';
COMMENT ON TABLE thermal_monitoring IS 'Real-time thermal process monitoring data';
COMMENT ON TABLE industrial_thermal_recipes IS 'Proven industrial thermal processing recipes';
COMMENT ON TABLE thermal_device_applications IS 'Industrial thermal device applications and specifications';
