-- =====================================================================
-- SemiPRO Metallization Module Database Schema
-- =====================================================================
-- Comprehensive database schema for metallization process tracking,
-- parameter storage, result analysis, and industrial application management
-- 
-- Author: Enhanced SemiPRO Development Team
-- =====================================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================================
-- METALLIZATION-SPECIFIC TABLES
-- =====================================================================

-- Metallization processes table - Track all metallization simulations
CREATE TABLE IF NOT EXISTS metallization_processes (
    process_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wafer_id UUID NOT NULL REFERENCES wafers(wafer_id) ON DELETE CASCADE,
    session_id UUID REFERENCES simulation_sessions(session_id),
    
    -- Process identification
    process_name VARCHAR(255) NOT NULL,
    process_type VARCHAR(100) NOT NULL, -- 'pvd_sputtering', 'electroplating', 'ald', etc.
    
    -- Material information
    metal_symbol VARCHAR(10) NOT NULL,
    metal_name VARCHAR(100),
    
    -- Process parameters
    target_thickness_nm DECIMAL(15,6) NOT NULL,
    actual_thickness_nm DECIMAL(15,6),
    temperature_c DECIMAL(10,3),
    pressure_torr DECIMAL(15,9),
    power_w DECIMAL(10,2),
    deposition_rate_nm_min DECIMAL(10,3),
    
    -- Equipment information
    equipment_type VARCHAR(100),
    equipment_model VARCHAR(255),
    chamber_id VARCHAR(100),
    
    -- Process-specific parameters (stored as JSON for flexibility)
    process_parameters JSONB,
    
    -- Results and quality metrics
    uniformity_percent DECIMAL(8,4),
    step_coverage_percent DECIMAL(8,4),
    grain_size_nm DECIMAL(10,3),
    stress_mpa DECIMAL(12,6),
    resistivity_uohm_cm DECIMAL(15,9),
    surface_roughness_nm DECIMAL(10,6),
    microstructure VARCHAR(100),
    
    -- Quality assessment
    meets_specifications BOOLEAN DEFAULT true,
    quality_score DECIMAL(5,2) DEFAULT 100.0,
    defect_count INTEGER DEFAULT 0,
    
    -- Process timing
    process_time_minutes DECIMAL(10,3),
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    operator_id VARCHAR(100),
    recipe_id VARCHAR(100),
    lot_id VARCHAR(100),
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Metal properties database
CREATE TABLE IF NOT EXISTS metal_properties (
    metal_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    
    -- Physical properties
    atomic_mass_amu DECIMAL(10,6),
    density_g_cm3 DECIMAL(10,6),
    melting_point_c DECIMAL(10,3),
    boiling_point_c DECIMAL(10,3),
    
    -- Electrical properties
    resistivity_uohm_cm DECIMAL(15,9),
    work_function_ev DECIMAL(8,6),
    
    -- Thermal properties
    thermal_conductivity_w_m_k DECIMAL(10,3),
    thermal_expansion_coeff DECIMAL(15,12),
    
    -- Mechanical properties
    young_modulus_gpa DECIMAL(10,3),
    poisson_ratio DECIMAL(8,6),
    hardness_gpa DECIMAL(8,3),
    
    -- Reliability properties
    electromigration_activation_ev DECIMAL(8,6),
    stress_migration_activation_ev DECIMAL(8,6),
    
    -- Additional properties (JSON for flexibility)
    additional_properties JSONB,
    
    -- Metadata
    data_source VARCHAR(255),
    reference_temperature_c DECIMAL(8,3) DEFAULT 25.0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Equipment database
CREATE TABLE IF NOT EXISTS metallization_equipment (
    equipment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    equipment_name VARCHAR(255) NOT NULL,
    manufacturer VARCHAR(255),
    model VARCHAR(255),
    equipment_type VARCHAR(100) NOT NULL, -- 'pvd', 'cvd', 'ald', 'ecd', etc.
    
    -- Capabilities
    supported_techniques TEXT[], -- Array of supported techniques
    max_wafer_size_mm INTEGER,
    max_temperature_c DECIMAL(8,2),
    min_pressure_torr DECIMAL(15,9),
    max_power_w DECIMAL(10,2),
    
    -- Performance specifications
    uniformity_spec_percent DECIMAL(8,4),
    throughput_wafers_hour INTEGER,
    
    -- Configuration parameters (JSON for flexibility)
    default_parameters JSONB,
    calibration_parameters JSONB,
    
    -- Status and maintenance
    is_active BOOLEAN DEFAULT true,
    last_maintenance TIMESTAMP WITH TIME ZONE,
    next_maintenance TIMESTAMP WITH TIME ZONE,
    
    -- Location and identification
    fab_location VARCHAR(255),
    tool_id VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Process recipes database
CREATE TABLE IF NOT EXISTS metallization_recipes (
    recipe_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recipe_name VARCHAR(255) NOT NULL,
    recipe_version VARCHAR(50) DEFAULT '1.0',
    
    -- Recipe classification
    process_type VARCHAR(100) NOT NULL,
    application_type VARCHAR(100), -- 'interconnects', 'contacts', 'barriers', etc.
    technology_node_nm INTEGER,
    
    -- Target specifications
    target_metal VARCHAR(10) NOT NULL,
    target_thickness_nm DECIMAL(15,6),
    target_uniformity_percent DECIMAL(8,4),
    target_step_coverage_percent DECIMAL(8,4),
    
    -- Process parameters
    recipe_parameters JSONB NOT NULL,
    
    -- Equipment requirements
    required_equipment_type VARCHAR(100),
    compatible_equipment TEXT[], -- Array of compatible equipment IDs
    
    -- Quality specifications
    quality_specifications JSONB,
    
    -- Recipe status and validation
    is_validated BOOLEAN DEFAULT false,
    validation_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    
    -- Ownership and approval
    created_by VARCHAR(100),
    approved_by VARCHAR(100),
    approval_date TIMESTAMP WITH TIME ZONE,
    
    -- Documentation
    description TEXT,
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Industrial applications database
CREATE TABLE IF NOT EXISTS metallization_applications (
    application_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    application_name VARCHAR(255) NOT NULL,
    application_type VARCHAR(100) NOT NULL, -- 'advanced_interconnects', 'power_devices', etc.
    
    -- Application specifications
    technology_node_nm INTEGER,
    device_type VARCHAR(100),
    feature_size_nm DECIMAL(10,3),
    aspect_ratio DECIMAL(8,4),
    
    -- Metallization requirements
    required_metals TEXT[], -- Array of required metals
    layer_stack JSONB, -- Detailed layer stack information
    
    -- Performance requirements
    performance_requirements JSONB,
    reliability_requirements JSONB,
    
    -- Process flow
    process_sequence JSONB, -- Ordered list of process steps
    
    -- Industrial context
    industry_sector VARCHAR(100),
    market_segment VARCHAR(100),
    
    -- Documentation
    description TEXT,
    reference_documents TEXT[],
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    maturity_level VARCHAR(50), -- 'research', 'development', 'production'
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Process characterization data
CREATE TABLE IF NOT EXISTS metallization_characterization (
    char_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    process_id UUID NOT NULL REFERENCES metallization_processes(process_id) ON DELETE CASCADE,
    
    -- Characterization method
    method VARCHAR(100) NOT NULL, -- 'xrd', 'sem', 'tem', 'afm', 'xps', etc.
    measurement_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Measurement data (stored as compressed binary for large datasets)
    raw_data BYTEA,
    processed_data JSONB,
    
    -- Analysis results
    analysis_results JSONB,
    
    -- Measurement conditions
    measurement_conditions JSONB,
    
    -- Equipment used for characterization
    characterization_equipment VARCHAR(255),
    operator VARCHAR(100),
    
    -- Quality and validation
    data_quality_score DECIMAL(5,2),
    is_validated BOOLEAN DEFAULT false,
    
    -- Metadata
    file_references TEXT[], -- References to external data files
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================================

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_metallization_processes_wafer_id ON metallization_processes(wafer_id);
CREATE INDEX IF NOT EXISTS idx_metallization_processes_session_id ON metallization_processes(session_id);
CREATE INDEX IF NOT EXISTS idx_metallization_processes_metal ON metallization_processes(metal_symbol);
CREATE INDEX IF NOT EXISTS idx_metallization_processes_type ON metallization_processes(process_type);
CREATE INDEX IF NOT EXISTS idx_metallization_processes_equipment ON metallization_processes(equipment_type);
CREATE INDEX IF NOT EXISTS idx_metallization_processes_created_at ON metallization_processes(created_at);

-- Metal properties indexes
CREATE INDEX IF NOT EXISTS idx_metal_properties_symbol ON metal_properties(symbol);
CREATE INDEX IF NOT EXISTS idx_metal_properties_name ON metal_properties(name);

-- Equipment indexes
CREATE INDEX IF NOT EXISTS idx_metallization_equipment_type ON metallization_equipment(equipment_type);
CREATE INDEX IF NOT EXISTS idx_metallization_equipment_active ON metallization_equipment(is_active);

-- Recipe indexes
CREATE INDEX IF NOT EXISTS idx_metallization_recipes_type ON metallization_recipes(process_type);
CREATE INDEX IF NOT EXISTS idx_metallization_recipes_metal ON metallization_recipes(target_metal);
CREATE INDEX IF NOT EXISTS idx_metallization_recipes_active ON metallization_recipes(is_active);

-- Application indexes
CREATE INDEX IF NOT EXISTS idx_metallization_applications_type ON metallization_applications(application_type);
CREATE INDEX IF NOT EXISTS idx_metallization_applications_node ON metallization_applications(technology_node_nm);

-- Characterization indexes
CREATE INDEX IF NOT EXISTS idx_metallization_characterization_process_id ON metallization_characterization(process_id);
CREATE INDEX IF NOT EXISTS idx_metallization_characterization_method ON metallization_characterization(method);

-- =====================================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================================

-- Process summary view
CREATE OR REPLACE VIEW metallization_process_summary AS
SELECT 
    mp.process_id,
    mp.wafer_id,
    mp.process_name,
    mp.process_type,
    mp.metal_symbol,
    mp.target_thickness_nm,
    mp.actual_thickness_nm,
    mp.uniformity_percent,
    mp.step_coverage_percent,
    mp.quality_score,
    mp.equipment_type,
    mp.process_time_minutes,
    mp.created_at,
    w.wafer_name,
    w.diameter_mm,
    w.thickness_um
FROM metallization_processes mp
JOIN wafers w ON mp.wafer_id = w.wafer_id;

-- Equipment utilization view
CREATE OR REPLACE VIEW equipment_utilization AS
SELECT 
    me.equipment_name,
    me.equipment_type,
    COUNT(mp.process_id) as total_processes,
    AVG(mp.quality_score) as avg_quality_score,
    AVG(mp.process_time_minutes) as avg_process_time,
    MAX(mp.created_at) as last_used
FROM metallization_equipment me
LEFT JOIN metallization_processes mp ON me.equipment_name = mp.equipment_type
WHERE me.is_active = true
GROUP BY me.equipment_id, me.equipment_name, me.equipment_type;

-- =====================================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================================

COMMENT ON TABLE metallization_processes IS 'Comprehensive tracking of all metallization simulation processes';
COMMENT ON TABLE metal_properties IS 'Database of metal properties for simulation and analysis';
COMMENT ON TABLE metallization_equipment IS 'Industrial equipment database with capabilities and specifications';
COMMENT ON TABLE metallization_recipes IS 'Process recipes for different metallization applications';
COMMENT ON TABLE metallization_applications IS 'Industrial applications and their metallization requirements';
COMMENT ON TABLE metallization_characterization IS 'Characterization data and analysis results';
