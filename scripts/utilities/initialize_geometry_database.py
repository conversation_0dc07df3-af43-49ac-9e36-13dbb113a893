#!/usr/bin/env python3
"""
Initialize Geometry Database Schema
==================================

Initialize PostgreSQL database with geometry module schema.

Author: Dr<PERSON>
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def initialize_database_schema():
    """Initialize the geometry database schema"""
    print("🗄️ Initializing Geometry Database Schema")
    print("=" * 50)
    
    try:
        from database_manager import DatabaseManager
        
        # Connect to database
        db = DatabaseManager()
        print("✅ Connected to PostgreSQL database")
        
        # Read schema file
        schema_file = Path(__file__).parent / "src" / "sql" / "geometry_schema.sql"
        
        if not schema_file.exists():
            print(f"❌ Schema file not found: {schema_file}")
            return False
        
        with open(schema_file, 'r') as f:
            schema_sql = f.read()
        
        print(f"✅ Schema file loaded: {len(schema_sql)} characters")
        
        # Execute schema
        with db.get_cursor() as cursor:
            cursor.execute(schema_sql)
            print("✅ Geometry database schema created successfully")
        
        # Initialize with data
        from geometry.database_init import initialize_geometry_database
        success = initialize_geometry_database(db)
        
        if success:
            print("✅ Geometry database initialized with data")
        else:
            print("⚠️ Database initialization completed with some issues")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = initialize_database_schema()
    if success:
        print("\n🎉 Geometry database initialization completed successfully!")
    else:
        print("\n❌ Geometry database initialization failed!")
    
    sys.exit(0 if success else 1)
