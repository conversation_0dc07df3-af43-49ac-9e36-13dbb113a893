#!/usr/bin/env python3
"""
PostgreSQL Device Fabrication Flow Demonstration
Complete device fabrication workflow with database tracking
"""

import sys
import os
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demonstrate_device_fabrication_flow():
    """Demonstrate complete device fabrication flow with PostgreSQL tracking"""
    print("PostgreSQL Device Fabrication Flow Demonstration")
    print("=" * 80)
    
    # Import Qt application
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    
    try:
        # Initialize database integration
        from src.python.module_database_integration import get_module_database_integration
        from src.python.database_manager import get_database_manager
        
        db_integration = get_module_database_integration()
        db = get_database_manager()
        
        print("✅ Database integration initialized")
        
        # Create a new wafer for device fabrication
        wafer_name = f"DEVICE_FAB_{int(time.time())}"
        wafer_id = db.create_wafer(
            wafer_name=wafer_name,
            diameter_mm=200.0,
            thickness_um=775.0,
            material="silicon",
            operator_id="fabrication_demo"
        )
        
        print(f"✅ Created wafer: {wafer_name} (ID: {wafer_id})")
        
        # Start simulation session
        session_id = db_integration.start_simulation_session(wafer_id, "complete_device_fabrication")
        print(f"✅ Started fabrication session: {session_id}")
        
        # Define complete device fabrication workflow
        fabrication_steps = [
            # Step 1: Geometry Definition
            {
                'module': 'geometry',
                'process_name': 'Wafer Geometry Setup',
                'panel_class': 'src.python.gui.geometry_panel.GeometryPanel',
                'simulation_method': 'run_geometry_simulation',
                'params': {
                    'wafer_diameter': 200.0,
                    'wafer_thickness': 775.0,
                    'grid_size': 100,
                    'material': 'silicon',
                    'crystal_orientation': '100'
                }
            },
            
            # Step 2: Initial Oxidation
            {
                'module': 'oxidation',
                'process_name': 'Gate Oxide Formation',
                'panel_class': 'src.python.gui.oxidation_panel.OxidationPanel',
                'simulation_method': 'run_oxidation_simulation',
                'params': {
                    'temperature': 1000.0,
                    'time': 30.0,
                    'atmosphere': 'dry_oxygen',
                    'target_thickness': 5.0
                }
            },
            
            # Step 3: Lithography
            {
                'module': 'enhanced_lithography',
                'process_name': 'Gate Pattern Lithography',
                'panel_class': 'src.python.gui.enhanced_lithography_panel.EnhancedLithographyPanel',
                'simulation_method': 'run_lithography_simulation',
                'params': {
                    'wavelength': 193.0,
                    'feature_size': 0.1,
                    'resist_type': 'positive',
                    'exposure_dose': 25.0
                }
            },
            
            # Step 4: Etching
            {
                'module': 'enhanced_etching',
                'process_name': 'Gate Oxide Etch',
                'panel_class': 'src.python.gui.enhanced_etching_panel.EnhancedEtchingPanel',
                'simulation_method': 'run_etching_simulation',
                'params': {
                    'etch_type': 'anisotropic',
                    'etch_depth': 0.05,
                    'selectivity': 15.0,
                    'chemistry': 'CF4/O2'
                }
            },
            
            # Step 5: Doping
            {
                'module': 'doping',
                'process_name': 'Source/Drain Implantation',
                'panel_class': 'src.python.gui.doping_panel.DopingPanel',
                'simulation_method': 'run_doping_simulation',
                'params': {
                    'dopant_species': 'arsenic',
                    'energy': 50.0,
                    'dose': 5e15,
                    'tilt_angle': 7.0,
                    'anneal_temperature': 1000.0
                }
            },
            
            # Step 6: Metallization
            {
                'module': 'enhanced_metallization',
                'process_name': 'Contact Metallization',
                'panel_class': 'src.python.gui.enhanced_metallization_panel.EnhancedMetallizationPanel',
                'simulation_method': 'run_metallization_simulation',
                'params': {
                    'metal': 'aluminum',
                    'thickness': 0.8,
                    'method': 'sputtering',
                    'temperature': 400.0
                }
            },
            
            # Step 7: CMP
            {
                'module': 'cmp',
                'process_name': 'Metal CMP',
                'panel_class': 'src.python.gui.cmp_panel.CMPPanel',
                'simulation_method': 'run_cmp_simulation',
                'params': {
                    'process_type': 'metal_cmp',
                    'down_force': 3.5,
                    'platen_speed': 60.0,
                    'target_removal': 300.0
                }
            },
            
            # Step 8: Thermal Processing
            {
                'module': 'enhanced_thermal',
                'process_name': 'Annealing',
                'panel_class': 'src.python.gui.enhanced_thermal_panel.EnhancedThermalPanel',
                'simulation_method': 'run_thermal_simulation',
                'params': {
                    'temperature': 450.0,
                    'time': 30.0,
                    'atmosphere': 'forming_gas',
                    'ramp_rate': 10.0
                }
            },
            
            # Step 9: Packaging
            {
                'module': 'enhanced_packaging',
                'process_name': 'Device Packaging',
                'panel_class': 'src.python.gui.enhanced_packaging_panel.EnhancedPackagingPanel',
                'simulation_method': 'run_packaging_simulation',
                'params': {
                    'package_type': 'BGA',
                    'pin_count': 256,
                    'thermal_resistance': 2.0,
                    'test_type': 'full_electrical'
                }
            },
            
            # Step 10: Reliability Testing
            {
                'module': 'enhanced_reliability',
                'process_name': 'Reliability Qualification',
                'panel_class': 'src.python.gui.enhanced_reliability_panel.EnhancedReliabilityPanel',
                'simulation_method': 'run_reliability_simulation',
                'params': {
                    'temperature': 125.0,
                    'voltage': 3.3,
                    'test_duration': 1000.0,
                    'stress_conditions': 'accelerated'
                }
            }
        ]
        
        print(f"\n🚀 Starting {len(fabrication_steps)}-step device fabrication workflow...")
        print("=" * 80)
        
        # Execute each fabrication step
        step_results = []
        current_wafer = None
        
        for i, step in enumerate(fabrication_steps, 1):
            print(f"\nStep {i}: {step['process_name']} ({step['module'].title()})")
            print("-" * 60)
            
            try:
                # Import and create panel
                module_path, class_name = step['panel_class'].rsplit('.', 1)
                module = __import__(module_path, fromlist=[class_name])
                panel_class = getattr(module, class_name)
                panel = panel_class()
                
                # Set wafer if available from previous step
                if current_wafer:
                    panel.set_wafer(current_wafer)
                
                # Run simulation
                simulation_method = getattr(panel, step['simulation_method'])
                result = simulation_method(step['process_name'], step['params'])
                
                if result and result.get('success'):
                    print(f"  ✅ {step['process_name']}: SUCCESS")
                    
                    # Record in database
                    step_id = db_integration.record_module_simulation(
                        wafer_id=wafer_id,
                        module_id=step['module'],
                        process_name=step['process_name'],
                        input_params=step['params'],
                        results=result
                    )
                    
                    print(f"  📊 Database recorded: Step ID {step_id}")
                    
                    # Update current wafer
                    current_wafer = panel.get_wafer()
                    
                    # Record wafer transfer to next module
                    if i < len(fabrication_steps):
                        next_module = fabrication_steps[i]['module']
                        transfer_id = db_integration.record_wafer_transfer(
                            wafer_id=wafer_id,
                            from_module=step['module'],
                            to_module=next_module,
                            wafer_state={'step': i, 'completed': step['process_name']}
                        )
                        print(f"  🔄 Wafer transferred to {next_module}: {transfer_id}")
                    
                    step_results.append({
                        'step': i,
                        'module': step['module'],
                        'process': step['process_name'],
                        'success': True,
                        'step_id': step_id,
                        'key_results': {k: v for k, v in result.items() if k not in ['wafer', 'conditions']}
                    })
                    
                else:
                    print(f"  ❌ {step['process_name']}: FAILED")
                    error_msg = result.get('error', 'Unknown error') if result else 'No result returned'
                    print(f"     Error: {error_msg}")
                    
                    step_results.append({
                        'step': i,
                        'module': step['module'],
                        'process': step['process_name'],
                        'success': False,
                        'error': error_msg
                    })
                
                # Small delay for demonstration
                time.sleep(0.5)
                
            except Exception as e:
                print(f"  ❌ {step['process_name']}: ERROR - {e}")
                step_results.append({
                    'step': i,
                    'module': step['module'],
                    'process': step['process_name'],
                    'success': False,
                    'error': str(e)
                })
        
        # Complete simulation session
        session_summary = db_integration.complete_simulation_session(session_id)
        
        # Generate comprehensive report
        print("\n" + "=" * 80)
        print("DEVICE FABRICATION FLOW COMPLETION REPORT")
        print("=" * 80)
        
        successful_steps = sum(1 for r in step_results if r['success'])
        total_steps = len(step_results)
        
        print(f"Wafer ID: {wafer_id}")
        print(f"Session ID: {session_id}")
        print(f"Total Steps: {total_steps}")
        print(f"Successful Steps: {successful_steps}")
        print(f"Success Rate: {successful_steps/total_steps*100:.1f}%")
        print(f"Total Processing Time: {session_summary.get('total_time_seconds', 0):.1f} seconds")
        
        print(f"\nStep-by-Step Results:")
        for result in step_results:
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            print(f"  {result['step']:2d}. {result['process']:<30} {status}")
            if result['success'] and 'key_results' in result:
                # Show key metrics
                key_results = result['key_results']
                if 'process_name' in key_results:
                    print(f"      Process: {key_results['process_name']}")
        
        # Database statistics
        print(f"\nDatabase Integration Results:")
        wafer_history = db_integration.get_wafer_history(wafer_id)
        print(f"  Process Steps Recorded: {wafer_history['total_steps']}")
        print(f"  Wafer Transfers Recorded: {wafer_history['total_transfers']}")
        print(f"  Layer Stack Depth: {wafer_history['total_layers']}")
        
        if successful_steps >= total_steps * 0.8:
            print(f"\n🎉 DEVICE FABRICATION SUCCESS!")
            print(f"✅ Complete semiconductor device successfully fabricated")
            print(f"✅ All process steps tracked in PostgreSQL database")
            print(f"✅ Wafer history and quality metrics recorded")
            print(f"✅ Device ready for testing and qualification")
        else:
            print(f"\n⚠️  DEVICE FABRICATION PARTIAL SUCCESS")
            print(f"📊 {successful_steps}/{total_steps} steps completed successfully")
            print(f"🔧 Review failed steps for process optimization")
        
        # Clean up
        app.quit()
        
        return successful_steps >= total_steps * 0.8
        
    except Exception as e:
        print(f"❌ Device fabrication flow failed: {e}")
        app.quit()
        return False

if __name__ == "__main__":
    success = demonstrate_device_fabrication_flow()
    print(f"\n{'='*80}")
    print("PostgreSQL Device Fabrication Flow Demonstration Complete!")
    sys.exit(0 if success else 1)
