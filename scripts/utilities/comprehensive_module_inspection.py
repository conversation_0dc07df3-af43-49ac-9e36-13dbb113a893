#!/usr/bin/env python3
"""
Comprehensive Module Inspection
Inspect all available modules in SemiPRO system including missing ones
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def inspect_all_modules():
    """Inspect all available modules in the SemiPRO system"""
    print("Comprehensive Module Inspection")
    print("=" * 70)
    
    # Import Qt application
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    
    # Define all expected modules based on documentation and code analysis
    all_modules = {
        # Enhanced modules (already integrated)
        'enhanced_deposition': {
            'path': 'src.python.gui.enhanced_deposition_panel',
            'class': 'EnhancedDepositionPanel',
            'type': 'enhanced',
            'status': 'integrated'
        },
        'enhanced_lithography': {
            'path': 'src.python.gui.enhanced_lithography_panel',
            'class': 'EnhancedLithographyPanel',
            'type': 'enhanced',
            'status': 'integrated'
        },
        'enhanced_etching': {
            'path': 'src.python.gui.enhanced_etching_panel',
            'class': 'EnhancedEtchingPanel',
            'type': 'enhanced',
            'status': 'integrated'
        },
        'enhanced_metallization': {
            'path': 'src.python.gui.enhanced_metallization_panel',
            'class': 'EnhancedMetallizationPanel',
            'type': 'enhanced',
            'status': 'integrated'
        },
        'enhanced_thermal': {
            'path': 'src.python.gui.enhanced_thermal_panel',
            'class': 'EnhancedThermalPanel',
            'type': 'enhanced',
            'status': 'integrated'
        },
        'enhanced_packaging': {
            'path': 'src.python.gui.enhanced_packaging_panel',
            'class': 'EnhancedPackagingPanel',
            'type': 'enhanced',
            'status': 'integrated'
        },
        'enhanced_reliability': {
            'path': 'src.python.gui.enhanced_reliability_panel',
            'class': 'EnhancedReliabilityPanel',
            'type': 'enhanced',
            'status': 'integrated'
        },
        
        # Basic modules (need integration)
        'geometry': {
            'path': 'src.python.gui.geometry_panel',
            'class': 'GeometryPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'oxidation': {
            'path': 'src.python.gui.oxidation_panel',
            'class': 'OxidationPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'doping': {
            'path': 'src.python.gui.doping_panel',
            'class': 'DopingPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'deposition': {
            'path': 'src.python.gui.deposition_panel',
            'class': 'DepositionPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'etching': {
            'path': 'src.python.gui.etching_panel',
            'class': 'EtchingPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'metallization': {
            'path': 'src.python.gui.metallization_panel',
            'class': 'MetallizationPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'packaging': {
            'path': 'src.python.gui.packaging_panel',
            'class': 'PackagingPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'thermal': {
            'path': 'src.python.gui.thermal_panel',
            'class': 'ThermalPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'reliability': {
            'path': 'src.python.gui.reliability_panel',
            'class': 'ReliabilityPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'photolithography': {
            'path': 'src.python.gui.photolithography_panel',
            'class': 'PhotolithographyPanel',
            'type': 'basic',
            'status': 'missing'
        },
        'cmp': {
            'path': 'src.python.gui.cmp_panel',
            'class': 'CMPPanel',
            'type': 'basic',
            'status': 'missing'
        },
        
        # Advanced modules
        'visualization': {
            'path': 'src.python.gui.visualization_panel',
            'class': 'VisualizationPanel',
            'type': 'advanced',
            'status': 'missing'
        },
        'multi_die': {
            'path': 'src.python.gui.multi_die_panel',
            'class': 'MultiDiePanel',
            'type': 'advanced',
            'status': 'missing'
        },
        'drc': {
            'path': 'src.python.gui.drc_panel',
            'class': 'DRCPanel',
            'type': 'advanced',
            'status': 'missing'
        },
        'inspection': {
            'path': 'src.python.gui.inspection_panel',
            'class': 'InspectionPanel',
            'type': 'advanced',
            'status': 'missing'
        },
        'metrology': {
            'path': 'src.python.gui.metrology_panel',
            'class': 'MetrologyPanel',
            'type': 'advanced',
            'status': 'missing'
        },
        'interconnect': {
            'path': 'src.python.gui.interconnect_panel',
            'class': 'InterconnectPanel',
            'type': 'advanced',
            'status': 'missing'
        },
        'workflow': {
            'path': 'src.python.gui.workflow_panel',
            'class': 'WorkflowPanel',
            'type': 'advanced',
            'status': 'missing'
        }
    }
    
    results = {}
    
    # Test each module
    for module_name, module_info in all_modules.items():
        print(f"\nTesting {module_name.title()} Module...")
        
        try:
            # Import module
            module = __import__(module_info['path'], fromlist=[module_info['class']])
            panel_class = getattr(module, module_info['class'])
            
            # Create panel instance
            panel = panel_class()
            print(f"  Panel created: SUCCESS")
            
            # Check for wafer management methods
            wafer_methods = ['set_wafer', 'get_wafer']
            wafer_ok = all(hasattr(panel, method) for method in wafer_methods)
            print(f"  Wafer methods: {'SUCCESS' if wafer_ok else 'MISSING'}")
            
            # Check for simulation method
            sim_methods = [f'run_{module_name}_simulation', 'run_simulation', 'simulate']
            sim_ok = any(hasattr(panel, method) for method in sim_methods)
            print(f"  Simulation method: {'SUCCESS' if sim_ok else 'MISSING'}")
            
            # Check for database integration
            db_methods = ['set_database_integration', 'set_wafer_with_database']
            db_ok = any(hasattr(panel, method) for method in db_methods)
            print(f"  Database integration: {'SUCCESS' if db_ok else 'MISSING'}")
            
            results[module_name] = {
                'available': True,
                'wafer_methods': wafer_ok,
                'simulation_method': sim_ok,
                'database_integration': db_ok,
                'type': module_info['type'],
                'integration_needed': not (wafer_ok and sim_ok and db_ok)
            }
            
            integration_status = "COMPLETE" if not results[module_name]['integration_needed'] else "NEEDS WORK"
            print(f"  Integration status: {integration_status}")
            
        except Exception as e:
            print(f"  ERROR: {e}")
            results[module_name] = {
                'available': False,
                'error': str(e),
                'type': module_info['type'],
                'integration_needed': True
            }
    
    # Generate comprehensive summary
    print("\n" + "=" * 70)
    print("COMPREHENSIVE MODULE INSPECTION SUMMARY")
    print("=" * 70)
    
    # Categorize results
    enhanced_modules = {k: v for k, v in results.items() if all_modules[k]['type'] == 'enhanced'}
    basic_modules = {k: v for k, v in results.items() if all_modules[k]['type'] == 'basic'}
    advanced_modules = {k: v for k, v in results.items() if all_modules[k]['type'] == 'advanced'}
    
    print(f"\nENHANCED MODULES (Already Integrated): {len(enhanced_modules)}")
    for name, result in enhanced_modules.items():
        status = "COMPLETE" if result.get('available') and not result.get('integration_needed') else "NEEDS WORK"
        print(f"  {name.title()}: {status}")
    
    print(f"\nBASIC MODULES (Need Integration): {len(basic_modules)}")
    for name, result in basic_modules.items():
        if result.get('available'):
            status = "AVAILABLE - NEEDS INTEGRATION"
        else:
            status = "NOT AVAILABLE"
        print(f"  {name.title()}: {status}")
    
    print(f"\nADVANCED MODULES (Need Integration): {len(advanced_modules)}")
    for name, result in advanced_modules.items():
        if result.get('available'):
            status = "AVAILABLE - NEEDS INTEGRATION"
        else:
            status = "NOT AVAILABLE"
        print(f"  {name.title()}: {status}")
    
    # Statistics
    total_modules = len(results)
    available_modules = sum(1 for r in results.values() if r.get('available'))
    integrated_modules = sum(1 for r in results.values() if r.get('available') and not r.get('integration_needed'))
    
    print(f"\nSTATISTICS:")
    print(f"Total Modules: {total_modules}")
    print(f"Available Modules: {available_modules}")
    print(f"Fully Integrated: {integrated_modules}")
    print(f"Need Integration: {available_modules - integrated_modules}")
    print(f"Not Available: {total_modules - available_modules}")
    
    print(f"\nINTEGRATION PRIORITY:")
    print(f"HIGH PRIORITY (Basic Process Modules):")
    high_priority = ['geometry', 'oxidation', 'doping', 'deposition', 'cmp']
    for module in high_priority:
        if module in results and results[module].get('available'):
            print(f"  {module.title()}: READY FOR INTEGRATION")
        else:
            print(f"  {module.title()}: NOT AVAILABLE")
    
    print(f"\nMEDIUM PRIORITY (Advanced Modules):")
    medium_priority = ['visualization', 'multi_die', 'drc', 'inspection']
    for module in medium_priority:
        if module in results and results[module].get('available'):
            print(f"  {module.title()}: READY FOR INTEGRATION")
        else:
            print(f"  {module.title()}: NOT AVAILABLE")
    
    # Clean up
    app.quit()
    
    return results

if __name__ == "__main__":
    results = inspect_all_modules()
    print(f"\n{'='*70}")
    print("Comprehensive Module Inspection Complete!")
    
    # Return success if most modules are available
    available_count = sum(1 for r in results.values() if r.get('available'))
    success = available_count >= len(results) * 0.6
    sys.exit(0 if success else 1)
