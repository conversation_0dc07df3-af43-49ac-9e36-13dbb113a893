#!/usr/bin/env python3
"""
Debug script to check what's being imported
"""

import sys
import os

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

try:
    from oxidation import OxidationManager
    
    print("OxidationManager imported successfully")
    print(f"OxidationManager type: {type(OxidationManager)}")
    
    manager = OxidationManager()
    print(f"Manager created: {type(manager)}")
    print(f"Manager model type: {type(manager.model)}")
    print(f"Manager process_controller: {manager.process_controller}")
    
    # Check available methods
    methods = [method for method in dir(manager) if not method.startswith('_')]
    print(f"Available methods: {methods}")
    
    # Check if the method exists
    if hasattr(manager, 'run_controlled_oxidation'):
        print("✓ run_controlled_oxidation method found")
    else:
        print("❌ run_controlled_oxidation method NOT found")
        
        # Check if it's in the model
        if hasattr(manager.model, 'run_controlled_oxidation'):
            print("✓ run_controlled_oxidation found in model")
        else:
            print("❌ run_controlled_oxidation NOT found in model")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
