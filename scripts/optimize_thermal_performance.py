#!/usr/bin/env python3
"""
Thermal Module Performance Optimization Script
==============================================

Script to optimize thermal module performance including database queries,
visualization rendering, and simulation algorithms.

Author: Dr<PERSON>
"""

import sys
import os
import time
import psutil
import numpy as np
from typing import Dict, List, Any
import logging

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from python.thermal.database_integration import ThermalDatabaseIntegration
    from python.thermal.thermal_visualization import ThermalVisualizationEngine
    from python.thermal.industrial_applications import IndustrialThermalApplications
    from python.semipro_packaging.enhanced_thermal_analysis import EnhancedThermalAnalysisEngine
    THERMAL_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Thermal modules not available: {e}")
    THERMAL_MODULES_AVAILABLE = False

class ThermalPerformanceOptimizer:
    """Thermal module performance optimizer"""
    
    def __init__(self):
        """Initialize performance optimizer"""
        self.logger = logging.getLogger(__name__)
        self.performance_metrics = {}
        
        if THERMAL_MODULES_AVAILABLE:
            self.thermal_engine = EnhancedThermalAnalysisEngine()
            self.viz_engine = ThermalVisualizationEngine()
            self.industrial_apps = IndustrialThermalApplications()
            
            # Try to initialize database integration
            try:
                self.db_integration = ThermalDatabaseIntegration()
                self.db_available = True
            except Exception as e:
                self.logger.warning(f"Database not available: {e}")
                self.db_available = False
        else:
            self.logger.error("Thermal modules not available for optimization")
    
    def benchmark_thermal_analysis(self, grid_sizes: List[int] = None) -> Dict[str, Any]:
        """Benchmark thermal analysis performance"""
        if not THERMAL_MODULES_AVAILABLE:
            return {"error": "Thermal modules not available"}
        
        if grid_sizes is None:
            grid_sizes = [50, 100, 200, 500]
        
        results = {}
        
        for grid_size in grid_sizes:
            self.logger.info(f"Benchmarking thermal analysis with {grid_size}x{grid_size} grid")
            
            # Create test device specifications
            device_specs = {
                'power_dissipation': 100.0,
                'thermal_resistance': 1.0,
                'ambient_temperature': 25.0,
                'grid_size': grid_size
            }
            
            # Measure memory usage before
            process = psutil.Process()
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            
            # Time the thermal analysis
            start_time = time.time()
            
            try:
                analysis_results = self.thermal_engine.run_thermal_analysis(device_specs)
                end_time = time.time()
                
                # Measure memory usage after
                memory_after = process.memory_info().rss / 1024 / 1024  # MB
                
                results[f"grid_{grid_size}"] = {
                    'execution_time_s': end_time - start_time,
                    'memory_usage_mb': memory_after - memory_before,
                    'success': analysis_results.get('success', True),
                    'max_temperature': analysis_results.get('max_temperature', 0),
                    'grid_points': grid_size * grid_size
                }
                
                self.logger.info(f"Grid {grid_size}: {end_time - start_time:.3f}s, "
                               f"{memory_after - memory_before:.1f}MB")
                
            except Exception as e:
                results[f"grid_{grid_size}"] = {
                    'error': str(e),
                    'execution_time_s': float('inf'),
                    'memory_usage_mb': 0,
                    'success': False
                }
                self.logger.error(f"Grid {grid_size} failed: {e}")
        
        return results
    
    def benchmark_visualization(self, data_sizes: List[int] = None) -> Dict[str, Any]:
        """Benchmark visualization performance"""
        if not THERMAL_MODULES_AVAILABLE:
            return {"error": "Thermal modules not available"}
        
        if data_sizes is None:
            data_sizes = [50, 100, 200, 500]
        
        results = {}
        
        for size in data_sizes:
            self.logger.info(f"Benchmarking visualization with {size}x{size} data")
            
            # Create test temperature data
            temperature_data = np.random.rand(size, size) * 100 + 298.15
            
            # Benchmark 2D visualization
            start_time = time.time()
            try:
                fig_2d = self.viz_engine.create_2d_temperature_map(
                    temperature_data,
                    title=f"Test {size}x{size}",
                    show_contours=True,
                    show_hotspots=True
                )
                time_2d = time.time() - start_time
                
                # Benchmark 3D visualization
                start_time = time.time()
                fig_3d = self.viz_engine.create_3d_temperature_surface(
                    temperature_data,
                    title=f"Test 3D {size}x{size}"
                )
                time_3d = time.time() - start_time
                
                results[f"size_{size}"] = {
                    'time_2d_s': time_2d,
                    'time_3d_s': time_3d,
                    'data_points': size * size,
                    'success': True
                }
                
                self.logger.info(f"Size {size}: 2D={time_2d:.3f}s, 3D={time_3d:.3f}s")
                
                # Clean up figures to save memory
                fig_2d.clear()
                fig_3d.clear()
                
            except Exception as e:
                results[f"size_{size}"] = {
                    'error': str(e),
                    'time_2d_s': float('inf'),
                    'time_3d_s': float('inf'),
                    'success': False
                }
                self.logger.error(f"Visualization size {size} failed: {e}")
        
        return results
    
    def benchmark_database_operations(self, num_operations: int = 100) -> Dict[str, Any]:
        """Benchmark database operations"""
        if not self.db_available:
            return {"error": "Database not available"}
        
        results = {}
        
        # Benchmark material insertion
        self.logger.info(f"Benchmarking {num_operations} material insertions")
        start_time = time.time()
        
        material_ids = []
        for i in range(num_operations):
            material_data = {
                'material_name': f'Test_Material_{i}',
                'chemical_formula': 'TestFormula',
                'material_class': 'test',
                'thermal_conductivity_w_mk': 100.0 + i,
                'specific_heat_j_kg_k': 500.0 + i,
                'density_kg_m3': 2000.0 + i,
                'thermal_expansion_per_k': 1e-6,
                'temperature_range_c': [25.0, 1000.0]
            }
            
            try:
                material_id = self.db_integration.add_thermal_material(material_data)
                material_ids.append(material_id)
            except Exception as e:
                self.logger.error(f"Failed to add material {i}: {e}")
        
        insert_time = time.time() - start_time
        
        # Benchmark material retrieval
        self.logger.info(f"Benchmarking {len(material_ids)} material retrievals")
        start_time = time.time()
        
        retrieved_count = 0
        for material_id in material_ids:
            try:
                material = self.db_integration.get_thermal_material(f'Test_Material_{material_ids.index(material_id)}')
                if material:
                    retrieved_count += 1
            except Exception as e:
                self.logger.error(f"Failed to retrieve material: {e}")
        
        retrieve_time = time.time() - start_time
        
        results = {
            'insert_time_s': insert_time,
            'insert_rate_ops_per_s': num_operations / insert_time if insert_time > 0 else 0,
            'retrieve_time_s': retrieve_time,
            'retrieve_rate_ops_per_s': retrieved_count / retrieve_time if retrieve_time > 0 else 0,
            'materials_inserted': len(material_ids),
            'materials_retrieved': retrieved_count,
            'success': True
        }
        
        self.logger.info(f"Insert: {results['insert_rate_ops_per_s']:.1f} ops/s, "
                        f"Retrieve: {results['retrieve_rate_ops_per_s']:.1f} ops/s")
        
        return results
    
    def benchmark_industrial_applications(self) -> Dict[str, Any]:
        """Benchmark industrial applications performance"""
        if not THERMAL_MODULES_AVAILABLE:
            return {"error": "Thermal modules not available"}
        
        results = {}
        
        # Get all application types
        from python.thermal.industrial_applications import IndustrialApplicationType
        app_types = list(IndustrialApplicationType)
        
        for app_type in app_types:
            self.logger.info(f"Benchmarking {app_type.value} applications")
            
            start_time = time.time()
            try:
                apps = self.industrial_apps.get_application_by_type(app_type)
                get_time = time.time() - start_time
                
                if apps:
                    # Benchmark performance calculation
                    app = apps[0]
                    cooling_solutions = app.get('cooling_solutions', [])
                    
                    if cooling_solutions:
                        cooling_solution = cooling_solutions[0]['type']
                        
                        start_time = time.time()
                        performance = self.industrial_apps.calculate_thermal_performance(
                            app, cooling_solution, ambient_temp=25.0
                        )
                        calc_time = time.time() - start_time
                        
                        # Benchmark report generation
                        start_time = time.time()
                        report = self.industrial_apps.generate_thermal_report(
                            app['name'], cooling_solution, ambient_temp=25.0
                        )
                        report_time = time.time() - start_time
                        
                        results[app_type.value] = {
                            'get_apps_time_s': get_time,
                            'calc_performance_time_s': calc_time,
                            'generate_report_time_s': report_time,
                            'num_applications': len(apps),
                            'success': True
                        }
                    else:
                        results[app_type.value] = {
                            'get_apps_time_s': get_time,
                            'num_applications': len(apps),
                            'error': 'No cooling solutions available',
                            'success': False
                        }
                else:
                    results[app_type.value] = {
                        'get_apps_time_s': get_time,
                        'num_applications': 0,
                        'error': 'No applications found',
                        'success': False
                    }
                    
            except Exception as e:
                results[app_type.value] = {
                    'error': str(e),
                    'success': False
                }
                self.logger.error(f"Industrial app {app_type.value} failed: {e}")
        
        return results
    
    def run_full_benchmark(self) -> Dict[str, Any]:
        """Run complete performance benchmark"""
        self.logger.info("Starting full thermal module performance benchmark")
        
        full_results = {
            'system_info': {
                'cpu_count': psutil.cpu_count(),
                'memory_gb': psutil.virtual_memory().total / (1024**3),
                'python_version': sys.version
            },
            'benchmarks': {}
        }
        
        # Run all benchmarks
        benchmarks = [
            ('thermal_analysis', self.benchmark_thermal_analysis),
            ('visualization', self.benchmark_visualization),
            ('industrial_applications', self.benchmark_industrial_applications)
        ]
        
        if self.db_available:
            benchmarks.append(('database_operations', self.benchmark_database_operations))
        
        for name, benchmark_func in benchmarks:
            self.logger.info(f"Running {name} benchmark")
            try:
                start_time = time.time()
                results = benchmark_func()
                end_time = time.time()
                
                full_results['benchmarks'][name] = {
                    'results': results,
                    'total_time_s': end_time - start_time,
                    'success': True
                }
                
            except Exception as e:
                full_results['benchmarks'][name] = {
                    'error': str(e),
                    'success': False
                }
                self.logger.error(f"Benchmark {name} failed: {e}")
        
        return full_results
    
    def generate_optimization_report(self, results: Dict[str, Any]) -> str:
        """Generate optimization recommendations report"""
        report = "THERMAL MODULE PERFORMANCE OPTIMIZATION REPORT\n"
        report += "=" * 50 + "\n\n"
        
        # System information
        system_info = results.get('system_info', {})
        report += f"System Information:\n"
        report += f"- CPU Cores: {system_info.get('cpu_count', 'Unknown')}\n"
        report += f"- Memory: {system_info.get('memory_gb', 0):.1f} GB\n"
        report += f"- Python: {system_info.get('python_version', 'Unknown')}\n\n"
        
        # Benchmark results
        benchmarks = results.get('benchmarks', {})
        
        for benchmark_name, benchmark_data in benchmarks.items():
            report += f"{benchmark_name.upper()} BENCHMARK\n"
            report += "-" * 30 + "\n"
            
            if benchmark_data.get('success', False):
                report += f"Total Time: {benchmark_data.get('total_time_s', 0):.3f}s\n"
                
                # Specific recommendations based on benchmark type
                if benchmark_name == 'thermal_analysis':
                    report += self._analyze_thermal_performance(benchmark_data['results'])
                elif benchmark_name == 'visualization':
                    report += self._analyze_visualization_performance(benchmark_data['results'])
                elif benchmark_name == 'database_operations':
                    report += self._analyze_database_performance(benchmark_data['results'])
                elif benchmark_name == 'industrial_applications':
                    report += self._analyze_industrial_performance(benchmark_data['results'])
            else:
                report += f"FAILED: {benchmark_data.get('error', 'Unknown error')}\n"
            
            report += "\n"
        
        # Overall recommendations
        report += "OPTIMIZATION RECOMMENDATIONS\n"
        report += "=" * 30 + "\n"
        report += self._generate_overall_recommendations(results)
        
        return report
    
    def _analyze_thermal_performance(self, results: Dict[str, Any]) -> str:
        """Analyze thermal analysis performance"""
        analysis = ""
        
        # Find performance trends
        grid_sizes = []
        times = []
        memory_usage = []
        
        for key, data in results.items():
            if key.startswith('grid_') and data.get('success', False):
                grid_size = int(key.split('_')[1])
                grid_sizes.append(grid_size)
                times.append(data['execution_time_s'])
                memory_usage.append(data['memory_usage_mb'])
        
        if len(grid_sizes) >= 2:
            # Calculate scaling
            time_scaling = times[-1] / times[0] if times[0] > 0 else float('inf')
            memory_scaling = memory_usage[-1] / memory_usage[0] if memory_usage[0] > 0 else float('inf')
            
            analysis += f"Performance scaling from {grid_sizes[0]} to {grid_sizes[-1]} grid:\n"
            analysis += f"- Time scaling: {time_scaling:.1f}x\n"
            analysis += f"- Memory scaling: {memory_scaling:.1f}x\n"
            
            if time_scaling > (grid_sizes[-1] / grid_sizes[0]) ** 2:
                analysis += "⚠️  Poor time scaling - consider algorithm optimization\n"
            
            if memory_scaling > (grid_sizes[-1] / grid_sizes[0]) ** 2:
                analysis += "⚠️  High memory usage - consider memory optimization\n"
        
        return analysis
    
    def _analyze_visualization_performance(self, results: Dict[str, Any]) -> str:
        """Analyze visualization performance"""
        analysis = ""
        
        # Check 2D vs 3D performance
        sizes_2d = []
        sizes_3d = []
        times_2d = []
        times_3d = []
        
        for key, data in results.items():
            if key.startswith('size_') and data.get('success', False):
                size = int(key.split('_')[1])
                sizes_2d.append(size)
                sizes_3d.append(size)
                times_2d.append(data['time_2d_s'])
                times_3d.append(data['time_3d_s'])
        
        if times_2d and times_3d:
            avg_2d = sum(times_2d) / len(times_2d)
            avg_3d = sum(times_3d) / len(times_3d)
            
            analysis += f"Average visualization times:\n"
            analysis += f"- 2D: {avg_2d:.3f}s\n"
            analysis += f"- 3D: {avg_3d:.3f}s\n"
            analysis += f"- 3D/2D ratio: {avg_3d/avg_2d:.1f}x\n"
            
            if avg_2d > 1.0:
                analysis += "⚠️  Slow 2D rendering - consider optimization\n"
            if avg_3d > 5.0:
                analysis += "⚠️  Very slow 3D rendering - consider level-of-detail\n"
        
        return analysis
    
    def _analyze_database_performance(self, results: Dict[str, Any]) -> str:
        """Analyze database performance"""
        analysis = ""
        
        insert_rate = results.get('insert_rate_ops_per_s', 0)
        retrieve_rate = results.get('retrieve_rate_ops_per_s', 0)
        
        analysis += f"Database operation rates:\n"
        analysis += f"- Insert: {insert_rate:.1f} ops/s\n"
        analysis += f"- Retrieve: {retrieve_rate:.1f} ops/s\n"
        
        if insert_rate < 10:
            analysis += "⚠️  Slow insert performance - consider batch operations\n"
        if retrieve_rate < 100:
            analysis += "⚠️  Slow retrieve performance - check indexing\n"
        
        return analysis
    
    def _analyze_industrial_performance(self, results: Dict[str, Any]) -> str:
        """Analyze industrial applications performance"""
        analysis = ""
        
        total_apps = sum(data.get('num_applications', 0) for data in results.values() if isinstance(data, dict))
        successful_apps = sum(1 for data in results.values() if isinstance(data, dict) and data.get('success', False))
        
        analysis += f"Industrial applications:\n"
        analysis += f"- Total applications: {total_apps}\n"
        analysis += f"- Successful benchmarks: {successful_apps}/{len(results)}\n"
        
        if successful_apps < len(results):
            analysis += "⚠️  Some industrial applications failed - check implementations\n"
        
        return analysis
    
    def _generate_overall_recommendations(self, results: Dict[str, Any]) -> str:
        """Generate overall optimization recommendations"""
        recommendations = ""
        
        recommendations += "1. Enable parallel processing for large thermal grids\n"
        recommendations += "2. Use database connection pooling for concurrent access\n"
        recommendations += "3. Implement level-of-detail rendering for large visualizations\n"
        recommendations += "4. Cache frequently accessed material properties\n"
        recommendations += "5. Use compressed storage for large thermal field data\n"
        recommendations += "6. Optimize matplotlib backend for better performance\n"
        recommendations += "7. Consider GPU acceleration for large-scale simulations\n"
        
        return recommendations

def main():
    """Main optimization script"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    if not THERMAL_MODULES_AVAILABLE:
        print("Thermal modules not available. Cannot run optimization.")
        return 1
    
    optimizer = ThermalPerformanceOptimizer()
    
    print("Starting thermal module performance optimization...")
    results = optimizer.run_full_benchmark()
    
    print("\nGenerating optimization report...")
    report = optimizer.generate_optimization_report(results)
    
    # Save report to file
    report_file = "thermal_performance_report.txt"
    with open(report_file, 'w') as f:
        f.write(report)
    
    print(f"\nOptimization report saved to: {report_file}")
    print("\nReport Summary:")
    print(report)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
