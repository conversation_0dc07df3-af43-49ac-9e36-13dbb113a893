#!/usr/bin/env python3
"""
Build and Test Script for Enhanced Metallization Module
=======================================================

Comprehensive build and test script for the enhanced metallization module
including C++ compilation, Cython building, database setup, and testing.

Author: Enhanced SemiPRO Development Team
"""

import os
import sys
import subprocess
import argparse
import logging
import tempfile
import shutil
from pathlib import Path
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MetallizationBuilder:
    """Builder for enhanced metallization module"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.src_dir = self.project_root / "src"
        self.cpp_dir = self.src_dir / "cpp"
        self.cython_dir = self.src_dir / "cython"
        self.python_dir = self.src_dir / "python"
        self.tests_dir = self.project_root / "tests"
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"
        
        # Create build directory if it doesn't exist
        self.build_dir.mkdir(exist_ok=True)
        
        logger.info(f"Initialized builder for project: {self.project_root}")
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available"""
        logger.info("Checking dependencies...")
        
        dependencies = {
            'cmake': 'cmake --version',
            'g++': 'g++ --version',
            'python': 'python --version',
            'cython': 'cython --version',
            'numpy': 'python -c "import numpy; print(numpy.__version__)"',
            'psycopg2': 'python -c "import psycopg2; print(psycopg2.__version__)"'
        }
        
        missing_deps = []
        
        for dep, check_cmd in dependencies.items():
            try:
                result = subprocess.run(
                    check_cmd.split(),
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    logger.info(f"✓ {dep}: Available")
                else:
                    logger.warning(f"✗ {dep}: Not available")
                    missing_deps.append(dep)
            except (subprocess.TimeoutExpired, FileNotFoundError):
                logger.warning(f"✗ {dep}: Not available")
                missing_deps.append(dep)
        
        if missing_deps:
            logger.error(f"Missing dependencies: {', '.join(missing_deps)}")
            return False
        
        logger.info("All dependencies available")
        return True
    
    def build_cpp_backend(self) -> bool:
        """Build C++ backend using CMake"""
        logger.info("Building C++ backend...")
        
        try:
            # Create CMakeLists.txt if it doesn't exist
            cmake_file = self.cpp_dir / "CMakeLists.txt"
            if not cmake_file.exists():
                self.create_cmake_file()
            
            # Create build directory for C++
            cpp_build_dir = self.build_dir / "cpp"
            cpp_build_dir.mkdir(exist_ok=True)
            
            # Run CMake configuration
            cmake_cmd = [
                'cmake',
                '-S', str(self.cpp_dir),
                '-B', str(cpp_build_dir),
                '-DCMAKE_BUILD_TYPE=Release'
            ]
            
            result = subprocess.run(cmake_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"CMake configuration failed: {result.stderr}")
                return False
            
            # Build
            build_cmd = ['cmake', '--build', str(cpp_build_dir), '--parallel']
            result = subprocess.run(build_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"C++ build failed: {result.stderr}")
                return False
            
            logger.info("C++ backend built successfully")
            return True
            
        except Exception as e:
            logger.error(f"C++ build failed: {e}")
            return False
    
    def create_cmake_file(self):
        """Create CMakeLists.txt for C++ backend"""
        cmake_content = """
cmake_minimum_required(VERSION 3.12)
project(SemiPRO_Metallization)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Eigen3 REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${EIGEN3_INCLUDE_DIR})

# Source files
file(GLOB_RECURSE SOURCES
    "core/*.cpp"
    "modules/metallization/*.cpp"
    "physics/*.cpp"
)

# Create library
add_library(semipro_metallization SHARED ${SOURCES})

# Link libraries
target_link_libraries(semipro_metallization ${EIGEN3_LIBRARIES})

# Install targets
install(TARGETS semipro_metallization
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)
"""
        
        cmake_file = self.cpp_dir / "CMakeLists.txt"
        with open(cmake_file, 'w') as f:
            f.write(cmake_content.strip())
        
        logger.info("Created CMakeLists.txt")
    
    def build_cython_extensions(self) -> bool:
        """Build Cython extensions"""
        logger.info("Building Cython extensions...")
        
        try:
            # Create setup.py for Cython build
            setup_py = self.create_cython_setup()
            
            # Build Cython extensions
            build_cmd = [
                sys.executable, str(setup_py),
                'build_ext', '--inplace'
            ]
            
            result = subprocess.run(
                build_cmd,
                cwd=str(self.cython_dir),
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Cython build failed: {result.stderr}")
                return False
            
            logger.info("Cython extensions built successfully")
            return True
            
        except Exception as e:
            logger.error(f"Cython build failed: {e}")
            return False
    
    def create_cython_setup(self) -> Path:
        """Create setup.py for Cython build"""
        setup_content = '''
from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy

extensions = [
    Extension(
        "metallization",
        sources=["metallization.pyx"],
        include_dirs=[
            numpy.get_include(),
            "../cpp",
            "../cpp/core",
            "../cpp/modules/metallization",
            "../cpp/physics"
        ],
        library_dirs=["../../build/cpp"],
        libraries=["semipro_metallization"],
        language="c++",
        extra_compile_args=["-std=c++17", "-O3"],
        extra_link_args=["-std=c++17"]
    )
]

setup(
    name="semipro_metallization",
    ext_modules=cythonize(extensions, compiler_directives={'language_level': 3}),
    zip_safe=False
)
'''
        
        setup_py = self.cython_dir / "setup.py"
        with open(setup_py, 'w') as f:
            f.write(setup_content.strip())
        
        return setup_py
    
    def setup_database(self) -> bool:
        """Set up PostgreSQL database schema"""
        logger.info("Setting up database schema...")
        
        try:
            # Check if PostgreSQL is available
            pg_check = subprocess.run(
                ['psql', '--version'],
                capture_output=True,
                text=True
            )
            
            if pg_check.returncode != 0:
                logger.warning("PostgreSQL not available, skipping database setup")
                return True
            
            # Try to create database schema
            schema_file = self.src_dir / "sql" / "metallization_schema.sql"
            if schema_file.exists():
                logger.info("Database schema file found, manual setup required")
                logger.info(f"Run: psql -d semipro -f {schema_file}")
            else:
                logger.warning("Database schema file not found")
            
            return True
            
        except Exception as e:
            logger.warning(f"Database setup failed: {e}")
            return True  # Non-critical failure
    
    def run_tests(self) -> bool:
        """Run comprehensive test suite"""
        logger.info("Running test suite...")
        
        try:
            # Add src to Python path
            env = os.environ.copy()
            python_path = str(self.src_dir / "python")
            if 'PYTHONPATH' in env:
                env['PYTHONPATH'] = f"{python_path}:{env['PYTHONPATH']}"
            else:
                env['PYTHONPATH'] = python_path
            
            # Run metallization module tests
            test_file = self.tests_dir / "test_metallization_module.py"
            if test_file.exists():
                test_cmd = [sys.executable, str(test_file)]
                result = subprocess.run(
                    test_cmd,
                    env=env,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode != 0:
                    logger.error(f"Tests failed: {result.stderr}")
                    logger.error(f"Test output: {result.stdout}")
                    return False
                
                logger.info("All tests passed")
                logger.info(f"Test output: {result.stdout}")
            else:
                logger.warning("Test file not found, skipping tests")
            
            return True
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            return False
    
    def validate_installation(self) -> bool:
        """Validate that the installation works correctly"""
        logger.info("Validating installation...")
        
        try:
            # Test Python imports
            test_script = '''
import sys
sys.path.insert(0, "src/python")

try:
    from enhanced_metallization_bridge import EnhancedMetallizationBridge
    bridge = EnhancedMetallizationBridge()
    metals = bridge.get_available_metals()
    print(f"Available metals: {len(metals)}")
    
    from enhanced_metallization_bridge import IndustrialMetallizationExamples
    examples = IndustrialMetallizationExamples()
    apps = examples.get_available_applications()
    print(f"Available applications: {len(apps)}")
    
    print("Validation successful")
except Exception as e:
    print(f"Validation failed: {e}")
    sys.exit(1)
'''
            
            result = subprocess.run(
                [sys.executable, '-c', test_script],
                cwd=str(self.project_root),
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Validation failed: {result.stderr}")
                return False
            
            logger.info("Installation validation successful")
            logger.info(f"Validation output: {result.stdout}")
            return True
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return False
    
    def clean_build(self):
        """Clean build artifacts"""
        logger.info("Cleaning build artifacts...")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        # Clean Cython artifacts
        for pattern in ['*.c', '*.cpp', '*.so', '*.pyd']:
            for file in self.cython_dir.glob(pattern):
                file.unlink()
        
        logger.info("Build artifacts cleaned")


def main():
    """Main build and test function"""
    parser = argparse.ArgumentParser(description='Build and test enhanced metallization module')
    parser.add_argument('--clean', action='store_true', help='Clean build artifacts')
    parser.add_argument('--no-cpp', action='store_true', help='Skip C++ backend build')
    parser.add_argument('--no-cython', action='store_true', help='Skip Cython build')
    parser.add_argument('--no-tests', action='store_true', help='Skip tests')
    parser.add_argument('--no-db', action='store_true', help='Skip database setup')
    parser.add_argument('--project-root', default='.', help='Project root directory')
    
    args = parser.parse_args()
    
    # Initialize builder
    builder = MetallizationBuilder(args.project_root)
    
    # Clean if requested
    if args.clean:
        builder.clean_build()
        return 0
    
    # Check dependencies
    if not builder.check_dependencies():
        logger.error("Dependency check failed")
        return 1
    
    # Build steps
    success = True
    
    if not args.no_cpp:
        success &= builder.build_cpp_backend()
    
    if not args.no_cython and success:
        success &= builder.build_cython_extensions()
    
    if not args.no_db and success:
        success &= builder.setup_database()
    
    if not args.no_tests and success:
        success &= builder.run_tests()
    
    if success:
        success &= builder.validate_installation()
    
    if success:
        logger.info("Build and test completed successfully!")
        return 0
    else:
        logger.error("Build and test failed!")
        return 1


if __name__ == '__main__':
    sys.exit(main())
