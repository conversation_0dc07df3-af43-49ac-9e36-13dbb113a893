#!/usr/bin/env python3
"""
Cython Orchestrator Smoke Test
==============================

Minimal smoke test for Cython orchestrator modules without requiring full build.
Tests both Cython backend (if available) and mock fallback implementations.

Author: <PERSON><PERSON>
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """Print formatted header"""
    print("\n" + "=" * 60)
    print(f"🔬 {title}")
    print("=" * 60)

def print_section(title):
    """Print formatted section"""
    print(f"\n📋 {title}")
    print("-" * 40)

def test_cython_imports():
    """Test Cython module imports"""
    print_section("Testing Cython Module Imports")
    
    cython_modules = [
        "minimal_test",
        "orchestrator", 
        "simulation_orchestrator",
        "simple_geometry",
        "enhanced_lithography",
        "advanced_thermal"
    ]
    
    available_modules = []
    
    # Add cython directory to path
    cython_path = os.path.join(os.path.dirname(__file__), "src", "cython")
    if os.path.exists(cython_path):
        sys.path.insert(0, cython_path)
    
    for module in cython_modules:
        try:
            imported_module = __import__(module)
            print(f"  ✅ {module}: Available")
            available_modules.append(module)
        except ImportError as e:
            print(f"  ❌ {module}: Not available ({str(e)[:50]}...)")
        except Exception as e:
            print(f"  ⚠️  {module}: Import error ({str(e)[:50]}...)")
    
    print(f"\n📊 Cython Import Summary:")
    print(f"  Available: {len(available_modules)}/{len(cython_modules)}")
    print(f"  Modules: {', '.join(available_modules) if available_modules else 'None'}")
    
    return available_modules

def test_minimal_cython_functionality():
    """Test minimal Cython functionality if available"""
    print_section("Testing Minimal Cython Functionality")
    
    try:
        # Try to import minimal test module
        sys.path.insert(0, "src/cython")
        import minimal_test
        
        # Test basic functionality
        result1 = minimal_test.test_cython_integration()
        print(f"  ✅ Basic Cython: {result1}")
        
        # Test NumPy integration
        result2 = minimal_test.test_numpy_integration()
        print(f"  ✅ NumPy Integration: {result2}")
        
        # Test C++ string handling
        result3 = minimal_test.test_cpp_string()
        print(f"  ✅ C++ String: {result3}")
        
        # Test object creation
        result4 = minimal_test.create_test_object(42.0)
        print(f"  ✅ Object Creation: {result4}")
        
        print("\n🎉 All minimal Cython tests passed!")
        return True
        
    except ImportError as e:
        print(f"  ❌ Minimal test module not available: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Cython functionality test failed: {e}")
        return False

def test_orchestrator_backend():
    """Test orchestrator backend (Cython or mock)"""
    print_section("Testing Orchestrator Backend")
    
    try:
        # Test PySimulationOrchestrator import
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            bridge = EnhancedOrchestratorBridge()
            
            if bridge.cython_available:
                print("  ✅ Cython orchestrator backend available")
                
                # Test basic orchestrator functionality
                applications = bridge.get_industrial_applications()
                print(f"  ✅ Industrial applications: {len(applications)}")
                
                modules = bridge.get_available_modules()
                print(f"  ✅ Available modules: {len(modules)}")
                
                status = bridge.get_module_status()
                print(f"  ✅ Module status: {len(status)} modules")
                
                print("  🎉 Cython orchestrator backend working!")
                return True
            else:
                print("  ⚠️  Using mock orchestrator backend")
                
                # Test mock functionality
                applications = bridge.get_industrial_applications()
                print(f"  ✅ Mock applications: {len(applications)}")
                
                modules = bridge.get_available_modules()
                print(f"  ✅ Mock modules: {len(modules)}")
                
                print("  ✅ Mock orchestrator backend working!")
                return True
                
        except ImportError as e:
            print(f"  ❌ Enhanced orchestrator bridge not available: {e}")
            return False
            
    except Exception as e:
        print(f"  ❌ Orchestrator backend test failed: {e}")
        return False

def test_simulation_flow():
    """Test basic simulation flow execution"""
    print_section("Testing Simulation Flow Execution")
    
    try:
        from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
        
        bridge = EnhancedOrchestratorBridge()
        
        # Test industrial application execution
        applications = bridge.get_industrial_applications()
        if applications:
            app = applications[0]  # Use first application
            app_id = app['id']
            app_name = app['display_name']
            
            print(f"  🔧 Testing execution of: {app_name}")
            
            start_time = time.time()
            result = bridge.execute_industrial_application(app_id, "test_wafer")
            execution_time = time.time() - start_time
            
            if result.get('success', False):
                print(f"  ✅ Application executed successfully in {execution_time:.3f}s")
                print(f"  📊 Result: {result.get('application_name', 'Unknown')}")
                return True
            else:
                print(f"  ⚠️  Application execution failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("  ❌ No applications available for testing")
            return False
            
    except Exception as e:
        print(f"  ❌ Simulation flow test failed: {e}")
        return False

def test_build_system():
    """Test build system availability"""
    print_section("Testing Build System")
    
    # Check for build files
    build_files = [
        "scripts/build_cython.py",
        "scripts/build_cython_simple.py", 
        "build_and_test_system.py",
        "src/cython/setup_minimal_test.py"
    ]
    
    available_files = []
    for build_file in build_files:
        if os.path.exists(build_file):
            print(f"  ✅ {build_file}: Available")
            available_files.append(build_file)
        else:
            print(f"  ❌ {build_file}: Not found")
    
    # Check for compiled modules
    cython_dir = Path("src/cython")
    if cython_dir.exists():
        so_files = list(cython_dir.glob("*.so"))
        pyd_files = list(cython_dir.glob("*.pyd"))
        compiled_files = so_files + pyd_files
        
        print(f"\n  📦 Compiled modules: {len(compiled_files)}")
        for compiled_file in compiled_files:
            print(f"    ✅ {compiled_file.name}")
    else:
        print("  ⚠️  Cython directory not found")
    
    print(f"\n📊 Build System Summary:")
    print(f"  Build files: {len(available_files)}/{len(build_files)}")
    print(f"  Compiled modules: {len(compiled_files) if 'compiled_files' in locals() else 0}")
    
    return len(available_files) > 0

def run_quick_build_test():
    """Run a quick build test if possible"""
    print_section("Quick Build Test")
    
    # Check if we can run a simple build
    if os.path.exists("src/cython/setup_minimal_test.py"):
        print("  🔧 Attempting minimal test build...")
        try:
            result = subprocess.run([
                sys.executable, "setup_minimal_test.py", "build_ext", "--inplace"
            ], cwd="src/cython", capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("  ✅ Minimal test build successful")
                return True
            else:
                print(f"  ❌ Build failed: {result.stderr[:100]}...")
                return False
                
        except subprocess.TimeoutExpired:
            print("  ⏰ Build timed out")
            return False
        except Exception as e:
            print(f"  ❌ Build test failed: {e}")
            return False
    else:
        print("  ⚠️  No build script available for testing")
        return False

def main():
    """Main smoke test runner"""
    print_header("Cython Orchestrator Smoke Test")
    print(f"🕐 Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run tests
    results = {}
    
    results["Cython Imports"] = test_cython_imports()
    results["Minimal Functionality"] = test_minimal_cython_functionality()
    results["Orchestrator Backend"] = test_orchestrator_backend()
    results["Simulation Flow"] = test_simulation_flow()
    results["Build System"] = test_build_system()
    results["Quick Build"] = run_quick_build_test()
    
    # Generate summary
    print_header("Smoke Test Summary")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"📊 Test Results:")
    print(f"  Total Tests: {total_tests}")
    print(f"  ✅ Passed: {passed_tests}")
    print(f"  ❌ Failed: {failed_tests}")
    print(f"  📈 Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 Detailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if passed_tests == total_tests:
        print("  🎉 All tests passed! Cython orchestrator system is working.")
    elif passed_tests >= total_tests * 0.5:
        print("  ⚠️  Most tests passed. System is functional with some limitations.")
        print("  🔧 Consider building missing Cython modules for full functionality.")
    else:
        print("  ❌ Many tests failed. Cython system needs attention.")
        print("  🔧 Run: python scripts/build_cython_simple.py")
        print("  🔧 Or: python build_and_test_system.py")
    
    print(f"\n🕐 Completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return 0 if passed_tests >= total_tests * 0.5 else 1

if __name__ == "__main__":
    sys.exit(main())
