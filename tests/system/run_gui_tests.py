#!/usr/bin/env python3
"""
Enhanced SemiPRO GUI Test Runner
===============================

Comprehensive test runner for the Enhanced SemiPRO GUI system.
Runs smoke tests, unit tests, and integration tests.

Author: <PERSON><PERSON>
"""

import sys
import os
import time
import subprocess
import argparse
from pathlib import Path

def print_header(title):
    """Print formatted header"""
    print("\n" + "=" * 80)
    print(f"🚀 {title}")
    print("=" * 80)

def print_section(title):
    """Print formatted section"""
    print(f"\n📋 {title}")
    print("-" * 60)

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔧 {description}...")
    print(f"   Command: {' '.join(command)}")
    
    start_time = time.time()
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=300)
        execution_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"   ✅ Success ({execution_time:.2f}s)")
            if result.stdout.strip():
                print("   Output:")
                for line in result.stdout.strip().split('\n')[-10:]:  # Last 10 lines
                    print(f"     {line}")
            return True
        else:
            print(f"   ❌ Failed ({execution_time:.2f}s)")
            if result.stderr.strip():
                print("   Error:")
                for line in result.stderr.strip().split('\n')[-5:]:  # Last 5 lines
                    print(f"     {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ Timeout after 5 minutes")
        return False
    except Exception as e:
        print(f"   💥 Exception: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print_section("Checking Dependencies")
    
    dependencies = [
        ("python", ["python", "--version"]),
        ("PySide6", ["python", "-c", "import PySide6; print(f'PySide6 {PySide6.__version__}')"]),
        ("numpy", ["python", "-c", "import numpy; print(f'numpy {numpy.__version__}')"]),
        ("matplotlib", ["python", "-c", "import matplotlib; print(f'matplotlib {matplotlib.__version__}')"]),
    ]
    
    all_available = True
    for name, command in dependencies:
        if run_command(command, f"Checking {name}"):
            print(f"   ✅ {name} is available")
        else:
            print(f"   ❌ {name} is not available")
            all_available = False
    
    return all_available

def run_smoke_tests():
    """Run smoke tests"""
    print_section("Running Smoke Tests")
    
    if not Path("test_enhanced_gui_smoke.py").exists():
        print("   ❌ Smoke test file not found")
        return False
    
    return run_command(
        ["python", "test_enhanced_gui_smoke.py"],
        "Running Enhanced GUI smoke tests"
    )

def run_unit_tests():
    """Run unit tests"""
    print_section("Running Unit Tests")
    
    if not Path("test_orchestrator_bridge_unit.py").exists():
        print("   ❌ Unit test file not found")
        return False
    
    return run_command(
        ["python", "test_orchestrator_bridge_unit.py"],
        "Running Enhanced Orchestrator Bridge unit tests"
    )

def run_gui_launch_test():
    """Test GUI launch in headless mode"""
    print_section("Testing GUI Launch")
    
    # Test basic import
    import_success = run_command(
        ["python", "-c", "from src.python.gui.enhanced_simulator_gui import EnhancedSimulatorGUI; print('Import successful')"],
        "Testing GUI import"
    )
    
    if not import_success:
        return False
    
    # Test launcher script
    if Path("launch_enhanced_semipro.py").exists():
        print("\n🔧 Testing launcher script (dry run)...")
        try:
            # Import the launcher to check for syntax errors
            result = subprocess.run(
                ["python", "-c", "import launch_enhanced_semipro; print('Launcher import successful')"],
                capture_output=True, text=True, timeout=30
            )
            if result.returncode == 0:
                print("   ✅ Launcher script syntax is valid")
                return True
            else:
                print(f"   ❌ Launcher script has issues: {result.stderr}")
                return False
        except Exception as e:
            print(f"   ❌ Launcher test failed: {e}")
            return False
    else:
        print("   ⚠️  Launcher script not found, skipping")
        return True

def run_integration_tests():
    """Run integration tests"""
    print_section("Running Integration Tests")
    
    # Test orchestrator bridge integration
    integration_success = run_command(
        ["python", "-c", """
from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
from src.python.gui.enhanced_simulator_gui import GlobalLogWindow, StatusBroadcaster

# Test integration
bridge = EnhancedOrchestratorBridge()
log_window = GlobalLogWindow()
broadcaster = StatusBroadcaster()

# Test basic integration
applications = bridge.get_industrial_applications()
log_window.add_log_entry('Info', 'TEST', f'Found {len(applications)} applications')
broadcaster.broadcast_status('TEST', 'Integration test successful')

print('Integration test completed successfully')
"""],
        "Testing orchestrator bridge integration"
    )
    
    return integration_success

def generate_test_report(results):
    """Generate test report"""
    print_section("Test Report")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"\n📊 Test Summary:")
    print(f"   Total Tests: {total_tests}")
    print(f"   ✅ Passed: {passed_tests}")
    print(f"   ❌ Failed: {failed_tests}")
    print(f"   📈 Success Rate: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 Detailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    # Generate recommendations
    print(f"\n💡 Recommendations:")
    if failed_tests == 0:
        print("   🎉 All tests passed! The Enhanced SemiPRO GUI system is ready for use.")
        print("   🚀 You can launch the GUI with: python launch_enhanced_semipro.py --enhanced")
    else:
        print("   ⚠️  Some tests failed. Please address the following:")
        for test_name, result in results.items():
            if not result:
                print(f"     - Fix issues in: {test_name}")
        print("   🔧 Run individual test files for more detailed error information.")
    
    return failed_tests == 0

def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description="Enhanced SemiPRO GUI Test Runner")
    parser.add_argument("--smoke-only", action="store_true", help="Run only smoke tests")
    parser.add_argument("--unit-only", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration-only", action="store_true", help="Run only integration tests")
    parser.add_argument("--skip-deps", action="store_true", help="Skip dependency check")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    
    args = parser.parse_args()
    
    print_header("Enhanced SemiPRO GUI Test Suite")
    print(f"🕐 Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check dependencies unless skipped
    if not args.skip_deps:
        if not check_dependencies():
            print("\n❌ Dependency check failed. Use --skip-deps to continue anyway.")
            return 1
    
    # Initialize results
    results = {}
    
    # Run tests based on arguments
    if args.smoke_only:
        results["Smoke Tests"] = run_smoke_tests()
    elif args.unit_only:
        results["Unit Tests"] = run_unit_tests()
    elif args.integration_only:
        results["Integration Tests"] = run_integration_tests()
    elif args.quick:
        results["Smoke Tests"] = run_smoke_tests()
        results["GUI Launch Test"] = run_gui_launch_test()
    else:
        # Run all tests
        results["Smoke Tests"] = run_smoke_tests()
        results["Unit Tests"] = run_unit_tests()
        results["GUI Launch Test"] = run_gui_launch_test()
        results["Integration Tests"] = run_integration_tests()
    
    # Generate report
    success = generate_test_report(results)
    
    print(f"\n🕐 Completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print_header("Test Suite Complete")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
