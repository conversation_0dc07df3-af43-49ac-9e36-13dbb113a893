#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Deposition Module
Tests all components: wafer, deposition manager, industrial examples, and GUI integration
"""

import sys
import os
import time
from pathlib import Path

# Add src/python to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def test_wafer_functionality():
    """Test basic wafer functionality"""
    print("🔧 Testing Wafer Functionality")
    print("-" * 30)
    
    try:
        from wafer import Wafer, WaferFactory, create_wafer
        
        # Test basic wafer creation
        wafer = Wafer(diameter=300, thickness=775, material="silicon")
        wafer.initialize_grid(50, 50)
        
        print(f"✓ Basic wafer: {wafer}")
        print(f"  Area: {wafer.get_area():.1f} mm²")
        print(f"  Volume: {wafer.get_volume():.1f} mm³")
        
        # Test factory methods
        standard_wafer = WaferFactory.create_standard_wafer('12_inch', 'silicon')
        device_wafer = WaferFactory.create_device_wafer('cmos')
        
        print(f"✓ Standard wafer: {standard_wafer.diameter}mm {standard_wafer.material}")
        print(f"✓ Device wafer: {device_wafer.diameter}mm {device_wafer.material}")
        
        # Test layer operations
        wafer.apply_layer(0.1, "silicon_dioxide")
        wafer.add_film_layer(0.05, "aluminum")
        
        print(f"✓ Layers applied: {wafer.get_layer_count()} layers")
        print(f"  Total thickness: {wafer.get_total_thickness():.3f} μm")
        print(f"  Surface material: {wafer.get_surface_material()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Wafer test failed: {e}")
        return False

def test_deposition_manager():
    """Test deposition manager functionality"""
    print("\n🏭 Testing Deposition Manager")
    print("-" * 30)
    
    try:
        from deposition_manager import DepositionManager, DepositionConditions, DepositionTechnique, MaterialType
        from wafer import Wafer
        
        # Create components
        wafer = Wafer(diameter=300)
        wafer.initialize_grid(50, 50)
        manager = DepositionManager()
        
        print("✓ Manager and wafer created")
        
        # Test ALD simulation
        ald_conditions = DepositionConditions(
            technique=DepositionTechnique.ALD,
            material=MaterialType.HAFNIUM_OXIDE,
            temperature=280.0,
            pressure=0.3,
            target_thickness=0.002,
            precursors=['TDMAH', 'H2O']
        )
        
        ald_result = manager.simulate_deposition(wafer, ald_conditions)
        print(f"✓ ALD simulation: {ald_result.final_thickness:.6f} μm, {ald_result.uniformity:.3f}% uniformity")
        
        # Test CVD simulation
        cvd_conditions = DepositionConditions(
            technique=DepositionTechnique.PECVD,
            material=MaterialType.SILICON_DIOXIDE,
            temperature=350.0,
            pressure=1.0,
            target_thickness=0.5,
            power=300.0
        )
        
        cvd_result = manager.simulate_deposition(wafer, cvd_conditions)
        print(f"✓ CVD simulation: {cvd_result.final_thickness:.3f} μm, {cvd_result.uniformity:.3f}% uniformity")
        
        # Test PVD simulation
        pvd_conditions = DepositionConditions(
            technique=DepositionTechnique.PVD_SPUTTERING,
            material=MaterialType.COPPER,
            temperature=200.0,
            pressure=0.01,
            target_thickness=1.0,
            power=500.0,
            bias_voltage=100.0
        )
        
        pvd_result = manager.simulate_deposition(wafer, pvd_conditions)
        print(f"✓ PVD simulation: {pvd_result.final_thickness:.3f} μm, {pvd_result.uniformity:.3f}% uniformity")
        
        # Check wafer state
        print(f"✓ Wafer state: {wafer.get_layer_count()} layers, {wafer.get_total_thickness():.3f} μm total")
        
        return True
        
    except Exception as e:
        print(f"❌ Deposition manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_industrial_examples():
    """Test industrial examples functionality"""
    print("\n🏭 Testing Industrial Examples")
    print("-" * 30)
    
    try:
        from enhanced_industrial_deposition_examples import EnhancedIndustrialDepositionExamples
        
        examples = EnhancedIndustrialDepositionExamples()
        
        # Get available applications
        apps = examples.get_available_applications()
        print(f"✓ Found {len(apps)} industrial applications")
        
        # Test application details
        for app in apps[:3]:  # Test first 3
            details = examples.get_application_details(app)
            print(f"  • {details['name']}: {details['device_type']} {details['technology_node']}")
        
        # Test simulations
        test_apps = ['cmos_gate_stack', 'power_semiconductor', 'advanced_packaging']
        successful_sims = 0
        
        for app in test_apps:
            try:
                result = examples.simulate_industrial_application(app)
                quality = result['specification_analysis']['quality_score']
                meets_specs = result['specification_analysis']['meets_specifications']
                status = '✅' if meets_specs else '⚠️'
                
                print(f"{status} {app}: Quality {quality:.2f}, Specs: {meets_specs}")
                successful_sims += 1
                
            except Exception as e:
                print(f"❌ {app}: Failed - {str(e)}")
        
        print(f"✓ Completed {successful_sims}/{len(test_apps)} simulations successfully")
        
        return successful_sims > 0
        
    except Exception as e:
        print(f"❌ Industrial examples test failed: {e}")
        return False

def test_gui_components():
    """Test GUI components (without launching full GUI)"""
    print("\n🖥️ Testing GUI Components")
    print("-" * 25)
    
    try:
        # Test PySide6 availability
        try:
            from PySide6.QtWidgets import QApplication
            print("✓ PySide6 available")
            pyside6_available = True
        except ImportError:
            print("⚠️ PySide6 not available")
            pyside6_available = False
        
        # Test GUI module imports
        try:
            from gui.enhanced_deposition_panel import EnhancedDepositionPanel
            print("✓ Enhanced deposition panel importable")
        except Exception as e:
            print(f"⚠️ Enhanced deposition panel import issue: {e}")
        
        # Test launcher script
        launcher_path = Path("launch_enhanced_deposition_gui.py")
        if launcher_path.exists():
            print("✓ GUI launcher script exists")
        else:
            print("❌ GUI launcher script missing")
        
        return pyside6_available
        
    except Exception as e:
        print(f"❌ GUI component test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🧪 SemiPRO Enhanced Deposition Module - Comprehensive Test")
    print("=" * 60)
    
    start_time = time.time()
    
    # Run all tests
    test_results = {
        'wafer': test_wafer_functionality(),
        'deposition_manager': test_deposition_manager(),
        'industrial_examples': test_industrial_examples(),
        'gui_components': test_gui_components()
    }
    
    end_time = time.time()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 15)
    
    passed = sum(test_results.values())
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name.replace('_', ' ').title()}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Test duration: {end_time - start_time:.2f} seconds")
    
    if passed == total:
        print("\n🎉 All tests passed! Enhanced Deposition Module is fully functional.")
        return True
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Some functionality may be limited.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
