"""
Thermal Module Integration Tests
===============================

Integration tests for thermal module with GUI, database, and other modules.

Author: Dr<PERSON> <PERSON><PERSON>
"""

import unittest
import sys
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
import numpy as np

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

try:
    from python.gui.enhanced_thermal_panel import EnhancedThermalPanel
    from python.thermal.database_integration import ThermalDatabaseIntegration
    from python.thermal.industrial_applications import IndustrialThermalApplications, IndustrialApplicationType
    THERMAL_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Thermal modules not available for integration testing: {e}")
    THERMAL_MODULES_AVAILABLE = False

class TestThermalGUIIntegration(unittest.TestCase):
    """Test thermal module GUI integration"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for GUI tests"""
        if not GUI_AVAILABLE or not THERMAL_MODULES_AVAILABLE:
            return
        
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Set up test fixtures"""
        if not GUI_AVAILABLE or not THERMAL_MODULES_AVAILABLE:
            self.skipTest("GUI or thermal modules not available")
    
    def test_thermal_panel_initialization(self):
        """Test thermal panel initialization"""
        with patch('python.thermal.database_integration.ThermalDatabaseIntegration'):
            panel = EnhancedThermalPanel(enable_database=False)
            
            self.assertIsNotNone(panel)
            self.assertFalse(panel.database_enabled)
    
    def test_thermal_panel_with_database(self):
        """Test thermal panel with database integration"""
        mock_db = Mock()
        
        with patch('python.thermal.database_integration.ThermalDatabaseIntegration', return_value=mock_db):
            panel = EnhancedThermalPanel(enable_database=True)
            
            self.assertIsNotNone(panel)
            # Database availability depends on import success
    
    def test_industrial_examples_loading(self):
        """Test loading industrial examples in GUI"""
        with patch('python.thermal.database_integration.ThermalDatabaseIntegration'):
            panel = EnhancedThermalPanel(enable_database=False)
            
            # Check that industrial examples are loaded
            self.assertIsInstance(panel.industrial_examples_cache, dict)
            self.assertGreater(len(panel.industrial_examples_cache), 0)
    
    def test_thermal_simulation_workflow(self):
        """Test complete thermal simulation workflow"""
        with patch('python.thermal.database_integration.ThermalDatabaseIntegration'):
            panel = EnhancedThermalPanel(enable_database=False)
            
            # Mock simulation parameters
            device_specs = {
                'power_dissipation': 100.0,
                'thermal_resistance': 1.0,
                'ambient_temperature': 25.0
            }
            
            # Run simulation
            results = panel.run_thermal_simulation(device_specs, process_name="Test Simulation")
            
            self.assertIsInstance(results, dict)
            self.assertIn('success', results)
    
    def test_visualization_integration(self):
        """Test visualization integration with thermal panel"""
        with patch('python.thermal.database_integration.ThermalDatabaseIntegration'):
            panel = EnhancedThermalPanel(enable_database=False)
            
            # Create test temperature data
            temperature_data = np.random.rand(50, 50) * 100 + 298.15
            
            # Test visualization creation
            if hasattr(panel, 'create_temperature_visualization'):
                fig = panel.create_temperature_visualization(temperature_data)
                self.assertIsNotNone(fig)

class TestThermalDatabaseIntegration(unittest.TestCase):
    """Test thermal module database integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
    
    def test_database_schema_creation(self):
        """Test database schema creation"""
        mock_db = Mock()
        mock_cursor = Mock()
        mock_db.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        with patch('python.thermal.database_integration.get_database_manager', return_value=mock_db):
            db_integration = ThermalDatabaseIntegration()
            
            # Verify schema creation was attempted
            mock_cursor.execute.assert_called()
    
    def test_material_database_operations(self):
        """Test material database operations"""
        mock_db = Mock()
        mock_cursor = Mock()
        mock_db.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        with patch('python.thermal.database_integration.get_database_manager', return_value=mock_db):
            db_integration = ThermalDatabaseIntegration()
            
            # Test adding material
            material_data = {
                'material_name': 'Test Material',
                'thermal_conductivity_w_mk': 100.0,
                'specific_heat_j_kg_k': 500.0,
                'density_kg_m3': 2000.0,
                'thermal_expansion_per_k': 1e-6,
                'temperature_range_c': [25.0, 1000.0]
            }
            
            mock_cursor.fetchone.return_value = {'material_id': 'test-uuid'}
            result = db_integration.add_thermal_material(material_data)
            
            self.assertEqual(result, 'test-uuid')
    
    def test_process_tracking(self):
        """Test thermal process tracking"""
        mock_db = Mock()
        mock_cursor = Mock()
        mock_db.get_cursor.return_value.__enter__.return_value = mock_cursor
        
        with patch('python.thermal.database_integration.get_database_manager', return_value=mock_db):
            db_integration = ThermalDatabaseIntegration()
            
            # Test process creation
            process_data = {
                'process_name': 'Integration Test Process',
                'process_type': 'RTP',
                'target_temperature_c': 1000.0,
                'hold_time_seconds': 30
            }
            
            process_id = db_integration.create_thermal_process(process_data)
            self.assertIsInstance(process_id, str)
            
            # Test process start
            db_integration.start_thermal_process(process_id)
            
            # Test process completion
            results = {
                'actual_temperature_c': 998.0,
                'process_success': True,
                'uniformity_achieved_percent': 95.0,
                'temperature_deviation_c': 2.0
            }
            db_integration.complete_thermal_process(process_id, results)

class TestThermalIndustrialApplications(unittest.TestCase):
    """Test thermal industrial applications integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
        
        self.industrial_apps = IndustrialThermalApplications()
    
    def test_cpu_thermal_analysis_integration(self):
        """Test CPU thermal analysis integration"""
        # Get CPU application
        cpu_apps = self.industrial_apps.get_application_by_type(IndustrialApplicationType.CPU_COOLING)
        self.assertGreater(len(cpu_apps), 0)
        
        cpu_app = cpu_apps[0]
        
        # Test thermal performance calculation
        performance = self.industrial_apps.calculate_thermal_performance(
            cpu_app,
            cooling_solution="liquid_cooler_240mm",
            ambient_temp=25.0
        )
        
        self.assertIn('junction_temperature', performance)
        self.assertIn('meets_requirements', performance)
        self.assertIsInstance(performance['meets_requirements'], bool)
    
    def test_power_electronics_integration(self):
        """Test power electronics integration"""
        power_apps = self.industrial_apps.get_application_by_type(IndustrialApplicationType.POWER_ELECTRONICS)
        
        if power_apps:
            power_app = power_apps[0]
            
            performance = self.industrial_apps.calculate_thermal_performance(
                power_app,
                cooling_solution="liquid_cooled_baseplate",
                ambient_temp=25.0
            )
            
            self.assertIn('junction_temperature', performance)
            self.assertIn('hotspots', performance)
    
    def test_led_thermal_integration(self):
        """Test LED thermal management integration"""
        led_apps = self.industrial_apps.get_application_by_type(IndustrialApplicationType.LED_THERMAL_MANAGEMENT)
        
        if led_apps:
            led_app = led_apps[0]
            
            performance = self.industrial_apps.calculate_thermal_performance(
                led_app,
                cooling_solution="active_cooling_fan",
                ambient_temp=25.0
            )
            
            self.assertIn('junction_temperature', performance)
            self.assertIn('performance_rating', performance)
    
    def test_report_generation_integration(self):
        """Test thermal report generation integration"""
        # Test with CPU application
        cpu_apps = self.industrial_apps.get_application_by_type(IndustrialApplicationType.CPU_COOLING)
        if cpu_apps:
            cpu_app = cpu_apps[0]
            
            report = self.industrial_apps.generate_thermal_report(
                cpu_app['name'],
                "liquid_cooler_240mm",
                ambient_temp=25.0
            )
            
            self.assertIsInstance(report, str)
            self.assertIn("THERMAL ANALYSIS REPORT", report)
            self.assertIn(cpu_app['name'], report)
            self.assertIn("INDUSTRIAL CONTEXT", report)

class TestThermalModuleWorkflow(unittest.TestCase):
    """Test complete thermal module workflow"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
    
    def test_complete_thermal_workflow(self):
        """Test complete thermal analysis workflow"""
        # 1. Initialize industrial applications
        industrial_apps = IndustrialThermalApplications()
        
        # 2. Get an application
        cpu_apps = industrial_apps.get_application_by_type(IndustrialApplicationType.CPU_COOLING)
        self.assertGreater(len(cpu_apps), 0)
        
        cpu_app = cpu_apps[0]
        
        # 3. Calculate thermal performance
        performance = industrial_apps.calculate_thermal_performance(
            cpu_app,
            cooling_solution="liquid_cooler_240mm",
            ambient_temp=25.0
        )
        
        self.assertIn('junction_temperature', performance)
        self.assertIn('hotspots', performance)
        
        # 4. Generate report
        report = industrial_apps.generate_thermal_report(
            cpu_app['name'],
            "liquid_cooler_240mm",
            ambient_temp=25.0
        )
        
        self.assertIsInstance(report, str)
        self.assertGreater(len(report), 100)  # Should be substantial report
        
        # 5. Verify workflow completion
        self.assertTrue(performance['meets_requirements'] is not None)

class TestThermalModuleErrorHandling(unittest.TestCase):
    """Test thermal module error handling"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
    
    def test_database_connection_failure(self):
        """Test handling of database connection failures"""
        with patch('python.thermal.database_integration.get_database_manager', side_effect=Exception("DB Connection Failed")):
            # Should not raise exception, should handle gracefully
            try:
                db_integration = ThermalDatabaseIntegration()
                # Should still be able to create instance
                self.assertIsNotNone(db_integration)
            except Exception as e:
                self.fail(f"Database integration should handle connection failures gracefully: {e}")
    
    def test_invalid_application_name(self):
        """Test handling of invalid application names"""
        industrial_apps = IndustrialThermalApplications()
        
        # Test with non-existent application
        app = industrial_apps.get_application_by_name("Non-Existent Application")
        self.assertIsNone(app)
        
        # Test report generation with invalid name
        report = industrial_apps.generate_thermal_report(
            "Non-Existent Application",
            "some_cooling",
            ambient_temp=25.0
        )
        
        self.assertIn("not found", report)
    
    def test_invalid_cooling_solution(self):
        """Test handling of invalid cooling solutions"""
        industrial_apps = IndustrialThermalApplications()
        
        cpu_apps = industrial_apps.get_application_by_type(IndustrialApplicationType.CPU_COOLING)
        if cpu_apps:
            cpu_app = cpu_apps[0]
            
            performance = industrial_apps.calculate_thermal_performance(
                cpu_app,
                cooling_solution="non_existent_cooling",
                ambient_temp=25.0
            )
            
            self.assertIn('error', performance)

if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    if THERMAL_MODULES_AVAILABLE:
        if GUI_AVAILABLE:
            test_suite.addTest(unittest.makeSuite(TestThermalGUIIntegration))
        
        test_suite.addTest(unittest.makeSuite(TestThermalDatabaseIntegration))
        test_suite.addTest(unittest.makeSuite(TestThermalIndustrialApplications))
        test_suite.addTest(unittest.makeSuite(TestThermalModuleWorkflow))
        test_suite.addTest(unittest.makeSuite(TestThermalModuleErrorHandling))
    else:
        print("Skipping thermal integration tests - modules not available")
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\nIntegration Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100)
        print(f"Success rate: {success_rate:.1f}%")
    else:
        print("No tests were run")
