#!/usr/bin/env python3
"""
Comprehensive Oxidation Module Test
===================================

Test the oxidation module with various industrial thermal oxidation scenarios
and validate the GUI integration with real C++ backend.
"""

import sys
import os
import json
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_oxidation_scenarios():
    """Test various oxidation scenarios"""
    
    print("🔥 COMPREHENSIVE OXIDATION MODULE TEST")
    print("=" * 50)
    
    # Industrial oxidation scenarios
    scenarios = {
        "gate_oxide": {
            "name": "Gate Oxide Growth",
            "config": {
                "wafer": {
                    "name": "gate_oxide_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 900.0,  # 900°C
                    "time": 0.5,  # 30 minutes
                    "atmosphere": "dry",
                    "target_thickness": 0.005  # 5nm gate oxide
                }
            }
        },
        "thick_oxide": {
            "name": "Thick Oxide Growth",
            "config": {
                "wafer": {
                    "name": "thick_oxide_wafer",
                    "diameter": 200.0,
                    "thickness": 0.525,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 1100.0,  # 1100°C (high temp dry)
                    "time": 1.0,  # 1 hour
                    "atmosphere": "dry",  # Use dry oxidation
                    "target_thickness": 0.5  # 500nm thick oxide
                }
            }
        },
        "tunnel_oxide": {
            "name": "Tunnel Oxide (Flash Memory)",
            "config": {
                "wafer": {
                    "name": "tunnel_oxide_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 850.0,  # 850°C
                    "time": 0.1,  # 6 minutes
                    "atmosphere": "dry",
                    "target_thickness": 0.008  # 8nm tunnel oxide
                }
            }
        },
        "sacrificial_oxide": {
            "name": "Sacrificial Oxide",
            "config": {
                "wafer": {
                    "name": "sacrificial_wafer",
                    "diameter": 200.0,
                    "thickness": 0.525,
                    "material": "Silicon",
                    "crystal_orientation": "111"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 900.0,  # 900°C (lower temp)
                    "time": 0.1,  # 6 minutes (shorter time)
                    "atmosphere": "dry",  # Use dry instead of wet
                    "target_thickness": 0.02  # 20nm sacrificial oxide
                }
            }
        }
    }
    
    success_count = 0
    total_scenarios = len(scenarios)
    results_summary = []
    
    for scenario_id, scenario in scenarios.items():
        print(f"\n🔬 Testing: {scenario['name']}")
        print("-" * 50)
        
        # Create config file
        config_file = f"config/oxidation_{scenario_id}.json"
        os.makedirs("config", exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(scenario['config'], f, indent=2)
        
        try:
            # Run C++ simulation
            simulator_path = project_root / "build" / "simulator"
            
            print(f"🚀 Running oxidation simulation...")
            print(f"   Temperature: {scenario['config']['process']['temperature']} °C")
            print(f"   Time: {scenario['config']['process']['time']} hours")
            print(f"   Atmosphere: {scenario['config']['process']['atmosphere']}")
            print(f"   Target: {scenario['config']['process']['target_thickness']} μm")
            
            start_time = time.time()
            result = subprocess.run([
                str(simulator_path),
                "--process", "oxidation",
                "--config", config_file
            ], capture_output=True, text=True, timeout=30)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"⏱️  Execution time: {execution_time:.3f} seconds")
            
            if result.returncode == 0:
                print("✅ Simulation: SUCCESS")
                
                # Parse results using GUI parser
                from gui.enhanced_main_window import SimulationWorker
                worker = SimulationWorker("oxidation", scenario['config'])
                parsed_results = worker.parse_simulation_output(result.stdout)
                
                print(f"📊 Results parsed: {len(parsed_results)} metrics")
                
                scenario_result = {
                    'name': scenario['name'],
                    'success': True,
                    'execution_time': execution_time,
                    'results': parsed_results
                }
                
                # Display key results
                for key, value in parsed_results.items():
                    if key == 'oxide_thickness':
                        target = scenario['config']['process']['target_thickness']
                        print(f"   • Oxide Thickness: {value:.6f} μm (Target: {target} μm)")
                    elif key == 'growth_rate':
                        print(f"   • Growth Rate: {value:.6f} μm/h")
                    elif key == 'oxidation_regime':
                        print(f"   • Oxidation Regime: {value}")
                    elif key == 'stress_mpa':
                        print(f"   • Stress: {value:.1f} MPa")
                    elif key == 'quality_score':
                        print(f"   • Quality Score: {value:.1f}")
                    else:
                        print(f"   • {key.replace('_', ' ').title()}: {value}")
                
                success_count += 1
                
            else:
                print("❌ Simulation: FAILED")
                scenario_result = {
                    'name': scenario['name'],
                    'success': False,
                    'execution_time': execution_time,
                    'error': result.stderr.strip()
                }
                
            results_summary.append(scenario_result)
                
        except subprocess.TimeoutExpired:
            print("⏰ Simulation timed out")
        except Exception as e:
            print(f"💥 Exception: {e}")
    
    # Final summary
    print(f"\n🏆 OXIDATION MODULE TEST RESULTS")
    print("=" * 50)
    print(f"✅ Successful scenarios: {success_count}/{total_scenarios}")
    print(f"📊 Success rate: {success_count/total_scenarios*100:.1f}%")
    
    if success_count == total_scenarios:
        print("🎉 ALL OXIDATION SCENARIOS WORKING!")
        print("🔥 Industrial thermal oxidation processes validated:")
        for result in results_summary:
            if result['success']:
                print(f"   ✅ {result['name']} ({result['execution_time']:.3f}s)")
        
        print(f"\n🚀 GUI Integration Ready:")
        print("   • Launch: python launch_gui.py")
        print("   • Navigate to Oxidation tab")
        print("   • Test with industrial parameters")
        print("   • View real-time oxide growth visualization")
        return True
    else:
        print("⚠️  Some scenarios failed - check implementation")
        return False

if __name__ == "__main__":
    success = test_oxidation_scenarios()
    sys.exit(0 if success else 1)
