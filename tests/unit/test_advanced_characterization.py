#!/usr/bin/env python3
"""
Test script for advanced oxidation characterization functionality
Tests C-V analysis, Dit calculation, breakdown prediction, and reliability analysis
"""

import sys
import os
import traceback
import numpy as np
import matplotlib.pyplot as plt

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_characterization_analyzer():
    """Test the OxidationCharacterizationAnalyzer class"""
    print("=" * 60)
    print("TESTING OXIDATION CHARACTERIZATION ANALYZER")
    print("=" * 60)
    
    try:
        from oxidation import OxidationManager, OxidationCharacterizationAnalyzer
        from geometry import GeometryManager
        
        # Initialize managers
        print("\n1. Initializing oxidation and geometry managers...")
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager()
        
        # Create characterization analyzer
        print("2. Creating characterization analyzer...")
        analyzer = OxidationCharacterizationAnalyzer(oxidation_manager)
        
        # Get test wafer from geometry manager
        print("3. Getting test wafer...")
        wafer = geometry_manager.wafer

        # Initialize grid for the wafer
        geometry_manager.initialize_grid(100, 100)
        
        # Test parameters
        test_thickness = 10.0  # nm
        test_area = 1e-4  # cm²
        test_temperature = 300.0  # K
        
        print(f"\nTest Parameters:")
        print(f"  Oxide Thickness: {test_thickness} nm")
        print(f"  Test Area: {test_area} cm²")
        print(f"  Temperature: {test_temperature} K")
        
        # Test 1: C-V Analysis
        print("\n" + "="*50)
        print("TEST 1: C-V ANALYSIS")
        print("="*50)
        
        cv_results = analyzer.perform_cv_analysis(
            thickness=test_thickness,
            area=test_area,
            temperature=test_temperature,
            voltage_range=(-5.0, 5.0),
            num_points=100
        )
        
        print(f"C-V Analysis Results:")
        print(f"  Oxide Capacitance: {cv_results['oxide_capacitance']:.2e} F/cm²")
        print(f"  Flat Band Voltage: {cv_results['flat_band_voltage']:.3f} V")
        print(f"  Voltage Points: {len(cv_results['voltage'])}")
        print(f"  Capacitance Range: {min(cv_results['capacitance']):.2e} to {max(cv_results['capacitance']):.2e} F/cm²")
        
        # Test 2: Interface State Density
        print("\n" + "="*50)
        print("TEST 2: INTERFACE STATE DENSITY")
        print("="*50)
        
        dit = analyzer.calculate_interface_state_density(
            thickness=test_thickness,
            temperature=test_temperature,
            frequency=1e6
        )
        
        print(f"Interface State Density Results:")
        print(f"  Dit: {dit:.2e} states/cm²/eV")
        print(f"  Quality Grade: {'Excellent' if dit < 1e11 else 'Good' if dit < 5e11 else 'Fair'}")
        
        # Test 3: Breakdown Voltage Prediction
        print("\n" + "="*50)
        print("TEST 3: BREAKDOWN VOLTAGE PREDICTION")
        print("="*50)
        
        breakdown_voltage = analyzer.predict_breakdown_voltage(
            thickness=test_thickness,
            quality=dit,
            temperature=test_temperature,
            area=test_area
        )
        
        breakdown_field = breakdown_voltage / (test_thickness * 1e-7)
        print(f"Breakdown Voltage Prediction:")
        print(f"  Breakdown Voltage: {breakdown_voltage:.2f} V")
        print(f"  Breakdown Field: {breakdown_field:.2e} V/cm")
        print(f"  Safety Margin (80%): {breakdown_voltage * 0.8:.2f} V")
        
        # Test 4: Reliability Analysis
        print("\n" + "="*50)
        print("TEST 4: RELIABILITY ANALYSIS (TDDB)")
        print("="*50)
        
        stress_voltage = breakdown_voltage * 0.8
        reliability_results = analyzer.perform_reliability_analysis(
            thickness=test_thickness,
            stress_voltage=stress_voltage,
            temperature=125.0,  # Stress temperature
            time_max=1e6
        )
        
        print(f"TDDB Reliability Analysis:")
        print(f"  Time to Breakdown: {reliability_results['time_to_breakdown']:.2e} seconds")
        print(f"  Time to Breakdown: {reliability_results['time_to_breakdown']/3.15e7:.2f} years")
        print(f"  Stress Voltage: {stress_voltage:.2f} V")
        print(f"  Stress Temperature: 125°C")
        print(f"  Failure Rate Points: {len(reliability_results['failure_rate'])}")
        
        # Test 5: Comprehensive Characterization
        print("\n" + "="*50)
        print("TEST 5: COMPREHENSIVE CHARACTERIZATION")
        print("="*50)
        
        process_params = {
            'target_thickness': test_thickness,
            'temperature': 1000.0,
            'time': 2.0,
            'pressure': 760.0
        }
        
        comprehensive_results = analyzer.perform_comprehensive_characterization(wafer, process_params)
        
        print(f"Comprehensive Characterization Results:")
        print(f"  Final Thickness: {comprehensive_results['final_thickness']:.2f} nm")
        print(f"  Uniformity: {comprehensive_results['uniformity']:.1f}%")
        print(f"  Breakdown Voltage: {comprehensive_results['breakdown_voltage']:.2f} V")
        print(f"  Interface State Density: {comprehensive_results['electrical_quality']:.2e} states/cm²/eV")
        print(f"  Flat Band Voltage: {comprehensive_results['flat_band_voltage']:.3f} V")
        print(f"  Threshold Voltage: {comprehensive_results['threshold_voltage']:.3f} V")
        print(f"  Time to Breakdown: {comprehensive_results['time_to_breakdown']:.2e} s")
        print(f"  Leakage Current: {comprehensive_results['leakage_current']:.2e} A/cm²")
        print(f"  Reliability Factor: {comprehensive_results['reliability_factor']:.3f}")
        
        print("\n" + "="*60)
        print("ALL CHARACTERIZATION TESTS COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\nERROR in characterization testing: {e}")
        traceback.print_exc()
        return False

def test_characterization_gui():
    """Test the characterization GUI functionality"""
    print("\n" + "="*60)
    print("TESTING CHARACTERIZATION GUI")
    print("="*60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.oxidation_panel import OxidationPanel
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("1. Creating oxidation panel...")
        panel = OxidationPanel()
        
        print("2. Checking characterization tab...")
        if hasattr(panel, 'characterization_tab'):
            print("   ✓ Characterization tab created successfully")
        else:
            print("   ✗ Characterization tab not found")
            return False
        
        print("3. Checking characterization analyzer...")
        if hasattr(panel, 'characterization_analyzer'):
            print("   ✓ Characterization analyzer initialized")
        else:
            print("   ✗ Characterization analyzer not found")
            return False
        
        print("4. Checking characterization methods...")
        methods = ['perform_cv_analysis', 'calculate_dit', 'predict_breakdown', 
                  'perform_reliability_analysis', 'perform_comprehensive_analysis']
        
        for method in methods:
            if hasattr(panel, method):
                print(f"   ✓ {method} method available")
            else:
                print(f"   ✗ {method} method missing")
                return False
        
        print("5. Checking visualization methods...")
        viz_methods = ['visualize_cv_results', 'visualize_dit_results', 
                      'visualize_breakdown_results', 'visualize_reliability_results',
                      'visualize_comprehensive_results']
        
        for method in viz_methods:
            if hasattr(panel, method):
                print(f"   ✓ {method} method available")
            else:
                print(f"   ✗ {method} method missing")
                return False
        
        print("\n" + "="*60)
        print("CHARACTERIZATION GUI TESTS COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\nERROR in GUI testing: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("Advanced Oxidation Characterization Test Suite")
    print("=" * 60)
    
    # Test 1: Characterization Analyzer
    analyzer_success = test_characterization_analyzer()
    
    # Test 2: GUI Integration
    gui_success = test_characterization_gui()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Characterization Analyzer: {'✓ PASS' if analyzer_success else '✗ FAIL'}")
    print(f"GUI Integration: {'✓ PASS' if gui_success else '✗ FAIL'}")
    
    if analyzer_success and gui_success:
        print("\n🎉 ALL TESTS PASSED! Advanced characterization is ready!")
        return 0
    else:
        print("\n❌ SOME TESTS FAILED! Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
