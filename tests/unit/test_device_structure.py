#!/usr/bin/env python3
"""
Test Device Structure
====================

Test script to verify that device structures are properly created
and contain the expected data for visualization.
"""

import sys
import os
from pathlib import Path

# Add src/python to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_device_structure():
    """Test device structure creation"""
    print("🧪 Testing Device Structure Creation")
    print("=" * 40)
    
    try:
        # Import required modules
        from enhanced_industrial_etching_examples import EnhancedIndustrialEtchingExamples
        from enhanced_etching_bridge import DeviceStructure
        
        # Create examples instance
        examples = EnhancedIndustrialEtchingExamples()
        print("✓ Enhanced etching examples loaded")
        
        # Test each application
        applications = examples.get_available_applications()
        print(f"✓ Found {len(applications)} applications")
        
        success_count = 0
        for app_name in applications:
            print(f"\n📱 Testing {app_name}...")
            try:
                result = examples.simulate_industrial_application(app_name)
                
                if result.get('success', False):
                    device = result.get('device_structure')
                    
                    if device and isinstance(device, DeviceStructure):
                        print(f"  ✓ DeviceStructure object created")
                        print(f"  ✓ Name: {device.name}")
                        print(f"  ✓ Type: {device.device_type}")
                        print(f"  ✓ Technology: {device.technology_node}")
                        print(f"  ✓ Layers: {len(device.layers)}")
                        print(f"  ✓ Features: {len(device.target_features)}")
                        
                        # Print layer details
                        if device.layers:
                            print(f"    Layer details:")
                            for i, layer in enumerate(device.layers):
                                print(f"      {i+1}. {layer['material']} - {layer['thickness']:.2f}μm ({layer['purpose']})")
                        
                        # Print feature details
                        if device.target_features:
                            print(f"    Feature details:")
                            for i, feature in enumerate(device.target_features):
                                print(f"      {i+1}. {feature['name']} - {feature['width']:.2f}μm x {feature['depth']:.2f}μm (AR: {feature['aspect_ratio']:.1f})")
                        
                        # Print critical dimensions
                        if device.critical_dimensions:
                            print(f"    Critical dimensions:")
                            for key, value in device.critical_dimensions.items():
                                print(f"      {key}: {value:.2f}μm")
                        
                        success_count += 1
                    else:
                        print(f"  ❌ Device structure is not a DeviceStructure object: {type(device)}")
                else:
                    print(f"  ❌ Simulation failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        print(f"\n📊 Summary: {success_count}/{len(applications)} applications successful")
        return success_count == len(applications)
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_data():
    """Test that visualization data is properly structured"""
    print("\n🎨 Testing Visualization Data Structure")
    print("=" * 40)
    
    try:
        from enhanced_industrial_etching_examples import EnhancedIndustrialEtchingExamples
        
        examples = EnhancedIndustrialEtchingExamples()
        
        # Test with MEMS accelerometer
        result = examples.simulate_industrial_application('mems_accelerometer')
        
        if result.get('success', False):
            print("✓ Simulation successful")
            
            # Check device structure
            device = result.get('device_structure')
            if device:
                print(f"✓ Device structure available: {type(device)}")
                
                # Test visualization compatibility
                print(f"\n🔍 Testing visualization compatibility:")
                
                # Test attribute access
                try:
                    name = getattr(device, 'name', 'N/A')
                    device_type = getattr(device, 'device_type', 'N/A')
                    layers = getattr(device, 'layers', [])
                    features = getattr(device, 'target_features', [])
                    critical_dims = getattr(device, 'critical_dimensions', {})
                    
                    print(f"  ✓ Name access: {name}")
                    print(f"  ✓ Type access: {device_type}")
                    print(f"  ✓ Layers access: {len(layers)} layers")
                    print(f"  ✓ Features access: {len(features)} features")
                    print(f"  ✓ Critical dims access: {len(critical_dims)} dimensions")
                    
                    # Test specific data needed for visualization
                    width = critical_dims.get('width', 10.0)
                    depth = critical_dims.get('depth', 1.0)
                    total_etch_depth = critical_dims.get('total_etch_depth', depth)
                    
                    print(f"  ✓ Width for visualization: {width:.2f}μm")
                    print(f"  ✓ Depth for visualization: {depth:.2f}μm")
                    print(f"  ✓ Total etch depth: {total_etch_depth:.2f}μm")
                    
                    print(f"\n✅ Device structure is ready for visualization!")
                    return True
                    
                except Exception as e:
                    print(f"  ❌ Attribute access error: {e}")
                    return False
            else:
                print(f"❌ No device structure in result")
                return False
        else:
            print(f"❌ Simulation failed")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main test function"""
    print("🔬 Enhanced Etching Device Structure Test")
    print("=" * 50)
    
    # Test device structure creation
    test1_success = test_device_structure()
    
    # Test visualization data
    test2_success = test_visualization_data()
    
    print(f"\n🎯 Final Results:")
    print(f"  Device Structure Test: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"  Visualization Data Test: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 All tests passed! Device structures are properly created.")
        print(f"   The enhanced etching module now creates DeviceStructure objects")
        print(f"   with proper layers, features, and dimensions for visualization.")
        return 0
    else:
        print(f"\n❌ Some tests failed. Device structure creation needs fixes.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
