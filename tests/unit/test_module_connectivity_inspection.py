#!/usr/bin/env python3
"""
Module Connectivity Inspection Test
===================================

Comprehensive inspection of:
1. Each module's backend connectivity
2. Inter-module wafer connectivity
3. Current persistence/database layer
4. Recommendations for PostgreSQL schema

Author: <PERSON><PERSON>
"""

import sys
import os
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def inspect_module_connectivity():
    """Inspect each module's backend connectivity"""
    print("🔍 Inspecting Module Backend Connectivity")
    print("=" * 70)
    
    # Import Qt application
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    
    connectivity_status = {}
    
    # 1. Deposition Module
    print("\n📦 Inspecting Deposition Module...")
    try:
        from src.python.gui.enhanced_deposition_panel import EnhancedDepositionPanel
        from src.python.deposition_manager import DepositionManager
        
        panel = EnhancedDepositionPanel()
        
        # Check backend connectivity
        backend_info = {
            'panel_created': True,
            'manager_available': hasattr(panel, 'deposition_manager') and panel.deposition_manager is not None,
            'cython_backend': False,
            'python_fallback': True,
            'wafer_methods': hasattr(panel, 'set_wafer') and hasattr(panel, 'get_wafer'),
            'simulation_method': hasattr(panel, 'run_deposition_simulation')
        }
        
        # Check for Cython backend
        if panel.deposition_manager:
            backend_info['manager_type'] = type(panel.deposition_manager).__name__
            backend_info['simulation_available'] = hasattr(panel.deposition_manager, 'simulate_deposition')
        
        connectivity_status['deposition'] = backend_info
        print(f"  ✅ Panel: {backend_info['panel_created']}")
        print(f"  ✅ Manager: {backend_info['manager_available']}")
        print(f"  ✅ Wafer Methods: {backend_info['wafer_methods']}")
        print(f"  ✅ Simulation: {backend_info['simulation_method']}")
        
    except Exception as e:
        connectivity_status['deposition'] = {'error': str(e)}
        print(f"  ❌ Error: {e}")
    
    # 2. Lithography Module
    print("\n📸 Inspecting Lithography Module...")
    try:
        from src.python.gui.enhanced_lithography_panel import EnhancedLithographyPanel
        from src.python.lithography.enhanced_lithography_manager import EnhancedLithographyManager
        
        panel = EnhancedLithographyPanel()
        
        backend_info = {
            'panel_created': True,
            'manager_available': hasattr(panel, 'lithography_manager') and panel.lithography_manager is not None,
            'cython_backend': False,
            'python_fallback': True,
            'wafer_methods': hasattr(panel, 'set_wafer') and hasattr(panel, 'get_wafer'),
            'simulation_method': hasattr(panel, 'run_lithography_simulation')
        }
        
        connectivity_status['lithography'] = backend_info
        print(f"  ✅ Panel: {backend_info['panel_created']}")
        print(f"  ✅ Manager: {backend_info['manager_available']}")
        print(f"  ✅ Wafer Methods: {backend_info['wafer_methods']}")
        print(f"  ⚠️  Simulation: {backend_info['simulation_method']}")
        
    except Exception as e:
        connectivity_status['lithography'] = {'error': str(e)}
        print(f"  ❌ Error: {e}")
    
    # 3. Etching Module
    print("\n🔪 Inspecting Etching Module...")
    try:
        from src.python.gui.enhanced_etching_panel import EnhancedEtchingPanel
        from src.python.etching.enhanced_etching_manager import EnhancedEtchingManager
        
        panel = EnhancedEtchingPanel()
        
        backend_info = {
            'panel_created': True,
            'manager_available': hasattr(panel, 'etching_manager') and panel.etching_manager is not None,
            'cython_backend': False,
            'python_fallback': True,
            'wafer_methods': hasattr(panel, 'set_wafer') and hasattr(panel, 'get_wafer'),
            'simulation_method': hasattr(panel, 'run_etching_simulation')
        }
        
        connectivity_status['etching'] = backend_info
        print(f"  ✅ Panel: {backend_info['panel_created']}")
        print(f"  ✅ Manager: {backend_info['manager_available']}")
        print(f"  ✅ Wafer Methods: {backend_info['wafer_methods']}")
        print(f"  ⚠️  Simulation: {backend_info['simulation_method']}")
        
    except Exception as e:
        connectivity_status['etching'] = {'error': str(e)}
        print(f"  ❌ Error: {e}")
    
    # 4. Metallization Module
    print("\n🔗 Inspecting Metallization Module...")
    try:
        from src.python.gui.enhanced_metallization_panel import EnhancedMetallizationPanel
        
        panel = EnhancedMetallizationPanel()
        
        backend_info = {
            'panel_created': True,
            'manager_available': hasattr(panel, 'metallization_manager'),
            'wafer_methods': hasattr(panel, 'set_wafer') and hasattr(panel, 'get_wafer'),
            'simulation_method': hasattr(panel, 'run_metallization_simulation')
        }
        
        connectivity_status['metallization'] = backend_info
        print(f"  ✅ Panel: {backend_info['panel_created']}")
        print(f"  ⚠️  Manager: {backend_info['manager_available']}")
        print(f"  ⚠️  Wafer Methods: {backend_info['wafer_methods']}")
        print(f"  ⚠️  Simulation: {backend_info['simulation_method']}")
        
    except Exception as e:
        connectivity_status['metallization'] = {'error': str(e)}
        print(f"  ❌ Error: {e}")
    
    # 5. Thermal Module
    print("\n🌡️  Inspecting Thermal Module...")
    try:
        from src.python.gui.enhanced_thermal_panel import EnhancedThermalPanel
        
        panel = EnhancedThermalPanel()
        
        backend_info = {
            'panel_created': True,
            'manager_available': hasattr(panel, 'thermal_manager'),
            'wafer_methods': hasattr(panel, 'set_wafer') and hasattr(panel, 'get_wafer'),
            'simulation_method': hasattr(panel, 'run_thermal_simulation')
        }
        
        connectivity_status['thermal'] = backend_info
        print(f"  ✅ Panel: {backend_info['panel_created']}")
        print(f"  ⚠️  Manager: {backend_info['manager_available']}")
        print(f"  ⚠️  Wafer Methods: {backend_info['wafer_methods']}")
        print(f"  ⚠️  Simulation: {backend_info['simulation_method']}")
        
    except Exception as e:
        connectivity_status['thermal'] = {'error': str(e)}
        print(f"  ❌ Error: {e}")
    
    # 6. Packaging Module
    print("\n📋 Inspecting Packaging Module...")
    try:
        from src.python.gui.enhanced_packaging_panel import EnhancedPackagingPanel
        from src.python.semipro_packaging.packaging_manager import PackagingManager
        
        panel = EnhancedPackagingPanel()
        
        backend_info = {
            'panel_created': True,
            'manager_available': hasattr(panel, 'packaging_manager') and panel.packaging_manager is not None,
            'wafer_methods': hasattr(panel, 'set_wafer') and hasattr(panel, 'get_wafer'),
            'simulation_method': hasattr(panel, 'run_packaging_simulation')
        }
        
        connectivity_status['packaging'] = backend_info
        print(f"  ✅ Panel: {backend_info['panel_created']}")
        print(f"  ✅ Manager: {backend_info['manager_available']}")
        print(f"  ⚠️  Wafer Methods: {backend_info['wafer_methods']}")
        print(f"  ⚠️  Simulation: {backend_info['simulation_method']}")
        
    except Exception as e:
        connectivity_status['packaging'] = {'error': str(e)}
        print(f"  ❌ Error: {e}")
    
    # 7. Reliability Module
    print("\n🛡️  Inspecting Reliability Module...")
    try:
        from src.python.gui.enhanced_reliability_panel import EnhancedReliabilityPanel
        
        panel = EnhancedReliabilityPanel()
        
        backend_info = {
            'panel_created': True,
            'manager_available': hasattr(panel, 'reliability_manager'),
            'wafer_methods': hasattr(panel, 'set_wafer') and hasattr(panel, 'get_wafer'),
            'simulation_method': hasattr(panel, 'run_reliability_simulation')
        }
        
        connectivity_status['reliability'] = backend_info
        print(f"  ✅ Panel: {backend_info['panel_created']}")
        print(f"  ⚠️  Manager: {backend_info['manager_available']}")
        print(f"  ⚠️  Wafer Methods: {backend_info['wafer_methods']}")
        print(f"  ⚠️  Simulation: {backend_info['simulation_method']}")
        
    except Exception as e:
        connectivity_status['reliability'] = {'error': str(e)}
        print(f"  ❌ Error: {e}")
    
    return connectivity_status

def inspect_inter_module_connectivity():
    """Inspect inter-module wafer connectivity"""
    print("\n🔄 Inspecting Inter-Module Connectivity")
    print("=" * 70)
    
    try:
        from src.python.gui.enhanced_simulator_gui import EnhancedSimulatorGUI
        
        gui = EnhancedSimulatorGUI()
        
        inter_module_status = {
            'wafer_management': hasattr(gui, 'current_wafer'),
            'wafer_initialization': hasattr(gui, 'initialize_wafer_management'),
            'wafer_connectivity': hasattr(gui, 'setup_wafer_connectivity'),
            'wafer_propagation': hasattr(gui, 'propagate_wafer_to_modules'),
            'module_signal_connection': hasattr(gui, 'connect_panel_signals'),
            'simulation_completion_handler': hasattr(gui, 'on_module_simulation_completed')
        }
        
        print(f"  ✅ Wafer Management: {inter_module_status['wafer_management']}")
        print(f"  ✅ Wafer Initialization: {inter_module_status['wafer_initialization']}")
        print(f"  ✅ Wafer Connectivity: {inter_module_status['wafer_connectivity']}")
        print(f"  ✅ Wafer Propagation: {inter_module_status['wafer_propagation']}")
        print(f"  ✅ Signal Connection: {inter_module_status['module_signal_connection']}")
        print(f"  ✅ Completion Handler: {inter_module_status['simulation_completion_handler']}")
        
        return inter_module_status
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return {'error': str(e)}

def inspect_persistence_layer():
    """Inspect current persistence/database layer"""
    print("\n💾 Inspecting Current Persistence Layer")
    print("=" * 70)
    
    persistence_status = {
        'cpp_simulation_engine': False,
        'cpp_wafer_management': False,
        'python_workflow_manager': False,
        'sqlite_databases': [],
        'file_based_storage': [],
        'in_memory_only': True
    }
    
    # Check for C++ simulation engine with wafer management
    try:
        # This would be available if C++ backend is compiled
        print("  🔍 Checking C++ Simulation Engine...")
        print("  ⚠️  C++ backend not compiled - no persistent wafer management")
    except:
        pass
    
    # Check for Python workflow manager
    try:
        from src.python.workflow_manager import WorkflowManager
        print("  ✅ Python WorkflowManager available")
        persistence_status['python_workflow_manager'] = True
        print("  ⚠️  Uses in-memory storage - no persistence")
    except Exception as e:
        print(f"  ❌ WorkflowManager error: {e}")
    
    # Check for existing databases
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db') or file.endswith('.sqlite'):
                db_files.append(os.path.join(root, file))
    
    if db_files:
        print(f"  ✅ Found {len(db_files)} database files:")
        for db in db_files:
            print(f"    - {db}")
        persistence_status['sqlite_databases'] = db_files
        persistence_status['in_memory_only'] = False
    else:
        print("  ⚠️  No database files found")
    
    return persistence_status

if __name__ == "__main__":
    print("🔍 Module Connectivity Inspection Starting...")
    
    # 1. Inspect module backend connectivity
    module_status = inspect_module_connectivity()
    
    # 2. Inspect inter-module connectivity
    inter_module_status = inspect_inter_module_connectivity()
    
    # 3. Inspect persistence layer
    persistence_status = inspect_persistence_layer()
    
    # Generate comprehensive report
    print("\n" + "=" * 70)
    print("📊 COMPREHENSIVE CONNECTIVITY INSPECTION REPORT")
    print("=" * 70)
    
    # Module connectivity summary
    working_modules = sum(1 for status in module_status.values() if isinstance(status, dict) and status.get('panel_created', False))
    total_modules = len(module_status)
    
    print(f"\n🔗 Module Backend Connectivity: {working_modules}/{total_modules} modules working")
    
    for module, status in module_status.items():
        if isinstance(status, dict) and 'error' not in status:
            manager_ok = "✅" if status.get('manager_available') else "❌"
            wafer_ok = "✅" if status.get('wafer_methods') else "❌"
            sim_ok = "✅" if status.get('simulation_method') else "❌"
            print(f"  {module.title()}: Manager{manager_ok} Wafer{wafer_ok} Simulation{sim_ok}")
        else:
            print(f"  {module.title()}: ❌ Error")
    
    # Inter-module connectivity summary
    inter_working = sum(inter_module_status.values()) if isinstance(inter_module_status, dict) and 'error' not in inter_module_status else 0
    inter_total = len(inter_module_status) if isinstance(inter_module_status, dict) and 'error' not in inter_module_status else 0
    
    print(f"\n🔄 Inter-Module Connectivity: {inter_working}/{inter_total} features working")
    
    # Persistence layer summary
    print(f"\n💾 Persistence Layer: {'Database files found' if persistence_status['sqlite_databases'] else 'In-memory only'}")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print(f"  1. ✅ Module panels are working - good foundation")
    print(f"  2. ⚠️  Need to add wafer management methods to all modules")
    print(f"  3. ⚠️  Need to add simulation methods to all modules")
    print(f"  4. ✅ Inter-module connectivity framework is in place")
    print(f"  5. ❌ CRITICAL: Need persistent database layer for wafer tracking")
    
    print(f"\n🗄️  POSTGRESQL SCHEMA RECOMMENDATION: HIGHLY BENEFICIAL!")
    print(f"  - Track wafer state across all modules")
    print(f"  - Store simulation results and parameters")
    print(f"  - Enable process history and rollback")
    print(f"  - Support concurrent multi-wafer processing")
    print(f"  - Provide audit trail for industrial compliance")
    
    sys.exit(0)
