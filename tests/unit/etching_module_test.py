#!/usr/bin/env python3
"""
Comprehensive Etching Module Test
=================================

Test the etching module with various industrial plasma etching scenarios
and validate the GUI integration with real C++ backend.
"""

import sys
import os
import json
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_etching_scenarios():
    """Test various etching scenarios"""
    
    print("⚡ COMPREHENSIVE ETCHING MODULE TEST")
    print("=" * 50)
    
    # Industrial etching scenarios
    scenarios = {
        "silicon_rie": {
            "name": "Silicon RIE (Deep Trench)",
            "config": {
                "wafer": {
                    "name": "silicon_etch_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "etching",
                    "material": "Silicon",
                    "etch_depth": 5.0,  # 5 μm deep trench
                    "chemistry": "SF6/O2",
                    "pressure": 0.01,  # 10 mTorr
                    "power": 300.0,  # 300W
                    "temperature": 20.0
                }
            }
        },
        "oxide_etch": {
            "name": "Oxide Etch (Contact Opening)",
            "config": {
                "wafer": {
                    "name": "oxide_etch_wafer",
                    "diameter": 200.0,
                    "thickness": 0.525,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "etching",
                    "material": "SiO2",
                    "etch_depth": 0.5,  # 500nm oxide etch
                    "chemistry": "CHF3/CF4",
                    "pressure": 0.05,  # 50 mTorr
                    "power": 200.0,  # 200W
                    "temperature": 25.0
                }
            }
        },
        "metal_etch": {
            "name": "Metal Etch (Interconnect)",
            "config": {
                "wafer": {
                    "name": "metal_etch_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "etching",
                    "material": "Aluminum",
                    "etch_depth": 0.8,  # 800nm metal etch
                    "chemistry": "Cl2/BCl3",
                    "pressure": 0.02,  # 20 mTorr
                    "power": 250.0,  # 250W
                    "temperature": 30.0
                }
            }
        },
        "nitride_etch": {
            "name": "Nitride Etch (Spacer)",
            "config": {
                "wafer": {
                    "name": "nitride_etch_wafer",
                    "diameter": 200.0,
                    "thickness": 0.525,
                    "material": "Silicon",
                    "crystal_orientation": "111"
                },
                "process": {
                    "operation": "etching",
                    "material": "Si3N4",
                    "etch_depth": 0.1,  # 100nm nitride etch
                    "chemistry": "CF4/O2",
                    "pressure": 0.03,  # 30 mTorr
                    "power": 150.0,  # 150W
                    "temperature": 15.0
                }
            }
        }
    }
    
    success_count = 0
    total_scenarios = len(scenarios)
    results_summary = []
    
    for scenario_id, scenario in scenarios.items():
        print(f"\n🔬 Testing: {scenario['name']}")
        print("-" * 50)
        
        # Create config file
        config_file = f"config/etching_{scenario_id}.json"
        os.makedirs("config", exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(scenario['config'], f, indent=2)
        
        try:
            # Run C++ simulation
            simulator_path = project_root / "build" / "simulator"
            
            print(f"🚀 Running etching simulation...")
            print(f"   Material: {scenario['config']['process']['material']}")
            print(f"   Depth: {scenario['config']['process']['etch_depth']} μm")
            print(f"   Chemistry: {scenario['config']['process']['chemistry']}")
            print(f"   Power: {scenario['config']['process']['power']} W")
            
            start_time = time.time()
            result = subprocess.run([
                str(simulator_path),
                "--process", "etching",
                "--config", config_file
            ], capture_output=True, text=True, timeout=30)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"⏱️  Execution time: {execution_time:.3f} seconds")
            
            if result.returncode == 0:
                print("✅ Simulation: SUCCESS")
                
                # Parse results using GUI parser
                from gui.enhanced_main_window import SimulationWorker
                worker = SimulationWorker("etching", scenario['config'])
                parsed_results = worker.parse_simulation_output(result.stdout)
                
                print(f"📊 Results parsed: {len(parsed_results)} metrics")
                
                scenario_result = {
                    'name': scenario['name'],
                    'success': True,
                    'execution_time': execution_time,
                    'results': parsed_results
                }
                
                # Display key results
                for key, value in parsed_results.items():
                    if key == 'etch_depth':
                        target = scenario['config']['process']['etch_depth']
                        accuracy = (1 - abs(value - target) / target) * 100 if target > 0 else 100
                        print(f"   • Etch Depth: {value:.6f} μm (Target: {target} μm, Accuracy: {accuracy:.1f}%)")
                    elif key == 'etch_rate':
                        print(f"   • Etch Rate: {value:.3f} μm/min")
                    elif key == 'selectivity':
                        print(f"   • Selectivity: {value:.1f}:1")
                    elif key == 'anisotropy':
                        print(f"   • Anisotropy: {value:.3f}")
                    else:
                        print(f"   • {key.replace('_', ' ').title()}: {value}")
                
                success_count += 1
                
            else:
                print("❌ Simulation: FAILED")
                scenario_result = {
                    'name': scenario['name'],
                    'success': False,
                    'execution_time': execution_time,
                    'error': result.stderr.strip()
                }
                
            results_summary.append(scenario_result)
                
        except subprocess.TimeoutExpired:
            print("⏰ Simulation timed out")
        except Exception as e:
            print(f"💥 Exception: {e}")
    
    # Final summary
    print(f"\n🏆 ETCHING MODULE TEST RESULTS")
    print("=" * 50)
    print(f"✅ Successful scenarios: {success_count}/{total_scenarios}")
    print(f"📊 Success rate: {success_count/total_scenarios*100:.1f}%")
    
    if success_count == total_scenarios:
        print("🎉 ALL ETCHING SCENARIOS WORKING!")
        print("⚡ Industrial plasma etching processes validated:")
        for result in results_summary:
            if result['success']:
                print(f"   ✅ {result['name']} ({result['execution_time']:.3f}s)")
        
        print(f"\n🚀 GUI Integration Ready:")
        print("   • Launch: python launch_gui.py")
        print("   • Navigate to Etching tab")
        print("   • Test with industrial parameters")
        print("   • View real-time etching profile visualization")
        return True
    else:
        print("⚠️  Some scenarios failed - check implementation")
        return False

if __name__ == "__main__":
    success = test_etching_scenarios()
    sys.exit(0 if success else 1)
