#!/usr/bin/env python3
"""
Test Enhanced Oxidation Module
==============================

Test script to validate the enhanced oxidation module implementation
with industrial examples and comprehensive functionality.
"""

import sys
import os
import logging
import traceback

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_oxidation_backend():
    """Test oxidation backend initialization"""
    print("🔥 Testing Oxidation Backend Initialization...")
    
    try:
        from python.oxidation import OxidationManager, IndustrialOxidationProcessor
        
        # Test basic manager
        manager = OxidationManager()
        print("✅ OxidationManager initialized successfully")
        
        # Test industrial processor
        processor = IndustrialOxidationProcessor()
        print("✅ IndustrialOxidationProcessor initialized successfully")
        
        # Test available recipes
        recipes = processor.get_available_recipes()
        print(f"✅ Available industrial recipes: {len(recipes)}")
        for recipe in recipes:
            print(f"   - {recipe}")
            
        return True
        
    except ImportError as e:
        print(f"⚠️  Using fallback oxidation backend: {e}")
        try:
            from python.oxidation.fallback_oxidation import FallbackOxidationModel
            manager = FallbackOxidationModel()
            print("✅ Fallback oxidation model initialized successfully")
            return True
        except Exception as fallback_e:
            print(f"❌ Failed to initialize fallback backend: {fallback_e}")
            return False
    except Exception as e:
        print(f"❌ Error initializing oxidation backend: {e}")
        print(traceback.format_exc())
        return False

def test_basic_oxidation():
    """Test basic oxidation functionality"""
    print("\n🔥 Testing Basic Oxidation Functionality...")
    
    try:
        from python.oxidation import OxidationManager
        
        manager = OxidationManager()
        
        # Test basic oxidation
        result = manager.run_basic_oxidation(temperature=1000.0, time=2.0)
        print(f"✅ Basic oxidation result: {result}")
        
        # Test Deal-Grove calculation
        thickness = manager.calculate_deal_grove_thickness(1000.0, 2.0)
        print(f"✅ Deal-Grove thickness: {thickness:.2f} nm")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in basic oxidation test: {e}")
        print(traceback.format_exc())
        return False

def test_industrial_processes():
    """Test industrial oxidation processes"""
    print("\n🔥 Testing Industrial Oxidation Processes...")
    
    try:
        from python.oxidation import IndustrialOxidationProcessor
        
        processor = IndustrialOxidationProcessor()
        
        # Test different industrial recipes
        test_recipes = [
            'cmos_gate_oxide_180nm',
            'flash_tunnel_oxide',
            'sti_liner_oxide'
        ]
        
        for recipe_name in test_recipes:
            try:
                # Create dummy wafer
                class DummyWafer:
                    def __init__(self):
                        self.layers = []
                        
                wafer = DummyWafer()
                
                result = processor.run_industrial_recipe(recipe_name, wafer)
                print(f"✅ {recipe_name}: thickness = {result.get('final_thickness', 0):.2f} nm")
                
            except Exception as recipe_e:
                print(f"⚠️  Recipe {recipe_name} failed: {recipe_e}")
                
        return True
        
    except Exception as e:
        print(f"❌ Error in industrial processes test: {e}")
        print(traceback.format_exc())
        return False

def test_oxidation_analysis():
    """Test oxidation analysis functionality"""
    print("\n🔥 Testing Oxidation Analysis...")
    
    try:
        from python.oxidation import OxidationAnalyzer, OxidationVisualizer
        
        # Test analyzer
        analyzer = OxidationAnalyzer()
        
        # Create sample results
        sample_results = {
            'final_thickness': 50.0,
            'uniformity': 95.0,
            'interface_roughness': 0.2,
            'stress_level': 100.0,
            'electrical_quality': 1e11,
            'thickness_profile': [50.0, 49.5, 49.0, 48.5, 48.0]
        }
        
        # Test thickness uniformity analysis
        uniformity_analysis = analyzer.analyze_thickness_uniformity(sample_results['thickness_profile'])
        print(f"✅ Uniformity analysis: {uniformity_analysis}")
        
        # Test electrical quality analysis
        electrical_analysis = analyzer.analyze_electrical_quality(sample_results)
        print(f"✅ Electrical quality: {electrical_analysis['quality_grade']}")
        
        # Test stress analysis
        stress_analysis = analyzer.analyze_stress_effects(sample_results)
        print(f"✅ Stress category: {stress_analysis['stress_category']}")
        
        # Test visualizer
        visualizer = OxidationVisualizer()
        
        # Test thickness profile data creation
        profile_data = visualizer.create_thickness_profile_data(sample_results)
        print(f"✅ Thickness profile data created: {len(profile_data.get('thickness_values', []))} points")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in oxidation analysis test: {e}")
        print(traceback.format_exc())
        return False

def test_gui_components():
    """Test GUI components (without actually launching GUI)"""
    print("\n🔥 Testing GUI Components...")
    
    try:
        # Test oxidation panel import
        from python.gui.oxidation_panel import OxidationPanel
        print("✅ OxidationPanel class imported successfully")
        
        # Test log window import
        from python.gui.oxidation_log_window import OxidationLogWindow
        print("✅ OxidationLogWindow class imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI components: {e}")
        print(traceback.format_exc())
        return False

def test_fallback_functionality():
    """Test fallback oxidation functionality"""
    print("\n🔥 Testing Fallback Oxidation Functionality...")
    
    try:
        from python.oxidation.fallback_oxidation import (
            FallbackOxidationModel,
            FallbackIndustrialOxidationParams,
            FallbackIndustrialOxidationResults
        )
        
        # Test fallback model
        model = FallbackOxidationModel()
        print("✅ Fallback oxidation model initialized")
        
        # Test basic oxidation
        class DummyWafer:
            def __init__(self):
                self.layers = []
                
        wafer = DummyWafer()
        result = model.simulate_oxidation(wafer, 1000.0, 2.0)
        print(f"✅ Fallback basic oxidation: {result['thickness']:.2f} nm")
        
        # Test industrial processes
        params = FallbackIndustrialOxidationParams()
        params.process_type = 0  # GATE_OXIDE_FORMATION
        params.target_thickness = 5.0
        params.temperature = 1000.0
        
        industrial_result = model.simulate_industrial_oxidation(wafer, params)
        print(f"✅ Fallback industrial oxidation: {industrial_result.final_thickness:.2f} nm")
        
        # Test specific processes
        gate_result = model.simulate_gate_oxide_formation(wafer, 5.0, 1000.0)
        print(f"✅ Gate oxide formation: {gate_result.final_thickness:.2f} nm")
        
        tunnel_result = model.simulate_tunnel_oxide(wafer, 2.5, 850.0)
        print(f"✅ Tunnel oxide formation: {tunnel_result.final_thickness:.2f} nm")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in fallback functionality test: {e}")
        print(traceback.format_exc())
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🔥 SemiPRO Enhanced Oxidation Module Test Suite")
    print("=" * 60)
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    tests = [
        ("Oxidation Backend", test_oxidation_backend),
        ("Basic Oxidation", test_basic_oxidation),
        ("Industrial Processes", test_industrial_processes),
        ("Oxidation Analysis", test_oxidation_analysis),
        ("GUI Components", test_gui_components),
        ("Fallback Functionality", test_fallback_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🔥 Test Summary: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! Enhanced oxidation module is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
