#!/usr/bin/env python3
"""
Simple Enhanced Doping Integration Test
======================================

Quick test to verify enhanced doping integration works
"""

import sys
import os

# Add src/python to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_enhanced_doping_bridge():
    """Test enhanced doping bridge creation"""
    print("Testing enhanced doping bridge creation...")
    
    try:
        from enhanced_doping_bridge import create_enhanced_doping_bridge
        bridge = create_enhanced_doping_bridge()
        
        print(f"✓ Bridge created successfully")
        print(f"✓ Enhanced available: {bridge.enhanced_available}")
        print(f"✓ Fallback available: {bridge.fallback_available}")
        
        # Test database access
        species = bridge.get_ion_species()
        equipment = bridge.get_available_equipment()
        processes = bridge.get_available_processes()
        
        print(f"✓ Ion species: {len(species)} available")
        print(f"✓ Equipment: {len(equipment)} available")
        print(f"✓ Processes: {len(processes)} available")
        
        return True
        
    except Exception as e:
        print(f"✗ Bridge creation failed: {e}")
        return False

def test_simulator_integration():
    """Test simulator integration"""
    print("\nTesting simulator integration...")
    
    try:
        from simulator import Simulator
        from wafer import Wafer
        
        # Create simulator and wafer
        simulator = Simulator()
        wafer = Wafer("test_wafer", 200.0)
        simulator.wafer = wafer
        
        print("✓ Simulator and wafer created")
        
        # Test ion implantation
        result = simulator.run_ion_implantation(
            energy=40.0, dose=5e15, species="arsenic")
        
        if result:
            print("✓ Ion implantation simulation successful")
            print(f"  - Species: {result.get('species', 'N/A')}")
            print(f"  - Energy: {result.get('energy', 'N/A')} keV")
            print(f"  - Enhanced: {result.get('enhanced_simulation', False)}")
        else:
            print("⚠ Ion implantation returned None")
        
        return True
        
    except Exception as e:
        print(f"✗ Simulator integration failed: {e}")
        return False

def test_pipeline_integration():
    """Test pipeline integration"""
    print("\nTesting pipeline integration...")
    
    try:
        from doping_pipeline_integration import create_enhanced_doping_pipeline
        pipeline = create_enhanced_doping_pipeline()
        
        print("✓ Pipeline created successfully")
        
        # Get available workflows
        workflows = pipeline.get_available_workflows()
        print(f"✓ Available workflows: {workflows}")
        
        # Get workflow details
        if workflows:
            details = pipeline.get_workflow_details(workflows[0])
            print(f"✓ Workflow '{workflows[0]}' has {details['steps']} steps")
        
        return True
        
    except Exception as e:
        print(f"✗ Pipeline integration failed: {e}")
        return False

def test_gui_integration():
    """Test GUI integration"""
    print("\nTesting GUI integration...")
    
    try:
        # Test import without creating GUI
        from gui.doping_panel import DopingPanel
        print("✓ DopingPanel import successful")
        
        # Test enhanced doping availability flag
        import launch_enhanced_semipro
        if hasattr(launch_enhanced_semipro, 'ENHANCED_DOPING_AVAILABLE'):
            print(f"✓ ENHANCED_DOPING_AVAILABLE flag exists")
        else:
            print("⚠ ENHANCED_DOPING_AVAILABLE flag not found")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("ENHANCED DOPING INTEGRATION - SIMPLE TEST")
    print("="*60)
    
    tests = [
        ("Enhanced Doping Bridge", test_enhanced_doping_bridge),
        ("Simulator Integration", test_simulator_integration),
        ("Pipeline Integration", test_pipeline_integration),
        ("GUI Integration", test_gui_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status:8} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced doping integration is working.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
