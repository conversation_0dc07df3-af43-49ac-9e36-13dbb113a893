#!/usr/bin/env python3
"""
Comprehensive Doping Module Test
================================

Test the doping module with various industrial ion implantation scenarios
and validate the GUI integration with real C++ backend.
"""

import sys
import os
import json
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_doping_scenarios():
    """Test various doping scenarios"""
    
    print("⚛️  COMPREHENSIVE DOPING MODULE TEST")
    print("=" * 50)
    
    # Industrial doping scenarios
    scenarios = {
        "nmos_source_drain": {
            "name": "NMOS Source/Drain Implant",
            "config": {
                "wafer": {
                    "name": "nmos_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "doping",
                    "dopant_type": "phosphorus",
                    "concentration": 5e15,  # 5e15 cm^-2
                    "energy": 80.0,  # 80 keV
                    "temperature": 1000.0,
                    "time": 0.5
                }
            }
        },
        "pmos_source_drain": {
            "name": "PMOS Source/Drain Implant",
            "config": {
                "wafer": {
                    "name": "pmos_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "doping",
                    "dopant_type": "boron",
                    "concentration": 3e15,  # 3e15 cm^-2
                    "energy": 25.0,  # 25 keV
                    "temperature": 950.0,
                    "time": 0.3
                }
            }
        },
        "well_implant": {
            "name": "N-Well Formation",
            "config": {
                "wafer": {
                    "name": "well_wafer",
                    "diameter": 200.0,
                    "thickness": 0.525,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "doping",
                    "dopant_type": "phosphorus",
                    "concentration": 1e13,  # 1e13 cm^-2 (deep well)
                    "energy": 180.0,  # 180 keV (deep implant)
                    "temperature": 1100.0,
                    "time": 2.0
                }
            }
        },
        "threshold_adjust": {
            "name": "Threshold Voltage Adjust",
            "config": {
                "wafer": {
                    "name": "vt_adjust_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "doping",
                    "dopant_type": "boron",
                    "concentration": 1e12,  # 1e12 cm^-2 (light dose)
                    "energy": 15.0,  # 15 keV (shallow implant)
                    "temperature": 800.0,
                    "time": 0.1
                }
            }
        }
    }
    
    success_count = 0
    total_scenarios = len(scenarios)
    results_summary = []
    
    for scenario_id, scenario in scenarios.items():
        print(f"\n🔬 Testing: {scenario['name']}")
        print("-" * 50)
        
        # Create config file
        config_file = f"config/doping_{scenario_id}.json"
        os.makedirs("config", exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(scenario['config'], f, indent=2)
        
        try:
            # Run C++ simulation
            simulator_path = project_root / "build" / "simulator"
            
            print(f"🚀 Running doping simulation...")
            print(f"   Dopant: {scenario['config']['process']['dopant_type']}")
            print(f"   Dose: {scenario['config']['process']['concentration']:.1e} cm⁻²")
            print(f"   Energy: {scenario['config']['process']['energy']} keV")
            print(f"   Temperature: {scenario['config']['process']['temperature']} °C")
            
            start_time = time.time()
            result = subprocess.run([
                str(simulator_path),
                "--process", "doping",
                "--config", config_file
            ], capture_output=True, text=True, timeout=30)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"⏱️  Execution time: {execution_time:.3f} seconds")
            
            if result.returncode == 0:
                print("✅ Simulation: SUCCESS")
                
                # Parse results using GUI parser
                from gui.enhanced_main_window import SimulationWorker
                worker = SimulationWorker("doping", scenario['config'])
                parsed_results = worker.parse_simulation_output(result.stdout)
                
                print(f"📊 Results parsed: {len(parsed_results)} metrics")
                
                scenario_result = {
                    'name': scenario['name'],
                    'success': True,
                    'execution_time': execution_time,
                    'results': parsed_results
                }
                
                # Display key results
                for key, value in parsed_results.items():
                    if key == 'implant_range':
                        print(f"   • Implant Range: {value:.3e} μm")
                    elif key == 'peak_concentration':
                        print(f"   • Peak Concentration: {value:.2e} cm⁻³")
                    elif key == 'dopant_type':
                        print(f"   • Dopant Type: {value}")
                    else:
                        print(f"   • {key.replace('_', ' ').title()}: {value}")
                
                success_count += 1
                
            else:
                print("❌ Simulation: FAILED")
                scenario_result = {
                    'name': scenario['name'],
                    'success': False,
                    'execution_time': execution_time,
                    'error': result.stderr.strip()
                }
                
            results_summary.append(scenario_result)
                
        except subprocess.TimeoutExpired:
            print("⏰ Simulation timed out")
        except Exception as e:
            print(f"💥 Exception: {e}")
    
    # Final summary
    print(f"\n🏆 DOPING MODULE TEST RESULTS")
    print("=" * 50)
    print(f"✅ Successful scenarios: {success_count}/{total_scenarios}")
    print(f"📊 Success rate: {success_count/total_scenarios*100:.1f}%")
    
    if success_count == total_scenarios:
        print("🎉 ALL DOPING SCENARIOS WORKING!")
        print("⚛️  Industrial ion implantation processes validated:")
        for result in results_summary:
            if result['success']:
                print(f"   ✅ {result['name']} ({result['execution_time']:.3f}s)")
        
        print(f"\n🚀 GUI Integration Ready:")
        print("   • Launch: python launch_gui.py")
        print("   • Navigate to Doping tab")
        print("   • Test with industrial parameters")
        print("   • View real-time dopant profile visualization")
        return True
    else:
        print("⚠️  Some scenarios failed - check implementation")
        return False

if __name__ == "__main__":
    success = test_doping_scenarios()
    sys.exit(0 if success else 1)
