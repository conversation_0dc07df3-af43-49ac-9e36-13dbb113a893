#!/usr/bin/env python3
"""
Test realistic oxidation parameters to debug the physics
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_oxidation_parameters():
    """Test different oxidation parameters to find realistic ones"""
    
    print("🔬 Testing Realistic Oxidation Parameters")
    print("=" * 50)
    
    # Test cases with known realistic results
    test_cases = [
        {
            "name": "Thin Gate Oxide (5nm)",
            "config": {
                "wafer": {
                    "name": "test_wafer",
                    "diameter": 200.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 900.0,    # Lower temp
                    "time": 0.1,            # 6 minutes
                    "atmosphere": "dry",     # Dry oxidation
                    "target_thickness": 0.005  # 5nm target
                }
            },
            "expected_range": (0.003, 0.008)  # 3-8nm expected
        },
        {
            "name": "Medium Oxide (50nm)",
            "config": {
                "wafer": {
                    "name": "test_wafer",
                    "diameter": 200.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 1000.0,   # Standard temp
                    "time": 0.5,            # 30 minutes
                    "atmosphere": "dry",     # Dry oxidation
                    "target_thickness": 0.05  # 50nm target
                }
            },
            "expected_range": (0.03, 0.08)  # 30-80nm expected
        },
        {
            "name": "Thick Field Oxide (200nm)",
            "config": {
                "wafer": {
                    "name": "test_wafer",
                    "diameter": 200.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 1100.0,   # High temp
                    "time": 1.0,            # 1 hour
                    "atmosphere": "wet",     # Wet oxidation for thick
                    "target_thickness": 0.2  # 200nm target
                }
            },
            "expected_range": (0.15, 0.3)  # 150-300nm expected
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print("-" * 40)
        
        # Create config file
        config_file = f"config/test_oxidation_{i}.json"
        os.makedirs("config", exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(test_case['config'], f, indent=2)
        
        print(f"📋 Config: {config_file}")
        print(f"🌡️  Temperature: {test_case['config']['process']['temperature']}°C")
        print(f"⏰ Time: {test_case['config']['process']['time']} hours")
        print(f"💨 Atmosphere: {test_case['config']['process']['atmosphere']}")
        
        try:
            # Run simulation
            simulator_path = project_root / "build" / "simulator"
            if not simulator_path.exists():
                print(f"❌ C++ simulator not found at {simulator_path}")
                continue
                
            start_time = time.time()
            result = subprocess.run([
                str(simulator_path),
                "--process", "oxidation",
                "--config", config_file
            ], capture_output=True, text=True, timeout=30)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            if result.returncode == 0:
                print("✅ SIMULATION SUCCESS")
                print("📊 Output:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        print(f"   {line}")
                        
                        # Try to extract thickness from output
                        if "thickness" in line.lower() or "μm" in line:
                            print(f"   🎯 Key result: {line}")
                            
            else:
                print("❌ SIMULATION FAILED")
                print("🚨 Error output:")
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"   {line}")
                        
                        # Look for specific error messages
                        if "out of physical range" in line:
                            print(f"   ⚠️  Physics range error: {line}")
                            
        except subprocess.TimeoutExpired:
            print("⏰ Simulation timed out (30s limit)")
        except Exception as e:
            print(f"💥 Exception: {e}")
    
    print(f"\n📊 OXIDATION PARAMETER TESTING COMPLETED")
    print("=" * 50)

if __name__ == "__main__":
    test_oxidation_parameters()
