#!/usr/bin/env python3
"""
Test Database Connection
Real PostgreSQL database connectivity test
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """Test real PostgreSQL database connection"""
    print("Testing PostgreSQL Database Connection")
    print("=" * 50)
    
    try:
        from src.python.database_manager import DatabaseManager
        
        # Test connection
        db = DatabaseManager()
        print("Database connection: SUCCESS")
        
        # Test wafer creation
        import time
        wafer_name = f"TEST_WAFER_{int(time.time())}"
        wafer_id = db.create_wafer(
            wafer_name=wafer_name,
            diameter_mm=200.0,
            thickness_um=775.0,
            material="silicon",
            batch_id="BATCH_001",
            operator_id="test_operator"
        )
        print(f"Wafer created: {wafer_id}")
        
        # Test wafer retrieval
        wafer_data = db.get_wafer(wafer_id)
        print(f"Wafer retrieved: {wafer_data['wafer_name']}")
        
        # Test layer addition
        layer_id = db.add_wafer_layer(
            wafer_id=wafer_id,
            material="SiO2",
            thickness_nm=100.0,
            created_by_module="deposition",
            deposition_technique="CVD",
            temperature_c=350.0,
            pressure_torr=1.0,
            time_seconds=60.0
        )
        print(f"Layer added: {layer_id}")
        
        # Test process step
        step_id = db.create_process_step(
            wafer_id=wafer_id,
            module_id="deposition",
            step_name="SiO2 Deposition",
            step_type="deposition",
            input_parameters={
                "technique": "CVD",
                "material": "SiO2",
                "thickness": 100.0,
                "temperature": 350.0
            }
        )
        print(f"Process step created: {step_id}")
        
        # Test step execution
        db.start_process_step(step_id)
        print("Process step started")
        
        db.complete_process_step(
            step_id=step_id,
            output_results={
                "final_thickness": 98.5,
                "uniformity": 95.2,
                "quality_score": 92.1
            },
            success_rate=95.2,
            quality_metrics={
                "uniformity": 95.2,
                "stress": -45.2,
                "surface_roughness": 1.2
            }
        )
        print("Process step completed")
        
        # Test wafer transfer
        transfer_id = db.record_wafer_transfer(
            wafer_id=wafer_id,
            from_module="deposition",
            to_module="lithography",
            transfer_type="simulation_complete",
            wafer_state_before={"layers": 1, "thickness": 775.0},
            wafer_state_after={"layers": 2, "thickness": 875.0}
        )
        print(f"Wafer transfer recorded: {transfer_id}")
        
        db.complete_wafer_transfer(transfer_id)
        print("Wafer transfer completed")
        
        # Test data retrieval
        layers = db.get_wafer_layers(wafer_id)
        print(f"Wafer layers: {len(layers)}")
        
        history = db.get_wafer_process_history(wafer_id)
        print(f"Process history: {len(history)} steps")
        
        transfers = db.get_wafer_transfers(wafer_id)
        print(f"Wafer transfers: {len(transfers)}")
        
        # Test statistics
        stats = db.get_module_statistics()
        print(f"Module statistics: {len(stats)} modules")
        
        overview = db.get_wafer_status_overview()
        print(f"Wafer overview: {len(overview)} wafers")
        
        # Update wafer status
        db.update_wafer_status(wafer_id, "in_lithography", "lithography")
        print("Wafer status updated")
        
        # Close connection
        db.close()
        print("Database connection closed")
        
        print("\nDatabase Test: SUCCESS")
        print("All database operations working correctly")
        return True
        
    except Exception as e:
        print(f"Database Test: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database_connection()
    sys.exit(0 if success else 1)
