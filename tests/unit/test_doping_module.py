#!/usr/bin/env python3
"""
Test script for the enhanced doping module
==========================================

Tests the doping module functionality including:
- Python doping manager
- Industrial processes
- Database integration
- GUI components
"""

import sys
import os
import logging
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_doping_manager():
    """Test the basic doping manager functionality"""
    print("\n🧪 Testing Doping Manager...")
    
    try:
        from doping.doping_manager import (
            DopingManager, ImplantationParameters, AnnealingParameters,
            IonSpecies, ChannelingDirection, DamageModel
        )
        
        # Create doping manager
        manager = DopingManager()
        print("✅ DopingManager created successfully")
        
        # Create test parameters
        implant_params = ImplantationParameters(
            species=IonSpecies.ARSENIC,
            energy=40.0,  # keV
            dose=5e15,    # cm⁻²
            beam_current=10.0,  # mA
            tilt_angle=7.0,
            twist_angle=22.0,
            temperature=25.0,
            channeling=ChannelingDirection.RANDOM,
            damage_model=DamageModel.KINCHIN_PEASE,
            enable_sputtering=False,
            beam_divergence=0.5
        )
        print("✅ ImplantationParameters created successfully")
        
        # Create mock wafer
        class MockWafer:
            def __init__(self):
                self.wafer_id = "test_wafer_001"
                self.diameter = 300  # mm
                self.thickness = 775  # μm
        
        wafer = MockWafer()
        
        # Test implantation simulation
        results = manager.simulate_implantation(wafer, implant_params)
        print(f"✅ Implantation simulation completed")
        print(f"   Projected Range: {results.projected_range:.2f} μm")
        print(f"   Peak Concentration: {results.peak_concentration:.2e} cm⁻³")
        print(f"   Sheet Resistance: {results.sheet_resistance:.1f} Ω/sq")
        print(f"   Junction Depth: {results.junction_depth:.3f} μm")
        
        # Test annealing parameters
        anneal_params = AnnealingParameters(
            temperature=1000.0,  # °C
            time=10.0,          # seconds
            ramp_rate=100.0,    # °C/s
            atmosphere="N2"
        )
        
        # Test annealing simulation
        anneal_results = manager.simulate_annealing(wafer, anneal_params, results)
        print(f"✅ Annealing simulation completed")
        print(f"   Final Sheet Resistance: {anneal_results.sheet_resistance:.1f} Ω/sq")
        print(f"   Final Junction Depth: {anneal_results.junction_depth:.3f} μm")
        
        return True
        
    except Exception as e:
        print(f"❌ Doping Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_industrial_processes():
    """Test industrial processes functionality"""
    print("\n🏭 Testing Industrial Processes...")
    
    try:
        from doping.doping_manager import DopingManager
        from doping.industrial_processes import IndustrialDopingProcesses, DeviceType, ProcessNode
        
        # Create managers
        doping_manager = DopingManager()
        industrial = IndustrialDopingProcesses(doping_manager)
        print("✅ Industrial processes manager created")
        
        # List available processes
        processes = industrial.list_all_processes()
        print(f"✅ Found {len(processes)} industrial processes:")
        for process in processes[:5]:  # Show first 5
            print(f"   - {process}")
        
        # Get a specific process
        if processes:
            process_name = processes[0]
            process = industrial.get_process(process_name)
            if process:
                print(f"✅ Retrieved process: {process.name}")
                print(f"   Device Type: {process.device_type.value}")
                print(f"   Process Node: {process.process_node.value}")
                print(f"   Implantation Steps: {len(process.implantation_steps)}")
                print(f"   Annealing Steps: {len(process.annealing_steps)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Industrial processes test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_integration():
    """Test database integration (without actual database)"""
    print("\n🗄️ Testing Database Integration...")
    
    try:
        from doping.database_integration import DopingDatabaseManager
        
        # Test database manager creation (will fail to connect but should not crash)
        try:
            db_manager = DopingDatabaseManager()
            print("⚠️  Database connection attempted (expected to fail without PostgreSQL)")
        except Exception as e:
            print(f"⚠️  Database connection failed as expected: {type(e).__name__}")
        
        print("✅ Database integration module loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_components():
    """Test GUI components (import only)"""
    print("\n🖥️ Testing GUI Components...")
    
    try:
        # Test if GUI components can be imported
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python', 'gui'))
        
        # Import without creating widgets (to avoid Qt dependency issues)
        import doping_panel
        print("✅ Doping panel module imported successfully")
        
        # Check if key classes exist
        if hasattr(doping_panel, 'DopingPanel'):
            print("✅ DopingPanel class found")
        if hasattr(doping_panel, 'DopingVisualizationWidget'):
            print("✅ DopingVisualizationWidget class found")
        if hasattr(doping_panel, 'DopingSimulationThread'):
            print("✅ DopingSimulationThread class found")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI components test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_data():
    """Test visualization data generation"""
    print("\n📊 Testing Visualization Data Generation...")
    
    try:
        from doping.doping_manager import DopingManager, ImplantationParameters, IonSpecies
        
        manager = DopingManager()
        
        # Create test parameters
        params = ImplantationParameters(
            species=IonSpecies.BORON,
            energy=25.0,
            dose=2e12,
            beam_current=2.0,
            tilt_angle=7.0,
            temperature=25.0
        )
        
        class MockWafer:
            def __init__(self):
                self.wafer_id = "viz_test_wafer"
        
        wafer = MockWafer()
        results = manager.simulate_implantation(wafer, params)
        
        # Test data for visualization
        print(f"✅ Generated concentration profile with {len(results.concentration_profile)} points")
        print(f"   Profile range: {min(results.concentration_profile):.2e} to {max(results.concentration_profile):.2e} cm⁻³")
        
        # Generate depth array for plotting
        depths = np.linspace(0, 500, len(results.concentration_profile))
        print(f"✅ Generated depth array: 0 to {depths[-1]:.1f} nm")
        
        # Test 3D data generation
        x = np.linspace(-200, 200, 20)
        y = np.linspace(-200, 200, 20)
        z = np.linspace(0, 500, 30)
        X, Y, Z = np.meshgrid(x, y, z)
        
        # Generate 3D concentration
        rp = results.projected_range * 1000  # Convert to nm
        concentration_3d = results.peak_concentration * np.exp(
            -0.5 * ((Z - rp) / 50) ** 2 -
            0.5 * (X ** 2 + Y ** 2) / (30 ** 2)
        )
        
        print(f"✅ Generated 3D concentration data: {concentration_3d.shape}")
        print(f"   3D range: {concentration_3d.min():.2e} to {concentration_3d.max():.2e} cm⁻³")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualization data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Enhanced Doping Module Test Suite")
    print("=" * 50)
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    tests = [
        ("Doping Manager", test_doping_manager),
        ("Industrial Processes", test_industrial_processes),
        ("Database Integration", test_database_integration),
        ("GUI Components", test_gui_components),
        ("Visualization Data", test_visualization_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Doping module is ready for integration.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
