#!/usr/bin/env python3
"""
Test Industrial Semiconductor Device Examples
============================================

Comprehensive test script for the 7 industrial device examples.

Author: Dr. <PERSON><PERSON>
"""

import sys
import os
import logging
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('industrial_examples_test.log')
        ]
    )

def test_geometry_modules():
    """Test if geometry modules are available"""
    print("🔍 Testing geometry module availability...")
    
    try:
        from src.python.geometry import GeometryManager
        print("✅ GeometryManager imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import GeometryManager: {e}")
        return False

def test_cython_bindings():
    """Test if Cython bindings are working"""
    print("🔍 Testing Cython bindings...")
    
    try:
        from src.cython.geometry import PyGeometryManager
        print("✅ Cython bindings available")
        return True
    except ImportError as e:
        print(f"⚠️  Cython bindings not available: {e}")
        print("   Falling back to Python implementation")
        return False

def test_industrial_examples():
    """Test industrial device examples"""
    print("\n🏭 Testing Industrial Device Examples")
    print("=" * 50)
    
    try:
        from src.python.examples import IndustrialDeviceExamples
        
        # Create examples instance
        examples = IndustrialDeviceExamples(output_dir="test_output")
        
        # Test individual examples
        test_results = {}
        
        print("\n📱 Testing MOSFET Example...")
        try:
            result = examples.example_1_mosfet_transistor()
            test_results["MOSFET"] = "✅ PASS" if "error" not in result else f"❌ FAIL: {result['error']}"
            print(f"   Result: {test_results['MOSFET']}")
        except Exception as e:
            test_results["MOSFET"] = f"❌ FAIL: {str(e)}"
            print(f"   Result: {test_results['MOSFET']}")
        
        print("\n🔬 Testing FinFET Example...")
        try:
            result = examples.example_2_finfet_3d_transistor()
            test_results["FinFET"] = "✅ PASS" if "error" not in result else f"❌ FAIL: {result['error']}"
            print(f"   Result: {test_results['FinFET']}")
        except Exception as e:
            test_results["FinFET"] = f"❌ FAIL: {str(e)}"
            print(f"   Result: {test_results['FinFET']}")
        
        print("\n📱 Testing MEMS Example...")
        try:
            result = examples.example_3_mems_accelerometer()
            test_results["MEMS"] = "✅ PASS" if "error" not in result else f"❌ FAIL: {result['error']}"
            print(f"   Result: {test_results['MEMS']}")
        except Exception as e:
            test_results["MEMS"] = f"❌ FAIL: {str(e)}"
            print(f"   Result: {test_results['MEMS']}")
        
        print("\n💾 Testing Memory Example...")
        try:
            result = examples.example_4_memory_cell_array()
            test_results["Memory"] = "✅ PASS" if "error" not in result else f"❌ FAIL: {result['error']}"
            print(f"   Result: {test_results['Memory']}")
        except Exception as e:
            test_results["Memory"] = f"❌ FAIL: {str(e)}"
            print(f"   Result: {test_results['Memory']}")
        
        print("\n⚡ Testing Power Device Example...")
        try:
            result = examples.example_5_power_igbt()
            test_results["Power"] = "✅ PASS" if "error" not in result else f"❌ FAIL: {result['error']}"
            print(f"   Result: {test_results['Power']}")
        except Exception as e:
            test_results["Power"] = f"❌ FAIL: {str(e)}"
            print(f"   Result: {test_results['Power']}")
        
        print("\n📡 Testing RF Device Example...")
        try:
            result = examples.example_6_rf_amplifier()
            test_results["RF"] = "✅ PASS" if "error" not in result else f"❌ FAIL: {result['error']}"
            print(f"   Result: {test_results['RF']}")
        except Exception as e:
            test_results["RF"] = f"❌ FAIL: {str(e)}"
            print(f"   Result: {test_results['RF']}")
        
        print("\n📷 Testing Image Sensor Example...")
        try:
            result = examples.example_7_image_sensor()
            test_results["Sensor"] = "✅ PASS" if "error" not in result else f"❌ FAIL: {result['error']}"
            print(f"   Result: {test_results['Sensor']}")
        except Exception as e:
            test_results["Sensor"] = f"❌ FAIL: {str(e)}"
            print(f"   Result: {test_results['Sensor']}")
        
        # Summary
        print("\n📊 Test Summary")
        print("-" * 30)
        passed = sum(1 for result in test_results.values() if "✅ PASS" in result)
        total = len(test_results)
        
        for device, result in test_results.items():
            print(f"{device:10}: {result}")
        
        print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        return test_results
        
    except ImportError as e:
        print(f"❌ Failed to import IndustrialDeviceExamples: {e}")
        return {}

def test_gui_integration():
    """Test GUI integration"""
    print("\n🖥️  Testing GUI Integration")
    print("=" * 30)
    
    try:
        from src.python.gui.geometry_panel import GeometryPanel
        print("✅ GeometryPanel imported successfully")
        
        # Test if PySide6 is available
        try:
            from PySide6.QtWidgets import QApplication
            print("✅ PySide6 available for GUI")
            return True
        except ImportError:
            print("⚠️  PySide6 not available - GUI tests skipped")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import GUI components: {e}")
        return False

def run_full_example_suite():
    """Run the full example suite"""
    print("\n🚀 Running Full Industrial Example Suite")
    print("=" * 50)
    
    try:
        from src.python.examples import run_industrial_examples
        
        start_time = time.time()
        results = run_industrial_examples()
        end_time = time.time()
        
        print(f"\n⏱️  Total execution time: {end_time - start_time:.2f} seconds")
        
        return results
        
    except Exception as e:
        print(f"❌ Failed to run full example suite: {e}")
        return {}

def generate_test_report(test_results, gui_available, full_suite_results):
    """Generate comprehensive test report"""
    report_path = Path("industrial_examples_test_report.md")
    
    with open(report_path, 'w') as f:
        f.write("# Industrial Examples Test Report\n\n")
        f.write(f"**Test Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Test Environment\n\n")
        f.write(f"- Python Version: {sys.version}\n")
        f.write(f"- Platform: {sys.platform}\n")
        f.write(f"- Working Directory: {os.getcwd()}\n\n")
        
        f.write("## Module Availability\n\n")
        f.write("| Module | Status |\n")
        f.write("|--------|--------|\n")
        f.write(f"| Geometry Manager | {'✅ Available' if test_geometry_modules() else '❌ Not Available'} |\n")
        f.write(f"| Cython Bindings | {'✅ Available' if test_cython_bindings() else '⚠️ Not Available'} |\n")
        f.write(f"| GUI Components | {'✅ Available' if gui_available else '❌ Not Available'} |\n\n")
        
        f.write("## Individual Example Tests\n\n")
        f.write("| Example | Result | Details |\n")
        f.write("|---------|--------|----------|\n")
        
        for device, result in test_results.items():
            status = "✅ PASS" if "✅ PASS" in result else "❌ FAIL"
            details = result.replace("✅ PASS", "").replace("❌ FAIL: ", "").strip()
            f.write(f"| {device} | {status} | {details} |\n")
        
        f.write("\n## Full Suite Results\n\n")
        if full_suite_results:
            f.write("Full example suite executed successfully.\n\n")
            for device, result in full_suite_results.items():
                if "error" in result:
                    f.write(f"- **{device}**: ❌ Failed - {result['error']}\n")
                else:
                    f.write(f"- **{device}**: ✅ Success - {result.get('device_type', 'Unknown')}\n")
        else:
            f.write("Full example suite was not executed.\n")
        
        f.write("\n## Recommendations\n\n")
        
        failed_tests = [device for device, result in test_results.items() if "❌ FAIL" in result]
        if failed_tests:
            f.write("### Issues Found\n\n")
            for device in failed_tests:
                f.write(f"- **{device}**: Review implementation and dependencies\n")
        
        if not gui_available:
            f.write("- **GUI**: Install PySide6 for GUI functionality\n")
        
        f.write("\n### Next Steps\n\n")
        f.write("1. Address any failed tests\n")
        f.write("2. Ensure all dependencies are installed\n")
        f.write("3. Run integration tests with simulation pipeline\n")
        f.write("4. Validate visualization outputs\n")
    
    print(f"\n📄 Test report generated: {report_path}")

def main():
    """Main test function"""
    print("🧪 SemiPRO Industrial Examples Test Suite")
    print("=" * 50)
    
    setup_logging()
    
    # Test module availability
    geometry_available = test_geometry_modules()
    cython_available = test_cython_bindings()
    
    # Test individual examples
    test_results = test_industrial_examples()
    
    # Test GUI integration
    gui_available = test_gui_integration()
    
    # Run full suite if modules are available
    full_suite_results = {}
    if geometry_available:
        full_suite_results = run_full_example_suite()
    
    # Generate test report
    generate_test_report(test_results, gui_available, full_suite_results)
    
    # Final summary
    print("\n🎯 Test Suite Complete")
    print("=" * 25)
    
    if test_results:
        passed = sum(1 for result in test_results.values() if "✅ PASS" in result)
        total = len(test_results)
        print(f"Individual Tests: {passed}/{total} passed")
    
    if full_suite_results:
        suite_passed = sum(1 for result in full_suite_results.values() if "error" not in result)
        suite_total = len(full_suite_results)
        print(f"Full Suite: {suite_passed}/{suite_total} examples successful")
    
    print(f"GUI Available: {'Yes' if gui_available else 'No'}")
    print(f"Geometry Modules: {'Available' if geometry_available else 'Not Available'}")
    
    return test_results, full_suite_results

if __name__ == "__main__":
    main()
