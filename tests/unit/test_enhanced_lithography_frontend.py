#!/usr/bin/env python3
"""
Test script for Enhanced Lithography Frontend
Tests stepper/scanner modeling and process optimization features
"""

import sys
import os
sys.path.append('src/python')

from lithography.enhanced_lithography_manager import (
    EnhancedLithographyManager,
    ProcessOptimizationConfig,
    StepperScannerModel,
    LithographyEquipmentDatabase
)
import numpy as np

def test_stepper_scanner_modeling():
    """Test stepper/scanner equipment modeling"""
    print("=== Testing Stepper/Scanner Modeling ===")
    
    manager = EnhancedLithographyManager()
    
    # Test equipment database creation
    equipment_db = manager.create_stepper_scanner_database()
    print(f"Equipment database created with {len(equipment_db.steppers)} steppers/scanners")
    
    # Test individual equipment lookup
    asml_equipment = equipment_db.get_equipment("ASML_TWINSCAN_NXT_1980Di")
    if asml_equipment:
        print(f"Found equipment: {asml_equipment.system_name}")
        print(f"  Manufacturer: {asml_equipment.manufacturer}")
        print(f"  Wavelength: {asml_equipment.wavelength} nm")
        print(f"  NA: {asml_equipment.numerical_aperture}")
        print(f"  Resolution capability: {asml_equipment.resolution_capability} nm")
        print(f"  Throughput: {asml_equipment.throughput} WPH")
    
    # Test stepper/scanner simulation
    process_conditions = {
        'k1_factor': 0.25,
        'k2_factor': 0.5,
        'exposure_time': 0.08,
        'move_time': 0.04,
        'illumination_mode': 'Quadrupole'
    }
    
    wafer_specs = {
        'diameter': 300  # mm
    }
    
    try:
        simulation_results = manager.simulate_stepper_scanner(
            "ASML_TWINSCAN_NXT_1980Di", 
            process_conditions, 
            wafer_specs
        )
        
        print(f"\nStepper simulation results:")
        print(f"  Calculated resolution: {simulation_results['calculated_resolution']:.1f} nm")
        print(f"  Depth of focus: {simulation_results['depth_of_focus']:.1f} nm")
        print(f"  Calculated throughput: {simulation_results['calculated_throughput']:.1f} WPH")
        print(f"  Fields per wafer: {simulation_results['fields_per_wafer']}")
        print(f"  CD uniformity: {simulation_results['cd_uniformity']:.1f}%")
        print(f"  Dose uniformity: {simulation_results['dose_uniformity']:.1f}%")
        
        print("✓ Stepper/scanner modeling test PASSED")
        
    except Exception as e:
        print(f"✗ Stepper/scanner modeling test FAILED: {e}")
        return False
    
    return True

def test_process_optimization():
    """Test process optimization functionality"""
    print("\n=== Testing Process Optimization ===")
    
    manager = EnhancedLithographyManager()
    
    # Create optimization configuration
    optimization_config = ProcessOptimizationConfig(
        target_cd=22.0,  # nm
        cd_tolerance=1.0,  # nm
        target_uniformity=95.0,  # %
        max_ler=3.0,  # nm
        optimization_method='gradient_descent',
        max_iterations=20,
        convergence_threshold=0.1,
        parameter_bounds={
            'dose': (20.0, 30.0),
            'focus': (-0.1, 0.1),
            'numerical_aperture': (1.2, 1.4)
        }
    )
    
    wafer_specs = {
        'diameter': 300,
        'thickness': 0.775
    }
    
    try:
        # Test optimization for CMOS gate patterning process
        optimization_result = manager.optimize_process_conditions(
            "cmos_gate_22nm",
            optimization_config,
            wafer_specs
        )
        
        print(f"Optimization completed:")
        print(f"  Iterations: {optimization_result.iterations}")
        print(f"  Convergence achieved: {optimization_result.convergence_achieved}")
        print(f"  Final optimization score: {optimization_result.optimization_score:.2f}")
        
        print(f"\nOptimized conditions:")
        for param, value in optimization_result.optimized_conditions.items():
            if isinstance(value, (int, float)):
                print(f"  {param}: {value:.3f}")
        
        print(f"\nQuality metrics:")
        for metric, value in optimization_result.quality_metrics.items():
            if isinstance(value, (int, float)):
                print(f"  {metric}: {value:.2f}")
            else:
                print(f"  {metric}: {value}")
        
        print("✓ Process optimization test PASSED")
        
    except Exception as e:
        print(f"✗ Process optimization test FAILED: {e}")
        return False
    
    return True

def test_process_characterization():
    """Test process characterization and reporting"""
    print("\n=== Testing Process Characterization ===")
    
    manager = EnhancedLithographyManager()
    
    # Generate mock characterization data
    np.random.seed(42)  # For reproducible results
    characterization_data = {
        'critical_dimension': np.random.normal(22.0, 1.5, 50).tolist(),
        'uniformity': np.random.normal(94.0, 2.0, 50).tolist(),
        'line_edge_roughness': np.random.normal(2.8, 0.5, 50).tolist()
    }
    
    try:
        # Generate characterization report
        report = manager.generate_process_characterization_report(
            "cmos_gate_22nm",
            characterization_data
        )
        
        print(f"Characterization report generated:")
        print(f"  Process: {report['process_name']}")
        
        cd_stats = report['characterization_summary']['cd_statistics']
        print(f"\nCD Statistics:")
        print(f"  Mean: {cd_stats['mean']:.2f} nm")
        print(f"  Std: {cd_stats['std']:.2f} nm")
        print(f"  Range: {cd_stats['min']:.2f} - {cd_stats['max']:.2f} nm")
        
        capability = report['process_capability']
        print(f"\nProcess Capability:")
        print(f"  CD Cpk: {capability['cd_cpk']:.3f}")
        print(f"  Uniformity Cpk: {capability['uniformity_cpk']:.3f}")
        print(f"  LER Cpk: {capability['ler_cpk']:.3f}")
        
        if report['recommendations']:
            print(f"\nRecommendations:")
            for i, rec in enumerate(report['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        print("✓ Process characterization test PASSED")
        
    except Exception as e:
        print(f"✗ Process characterization test FAILED: {e}")
        return False
    
    return True

def test_equipment_database():
    """Test comprehensive equipment database"""
    print("\n=== Testing Equipment Database ===")
    
    manager = EnhancedLithographyManager()
    equipment_db = manager.create_stepper_scanner_database()
    
    print(f"Equipment Database Summary:")
    print(f"  Steppers/Scanners: {len(equipment_db.steppers)}")
    print(f"  E-beam systems: {len(equipment_db.ebeam_systems)}")
    print(f"  Mask aligners: {len(equipment_db.mask_aligners)}")
    
    # Test each equipment type
    for name, equipment in equipment_db.steppers.items():
        print(f"\n{name}:")
        print(f"  Manufacturer: {equipment.manufacturer}")
        print(f"  Wavelength: {equipment.wavelength} nm")
        print(f"  NA: {equipment.numerical_aperture}")
        print(f"  Resolution: {equipment.resolution_capability} nm")
        print(f"  Throughput: {equipment.throughput} WPH")
    
    print("✓ Equipment database test PASSED")
    return True

def main():
    """Run all enhanced lithography frontend tests"""
    print("Enhanced Lithography Frontend Test Suite")
    print("=" * 50)
    
    tests = [
        test_equipment_database,
        test_stepper_scanner_modeling,
        test_process_optimization,
        test_process_characterization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"Test failed with exception: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All enhanced lithography frontend tests PASSED!")
        return True
    else:
        print("❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
