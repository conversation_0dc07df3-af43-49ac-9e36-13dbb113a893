#!/usr/bin/env python3
"""
Enhanced Orchestrator Bridge Unit Tests
=======================================

Comprehensive unit tests for the Enhanced Orchestrator Bridge functionality.

Author: Dr. <PERSON><PERSON><PERSON>
"""

import sys
import os
import unittest
import time
from unittest.mock import Mock, patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestEnhancedOrchestratorBridge(unittest.TestCase):
    """Unit tests for Enhanced Orchestrator Bridge"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_start_time = time.time()
        
        # Mock the Cython imports to avoid dependency issues
        self.cython_mock = Mock()
        self.simulator_mock = Mock()
        
    def tearDown(self):
        """Clean up after test"""
        test_time = time.time() - self.test_start_time
        print(f"    Test completed in {test_time:.3f}s")
    
    def test_bridge_initialization(self):
        """Test Enhanced Orchestrator Bridge initialization"""
        print("\n  🔍 Testing bridge initialization...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            
            # Verify basic attributes
            self.assertIsNotNone(bridge.logger)
            self.assertIsNotNone(bridge.simulator)
            self.assertIsNotNone(bridge.executor)
            self.assertIn(bridge.execution_status, ['idle', 'running', 'completed', 'failed'])
            
            print("    ✅ Bridge initialized with required attributes")
            return True
            
        except Exception as e:
            print(f"    ❌ Bridge initialization failed: {e}")
            self.fail(f"Bridge initialization failed: {e}")
    
    def test_get_industrial_applications(self):
        """Test getting industrial applications"""
        print("\n  🔍 Testing get_industrial_applications...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            applications = bridge.get_industrial_applications()
            
            # Verify applications structure
            self.assertIsInstance(applications, list)
            self.assertGreater(len(applications), 0)
            
            # Verify each application has required fields
            required_fields = ['id', 'name', 'display_name', 'technology_node']
            for app in applications:
                for field in required_fields:
                    self.assertIn(field, app, f"Application missing field: {field}")
            
            print(f"    ✅ Found {len(applications)} valid industrial applications")
            return True
            
        except Exception as e:
            print(f"    ❌ Get industrial applications failed: {e}")
            self.fail(f"Get industrial applications failed: {e}")
    
    def test_get_module_status(self):
        """Test getting module status"""
        print("\n  🔍 Testing get_module_status...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            status = bridge.get_module_status()
            
            # Verify status structure
            self.assertIsInstance(status, dict)
            
            # Verify status values are boolean
            for module, available in status.items():
                self.assertIsInstance(available, bool, f"Module {module} status should be boolean")
            
            print(f"    ✅ Retrieved status for {len(status)} modules")
            return True
            
        except Exception as e:
            print(f"    ❌ Get module status failed: {e}")
            self.fail(f"Get module status failed: {e}")
    
    def test_get_available_modules(self):
        """Test getting available modules"""
        print("\n  🔍 Testing get_available_modules...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            modules = bridge.get_available_modules()
            
            # Verify modules structure
            self.assertIsInstance(modules, list)
            
            # Verify module names are strings
            for module in modules:
                self.assertIsInstance(module, str, f"Module name should be string: {module}")
            
            print(f"    ✅ Found {len(modules)} available modules")
            return True
            
        except Exception as e:
            print(f"    ❌ Get available modules failed: {e}")
            self.fail(f"Get available modules failed: {e}")
    
    def test_callback_functionality(self):
        """Test callback functionality"""
        print("\n  🔍 Testing callback functionality...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            
            # Test progress callback
            progress_calls = []
            def progress_callback(progress, message):
                progress_calls.append((progress, message))
            
            bridge.set_progress_callback(progress_callback)
            self.assertEqual(bridge.progress_callback, progress_callback)
            
            # Test status callback
            status_calls = []
            def status_callback(status, message):
                status_calls.append((status, message))
            
            bridge.set_status_callback(status_callback)
            self.assertEqual(bridge.status_callback, status_callback)
            
            print("    ✅ Callbacks set successfully")
            return True
            
        except Exception as e:
            print(f"    ❌ Callback functionality failed: {e}")
            self.fail(f"Callback functionality failed: {e}")
    
    def test_execute_industrial_application(self):
        """Test executing industrial application"""
        print("\n  🔍 Testing execute_industrial_application...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            
            # Get available applications
            applications = bridge.get_industrial_applications()
            self.assertGreater(len(applications), 0, "No applications available for testing")
            
            # Test execution with first application
            app_id = applications[0]['id']
            result = bridge.execute_industrial_application(app_id, "test_wafer")
            
            # Verify result structure
            self.assertIsInstance(result, dict)
            self.assertIn('success', result)
            self.assertIsInstance(result['success'], bool)
            
            if result['success']:
                # Verify success result fields
                expected_fields = ['application_name', 'display_name', 'execution_time_s']
                for field in expected_fields:
                    self.assertIn(field, result, f"Success result missing field: {field}")
                print(f"    ✅ Application executed successfully in {result.get('execution_time_s', 0):.3f}s")
            else:
                # Verify error result fields
                self.assertIn('error', result, "Failed result should contain error message")
                print(f"    ⚠️  Application execution failed: {result.get('error', 'Unknown error')}")
            
            return True
            
        except Exception as e:
            print(f"    ❌ Execute industrial application failed: {e}")
            self.fail(f"Execute industrial application failed: {e}")
    
    def test_execution_status_tracking(self):
        """Test execution status tracking"""
        print("\n  🔍 Testing execution status tracking...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            
            # Initial status should be idle
            self.assertEqual(bridge.execution_status, 'idle')
            
            # Test status changes during execution
            applications = bridge.get_industrial_applications()
            if applications:
                app_id = applications[0]['id']
                
                # Execute application and check status changes
                initial_status = bridge.execution_status
                result = bridge.execute_industrial_application(app_id, "test_wafer")
                final_status = bridge.execution_status
                
                # Status should change after execution
                expected_final_status = 'completed' if result.get('success') else 'failed'
                self.assertEqual(final_status, expected_final_status)
                
                print(f"    ✅ Status tracking: {initial_status} → {final_status}")
            
            return True
            
        except Exception as e:
            print(f"    ❌ Execution status tracking failed: {e}")
            self.fail(f"Execution status tracking failed: {e}")
    
    def test_mock_implementation_fallback(self):
        """Test mock implementation fallback when Cython not available"""
        print("\n  🔍 Testing mock implementation fallback...")
        
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            
            # Since Cython is likely not available in test environment,
            # verify that mock implementations work
            if not bridge.cython_available:
                print("    ℹ️  Using mock implementation (Cython not available)")
                
                # Test mock methods
                applications = bridge.get_industrial_applications()
                self.assertIsInstance(applications, list)
                self.assertGreater(len(applications), 0)
                
                modules = bridge.get_available_modules()
                self.assertIsInstance(modules, list)
                
                status = bridge.get_module_status()
                self.assertIsInstance(status, dict)
                
                print("    ✅ Mock implementation working correctly")
            else:
                print("    ℹ️  Cython backend available, skipping mock test")
            
            return True
            
        except Exception as e:
            print(f"    ❌ Mock implementation fallback failed: {e}")
            self.fail(f"Mock implementation fallback failed: {e}")

class TestLoggingAggregation(unittest.TestCase):
    """Unit tests for logging aggregation functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_start_time = time.time()
        
    def tearDown(self):
        """Clean up after test"""
        test_time = time.time() - self.test_start_time
        print(f"    Test completed in {test_time:.3f}s")
    
    def test_global_log_window_functionality(self):
        """Test Global Log Window functionality"""
        print("\n  🔍 Testing Global Log Window functionality...")

        try:
            # Test that the class can be imported and has expected methods
            from src.python.gui.enhanced_simulator_gui import GlobalLogWindow

            # Check that the class has expected methods
            expected_methods = ['add_log_entry']
            for method in expected_methods:
                if hasattr(GlobalLogWindow, method):
                    print(f"    ✅ GlobalLogWindow has {method} method")
                else:
                    print(f"    ⚠️  GlobalLogWindow missing {method} method")

            print("    ✅ Global Log Window class structure verified")
            return True

        except Exception as e:
            print(f"    ❌ Global Log Window functionality failed: {e}")
            self.fail(f"Global Log Window functionality failed: {e}")
    
    def test_status_broadcaster_functionality(self):
        """Test Status Broadcaster functionality"""
        print("\n  🔍 Testing Status Broadcaster functionality...")

        try:
            # Test that the class can be imported and has expected methods
            from src.python.gui.enhanced_simulator_gui import StatusBroadcaster

            # Check that the class has expected methods
            expected_methods = ['broadcast_status', 'broadcast_progress']
            for method in expected_methods:
                if hasattr(StatusBroadcaster, method):
                    print(f"    ✅ StatusBroadcaster has {method} method")
                else:
                    print(f"    ⚠️  StatusBroadcaster missing {method} method")

            print("    ✅ Status Broadcaster class structure verified")
            return True

        except Exception as e:
            print(f"    ❌ Status Broadcaster functionality failed: {e}")
            self.fail(f"Status Broadcaster functionality failed: {e}")

def run_unit_tests():
    """Run all unit tests"""
    print("🧪 Starting Enhanced Orchestrator Bridge Unit Tests")
    print("=" * 70)
    
    # Create test suites
    bridge_suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedOrchestratorBridge)
    logging_suite = unittest.TestLoader().loadTestsFromTestCase(TestLoggingAggregation)
    
    # Combine suites
    combined_suite = unittest.TestSuite([bridge_suite, logging_suite])
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(combined_suite)
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 Unit Test Summary:")
    print(f"  🧪 Tests Run: {result.testsRun}")
    print(f"  ✅ Passed: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"  ❌ Failed: {len(result.failures)}")
    print(f"  💥 Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n📈 Success Rate: {success_rate:.1f}%")
    
    if len(result.failures) == 0 and len(result.errors) == 0:
        print("\n🎉 All unit tests passed! Enhanced Orchestrator Bridge is working correctly.")
        return True
    else:
        print("\n⚠️  Some unit tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = run_unit_tests()
    sys.exit(0 if success else 1)
