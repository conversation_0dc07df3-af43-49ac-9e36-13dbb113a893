#!/usr/bin/env python3
"""
Test script for the enhanced doping Python frontend
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

import numpy as np
from doping import (
    DopingManager, IndustrialDopingProcesses, EquipmentModels, 
    ProcessOptimizer, DopingCharacterization
)
from doping.doping_manager import (
    ImplantationParameters, AnnealingParameters, IonSpecies, 
    ChannelingDirection, DamageModel
)
from doping.industrial_processes import DeviceType, ProcessNode
from doping.equipment_models import ImplanterType, AnnealingType
from doping.process_optimization import OptimizationObjective, OptimizationConstraint
from doping.characterization import CharacterizationMethod

class MockWafer:
    """Mock wafer object for testing"""
    def __init__(self):
        self.diameter = 300  # mm
        self.thickness = 775  # μm
        self.crystal_orientation = "<100>"
        self.substrate_type = "p-type"
        self.resistivity = 10.0  # Ω·cm

def test_doping_manager():
    """Test the DopingManager class"""
    print("=" * 60)
    print("Testing DopingManager")
    print("=" * 60)
    
    # Initialize doping manager
    doping_manager = DopingManager()
    print(f"✓ DopingManager initialized (Enhanced: {doping_manager.physics_engine is not None})")
    
    # Create mock wafer
    wafer = MockWafer()
    
    # Test implantation parameters
    implant_params = ImplantationParameters(
        species=IonSpecies.ARSENIC,
        energy=40.0,  # keV
        dose=5e15,    # cm^-2
        beam_current=15.0,
        tilt_angle=7.0,
        temperature=25.0,
        channeling=ChannelingDirection.RANDOM,
        damage_model=DamageModel.KINCHIN_PEASE
    )
    
    print(f"✓ Implantation parameters created: {implant_params.species.value} at {implant_params.energy} keV")
    
    # Test implantation simulation
    try:
        implant_results = doping_manager.simulate_implantation(wafer, implant_params)
        print(f"✓ Implantation simulation completed")
        print(f"  - Projected range: {implant_results.projected_range:.1f} nm")
        print(f"  - Range straggling: {implant_results.range_straggling:.1f} nm")
        print(f"  - Sheet resistance: {implant_results.sheet_resistance:.1f} Ω/sq")
        print(f"  - Junction depth: {implant_results.junction_depth:.1f} nm")
    except Exception as e:
        print(f"✗ Implantation simulation failed: {e}")
        return False
    
    # Test annealing parameters
    anneal_params = AnnealingParameters(
        temperature=1000.0,  # °C
        time=10.0,          # minutes
        ramp_rate=50.0,     # °C/min
        atmosphere="N2",
        rapid_thermal=True
    )
    
    print(f"✓ Annealing parameters created: {anneal_params.temperature}°C for {anneal_params.time} min")
    
    # Test annealing simulation
    try:
        anneal_results = doping_manager.simulate_annealing(wafer, anneal_params, implant_results)
        print(f"✓ Annealing simulation completed")
        print(f"  - Final sheet resistance: {anneal_results.sheet_resistance:.1f} Ω/sq")
        print(f"  - Final junction depth: {anneal_results.junction_depth:.1f} nm")
        if anneal_results.quality_metrics:
            for metric, value in anneal_results.quality_metrics.items():
                print(f"  - {metric}: {value}")
    except Exception as e:
        print(f"✗ Annealing simulation failed: {e}")
        return False
    
    # Test ion properties
    try:
        ion_props = doping_manager.get_ion_properties(IonSpecies.ARSENIC)
        print(f"✓ Ion properties retrieved for {IonSpecies.ARSENIC.value}")
        for prop, value in ion_props.items():
            print(f"  - {prop}: {value}")
    except Exception as e:
        print(f"✗ Ion properties retrieval failed: {e}")
    
    return True

def test_industrial_processes():
    """Test the IndustrialDopingProcesses class"""
    print("\n" + "=" * 60)
    print("Testing IndustrialDopingProcesses")
    print("=" * 60)
    
    # Initialize components
    doping_manager = DopingManager()
    industrial_processes = IndustrialDopingProcesses(doping_manager)
    
    print(f"✓ IndustrialDopingProcesses initialized")
    
    # List all processes
    process_names = industrial_processes.list_all_processes()
    print(f"✓ Available processes ({len(process_names)}):")
    for name in process_names:
        print(f"  - {name}")
    
    # Test specific process
    process_name = 'mosfet_source_drain'
    process = industrial_processes.get_process(process_name)
    if process:
        print(f"✓ Retrieved process: {process.name}")
        print(f"  - Application: {process.application}")
        print(f"  - Device type: {process.device_type.value}")
        print(f"  - Process node: {process.process_node.value}")
        print(f"  - Implantation steps: {len(process.implantation_steps)}")
        print(f"  - Annealing steps: {len(process.annealing_steps)}")
        print(f"  - Quality requirements: {len(process.quality_requirements)}")
    
    # Test process simulation
    wafer = MockWafer()
    try:
        simulation_results = industrial_processes.simulate_industrial_process(process_name, wafer)
        print(f"✓ Industrial process simulation completed")
        print(f"  - Process success: {simulation_results['process_success']}")
        print(f"  - Implantation results: {len(simulation_results['implantation_results'])}")
        print(f"  - Annealing results: {len(simulation_results['annealing_results'])}")
        if simulation_results['warnings']:
            print(f"  - Warnings: {len(simulation_results['warnings'])}")
    except Exception as e:
        print(f"✗ Industrial process simulation failed: {e}")
        return False
    
    # Test process filtering
    mosfet_processes = industrial_processes.get_processes_by_device_type(DeviceType.MOSFET)
    print(f"✓ MOSFET processes found: {len(mosfet_processes)}")
    
    node_180nm_processes = industrial_processes.get_processes_by_node(ProcessNode.NODE_180NM)
    print(f"✓ 180nm node processes found: {len(node_180nm_processes)}")
    
    return True

def test_equipment_models():
    """Test the EquipmentModels class"""
    print("\n" + "=" * 60)
    print("Testing EquipmentModels")
    print("=" * 60)
    
    equipment_models = EquipmentModels()
    print(f"✓ EquipmentModels initialized")
    
    # List all implanters
    implanters = equipment_models.list_all_implanters()
    print(f"✓ Available implanters ({len(implanters)}):")
    for implanter in implanters[:3]:  # Show first 3
        print(f"  - {implanter}")
    
    # List all annealing equipment
    annealing_equipment = equipment_models.list_all_annealing_equipment()
    print(f"✓ Available annealing equipment ({len(annealing_equipment)}):")
    for equipment in annealing_equipment[:3]:  # Show first 3
        print(f"  - {equipment}")
    
    # Test implanter recommendation
    try:
        recommendations = equipment_models.recommend_implanter(
            energy=40.0, current=15.0, species="As")
        print(f"✓ Implanter recommendations for 40 keV, 15 mA As implant:")
        for rec in recommendations[:2]:  # Show top 2
            print(f"  - {rec}")
    except Exception as e:
        print(f"✗ Implanter recommendation failed: {e}")
    
    # Test annealing equipment recommendation
    try:
        recommendations = equipment_models.recommend_annealing_equipment(
            temperature=1000.0, ramp_rate=50.0, atmosphere="N2")
        print(f"✓ Annealing equipment recommendations for 1000°C, 50°C/min, N2:")
        for rec in recommendations[:2]:  # Show top 2
            print(f"  - {rec}")
    except Exception as e:
        print(f"✗ Annealing equipment recommendation failed: {e}")
    
    # Test equipment capabilities
    try:
        capabilities = equipment_models.get_equipment_capabilities()
        print(f"✓ Equipment capabilities summary:")
        print(f"  - Total implanters: {capabilities['implanters']['total_count']}")
        print(f"  - Total annealing equipment: {capabilities['annealing']['total_count']}")
        print(f"  - Implanter energy range: {capabilities['implanters']['energy_range']['min']:.1f} - {capabilities['implanters']['energy_range']['max']:.1f} keV")
        print(f"  - Annealing temperature range: {capabilities['annealing']['temperature_range']['min']:.0f} - {capabilities['annealing']['temperature_range']['max']:.0f} °C")
    except Exception as e:
        print(f"✗ Equipment capabilities failed: {e}")
    
    return True

def test_process_optimization():
    """Test the ProcessOptimizer class"""
    print("\n" + "=" * 60)
    print("Testing ProcessOptimizer")
    print("=" * 60)
    
    # Initialize components
    doping_manager = DopingManager()
    industrial_processes = IndustrialDopingProcesses(doping_manager)
    equipment_models = EquipmentModels()
    optimizer = ProcessOptimizer(doping_manager, industrial_processes, equipment_models)
    
    print(f"✓ ProcessOptimizer initialized")
    
    # Test optimization constraints
    constraints = [
        OptimizationConstraint(parameter='energy', min_value=10.0, max_value=100.0),
        OptimizationConstraint(parameter='dose', min_value=1e14, max_value=1e16),
        OptimizationConstraint(parameter='temperature', min_value=25.0, max_value=400.0)
    ]
    
    print(f"✓ Optimization constraints created: {len(constraints)}")
    
    # Test design of experiments
    wafer = MockWafer()
    parameter_ranges = {
        'energy': (20.0, 80.0),
        'dose': (1e14, 1e16),
        'temperature': (25.0, 200.0)
    }
    
    try:
        doe_results = optimizer.design_of_experiments(wafer, parameter_ranges, n_experiments=9)
        print(f"✓ Design of experiments completed")
        print(f"  - Design points: {len(doe_results['design_points'])}")
        print(f"  - Successful experiments: {doe_results['n_successful_experiments']}")
        if doe_results['analysis']:
            print(f"  - Analysis completed: {len(doe_results['analysis'])} metrics")
    except Exception as e:
        print(f"✗ Design of experiments failed: {e}")
        return False
    
    return True

def test_characterization():
    """Test the DopingCharacterization class"""
    print("\n" + "=" * 60)
    print("Testing DopingCharacterization")
    print("=" * 60)
    
    characterization = DopingCharacterization()
    print(f"✓ DopingCharacterization initialized")
    
    # Create mock doping results
    from doping.doping_manager import DopingResults
    mock_results = DopingResults(
        projected_range=120.0,
        range_straggling=35.0,
        lateral_straggling=60.0,
        peak_concentration=2e20,
        sheet_resistance=80.0,
        junction_depth=150.0,
        concentration_profile=np.random.exponential(1e19, 100)
    )
    
    # Test SIMS profiling
    try:
        sims_profile = characterization.simulate_sims_profile(mock_results, species="As")
        print(f"✓ SIMS profiling simulation completed")
        print(f"  - Depth points: {len(sims_profile.depth)}")
        print(f"  - Detection limit: {sims_profile.detection_limit:.1e} atoms/cm³")
        print(f"  - Depth resolution: {sims_profile.depth_resolution:.1f} nm")
        print(f"  - Measurement time: {sims_profile.measurement_time:.1f} minutes")
    except Exception as e:
        print(f"✗ SIMS profiling failed: {e}")
    
    # Test C-V profiling
    try:
        cv_profile = characterization.simulate_cv_profile(mock_results)
        print(f"✓ C-V profiling simulation completed")
        print(f"  - Depth points: {len(cv_profile.depth)}")
        print(f"  - Built-in voltage: {cv_profile.built_in_voltage:.2f} V")
        print(f"  - Measurement frequency: {cv_profile.frequency:.0e} Hz")
    except Exception as e:
        print(f"✗ C-V profiling failed: {e}")
    
    # Test sheet resistance measurement
    try:
        sheet_res_result = characterization.measure_sheet_resistance(
            mock_results, CharacterizationMethod.FOUR_POINT_PROBE)
        print(f"✓ Sheet resistance measurement completed")
        print(f"  - Method: {sheet_res_result.method.value}")
        print(f"  - Sheet resistance: {sheet_res_result.sheet_resistance:.1f} Ω/sq")
        print(f"  - Uncertainty: ±{sheet_res_result.measurement_uncertainty['sheet_resistance']:.1f} Ω/sq")
    except Exception as e:
        print(f"✗ Sheet resistance measurement failed: {e}")
    
    # Test comprehensive characterization
    try:
        comprehensive_results = characterization.comprehensive_characterization(mock_results)
        print(f"✓ Comprehensive characterization completed")
        print(f"  - Methods used: {len(comprehensive_results['summary']['measurement_methods'])}")
        print(f"  - Electrical measurements: {len(comprehensive_results['electrical_measurements'])}")
        print(f"  - Physical profiles: {len(comprehensive_results['physical_profiles'])}")
        print(f"  - Quality grade: {comprehensive_results['quality_assessment']['overall_grade']}")
    except Exception as e:
        print(f"✗ Comprehensive characterization failed: {e}")
    
    return True

def main():
    """Run all tests"""
    print("Enhanced Doping Python Frontend Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Run all tests
    test_results.append(("DopingManager", test_doping_manager()))
    test_results.append(("IndustrialDopingProcesses", test_industrial_processes()))
    test_results.append(("EquipmentModels", test_equipment_models()))
    test_results.append(("ProcessOptimizer", test_process_optimization()))
    test_results.append(("DopingCharacterization", test_characterization()))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:25} : {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced doping Python frontend is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
