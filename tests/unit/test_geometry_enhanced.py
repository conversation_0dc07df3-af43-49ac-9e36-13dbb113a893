#!/usr/bin/env python3
"""
Test Enhanced Geometry Module
=============================

Test the enhanced geometry module with industrial examples and log window.

Author: Dr<PERSON>
"""

import sys
import os

# Set up the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_python_dir = os.path.join(current_dir, 'src', 'python')

# Add to Python path
if src_python_dir not in sys.path:
    sys.path.insert(0, src_python_dir)

# Change working directory
original_cwd = os.getcwd()
os.chdir(src_python_dir)

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QLabel
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont
    
    # Import geometry panel
    from gui.geometry_panel import GeometryPanel

    class GeometryTestWindow(QMainWindow):
        """Test window for enhanced geometry module"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("SemiPRO - Enhanced Geometry Module Test")
            self.setGeometry(50, 50, 1400, 900)

            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)

            # Header
            header_layout = QHBoxLayout()
            
            title = QLabel("🚀 SemiPRO Enhanced Geometry Module")
            title.setFont(QFont("Arial", 16, QFont.Bold))
            title.setStyleSheet("QLabel { color: #2E86AB; padding: 10px; }")
            header_layout.addWidget(title)
            
            header_layout.addStretch()
            
            status = QLabel("✅ Integrated | 🏭 7 Industrial Examples | 📊 Log Window")
            status.setFont(QFont("Arial", 10))
            status.setStyleSheet("QLabel { color: #666; padding: 5px; }")
            header_layout.addWidget(status)
            
            layout.addLayout(header_layout)

            # Create geometry panel
            self.geometry_panel = GeometryPanel()
            layout.addWidget(self.geometry_panel)

            # Set status bar message
            self.statusBar().showMessage("Enhanced Geometry Module Ready - Test all features!")

    def main():
        """Main function"""
        app = QApplication(sys.argv)
        
        # Set application properties
        app.setApplicationName("SemiPRO Geometry Test")
        app.setApplicationVersion("1.0")
        
        # Create and show test window
        window = GeometryTestWindow()
        window.show()
        
        print("🚀 Enhanced Geometry Module Test launched!")
        print("=" * 50)
        print("✅ Features to test:")
        print("  1. Navigate to 'Industrial Examples' tab")
        print("  2. Select any of the 7 industrial devices")
        print("  3. Click 'Run Example' to test functionality")
        print("  4. Click 'Show Geometry Logs' to view backend logs")
        print("  5. Try 'Create Device Pattern' in Device Patterns tab")
        print("  6. Test 3D visualization features")
        print("=" * 50)
        print("🔧 Industrial Devices Available:")
        print("  • 180nm MOSFET Transistor")
        print("  • 14nm FinFET 3D Transistor") 
        print("  • MEMS Accelerometer")
        print("  • 3D NAND Memory Cell Array")
        print("  • 1200V Power IGBT")
        print("  • GaN RF Amplifier")
        print("  • CMOS Image Sensor")
        print("=" * 50)
        
        # Run the application
        sys.exit(app.exec())

    if __name__ == "__main__":
        main()

except Exception as e:
    print(f"❌ Failed to launch geometry test: {e}")
    import traceback
    traceback.print_exc()
finally:
    # Restore original working directory
    os.chdir(original_cwd)
