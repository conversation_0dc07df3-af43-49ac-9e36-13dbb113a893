#!/usr/bin/env python3
"""
Comprehensive Deposition Module Test
====================================

Test the deposition module with various industrial scenarios
and validate the GUI integration with real C++ backend.
"""

import sys
import os
import json
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_deposition_scenarios():
    """Test various deposition scenarios"""
    
    print("🏭 COMPREHENSIVE DEPOSITION MODULE TEST")
    print("=" * 50)
    
    # Industrial deposition scenarios
    scenarios = {
        "gate_oxide": {
            "name": "Gate Oxide Deposition",
            "config": {
                "wafer": {
                    "name": "gate_oxide_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "deposition",
                    "material": "SiO2",
                    "thickness": 0.005,  # 5nm gate oxide
                    "technique": "ALD",
                    "temperature": 400.0,
                    "pressure": 0.1
                }
            }
        },
        "metal_interconnect": {
            "name": "Metal Interconnect Deposition",
            "config": {
                "wafer": {
                    "name": "metal_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "deposition",
                    "material": "Aluminum",
                    "thickness": 0.5,  # 500nm metal layer
                    "technique": "PVD",
                    "temperature": 200.0,
                    "pressure": 1e-6
                }
            }
        },
        "passivation_layer": {
            "name": "Passivation Layer Deposition",
            "config": {
                "wafer": {
                    "name": "passivation_wafer",
                    "diameter": 200.0,
                    "thickness": 0.525,
                    "material": "Silicon",
                    "crystal_orientation": "111"
                },
                "process": {
                    "operation": "deposition",
                    "material": "Si3N4",
                    "thickness": 0.1,  # 100nm passivation
                    "technique": "CVD",
                    "temperature": 800.0,
                    "pressure": 2.0
                }
            }
        }
    }
    
    success_count = 0
    total_scenarios = len(scenarios)
    results_summary = []
    
    for scenario_id, scenario in scenarios.items():
        print(f"\n🔬 Testing: {scenario['name']}")
        print("-" * 50)
        
        # Create config file
        config_file = f"config/deposition_{scenario_id}.json"
        os.makedirs("config", exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(scenario['config'], f, indent=2)
        
        try:
            # Run C++ simulation
            simulator_path = project_root / "build" / "simulator"
            
            print(f"🚀 Running deposition simulation...")
            print(f"   Material: {scenario['config']['process']['material']}")
            print(f"   Thickness: {scenario['config']['process']['thickness']} μm")
            print(f"   Technique: {scenario['config']['process']['technique']}")
            print(f"   Temperature: {scenario['config']['process']['temperature']} °C")
            
            start_time = time.time()
            result = subprocess.run([
                str(simulator_path),
                "--process", "deposition",
                "--config", config_file
            ], capture_output=True, text=True, timeout=30)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"⏱️  Execution time: {execution_time:.3f} seconds")
            
            if result.returncode == 0:
                print("✅ Simulation: SUCCESS")
                
                # Parse results using GUI parser
                from gui.enhanced_main_window import SimulationWorker
                worker = SimulationWorker("deposition", scenario['config'])
                parsed_results = worker.parse_simulation_output(result.stdout)
                
                print(f"📊 Results parsed: {len(parsed_results)} metrics")
                
                scenario_result = {
                    'name': scenario['name'],
                    'success': True,
                    'execution_time': execution_time,
                    'results': parsed_results
                }
                
                # Display key results
                for key, value in parsed_results.items():
                    if key == 'deposited_thickness':
                        target = scenario['config']['process']['thickness']
                        accuracy = (1 - abs(value - target) / target) * 100
                        print(f"   • Deposited Thickness: {value:.6f} μm (Target: {target} μm, Accuracy: {accuracy:.1f}%)")
                    elif key == 'temperature':
                        print(f"   • Process Temperature: {value:.1f} °C")
                    else:
                        print(f"   • {key.replace('_', ' ').title()}: {value}")
                
                success_count += 1
                
            else:
                print("❌ Simulation: FAILED")
                scenario_result = {
                    'name': scenario['name'],
                    'success': False,
                    'execution_time': execution_time,
                    'error': result.stderr.strip()
                }
                
            results_summary.append(scenario_result)
                
        except subprocess.TimeoutExpired:
            print("⏰ Simulation timed out")
        except Exception as e:
            print(f"💥 Exception: {e}")
    
    # Final summary
    print(f"\n🏆 DEPOSITION MODULE TEST RESULTS")
    print("=" * 50)
    print(f"✅ Successful scenarios: {success_count}/{total_scenarios}")
    print(f"📊 Success rate: {success_count/total_scenarios*100:.1f}%")
    
    if success_count == total_scenarios:
        print("🎉 ALL DEPOSITION SCENARIOS WORKING!")
        print("🏭 Industrial deposition processes validated:")
        for result in results_summary:
            if result['success']:
                print(f"   ✅ {result['name']} ({result['execution_time']:.3f}s)")
        
        print(f"\n🚀 GUI Integration Ready:")
        print("   • Launch: python launch_gui.py")
        print("   • Navigate to Deposition tab")
        print("   • Test with industrial parameters")
        print("   • View real-time visualization")
        return True
    else:
        print("⚠️  Some scenarios failed - check implementation")
        return False

if __name__ == "__main__":
    success = test_deposition_scenarios()
    sys.exit(0 if success else 1)
