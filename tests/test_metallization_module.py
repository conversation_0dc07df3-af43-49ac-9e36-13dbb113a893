#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Metallization Module
=========================================================

Unit tests, integration tests, and validation tests for the enhanced
metallization module including C++ backend, Cython integration,
database functionality, and industrial applications.

Author: Enhanced SemiPRO Development Team
"""

import unittest
import sys
import os
import tempfile
import shutil
from pathlib import Path
import numpy as np
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src', 'python'))

try:
    from enhanced_metallization_bridge import (
        EnhancedMetallizationBridge, MetallizationTechnique, EquipmentType,
        ProcessParameters, SimulationResults, MetalProperties, IndustrialMetallizationExamples
    )
    BRIDGE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Enhanced metallization bridge not available: {e}")
    BRIDGE_AVAILABLE = False

try:
    from metallization_database_manager import (
        MetallizationDatabaseManager, MetallizationProcess, MetalProperties as DBMetalProperties
    )
    DATABASE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Database manager not available: {e}")
    DATABASE_AVAILABLE = False


class TestMetalProperties(unittest.TestCase):
    """Test metal properties data structures"""
    
    def test_metal_properties_creation(self):
        """Test metal properties object creation"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        props = MetalProperties(
            symbol='Cu',
            name='Copper',
            atomic_mass=63.5,
            density=8.96,
            melting_point=1085.0,
            resistivity=1.7
        )
        
        self.assertEqual(props.symbol, 'Cu')
        self.assertEqual(props.name, 'Copper')
        self.assertEqual(props.atomic_mass, 63.5)
        self.assertEqual(props.density, 8.96)
        self.assertEqual(props.melting_point, 1085.0)
        self.assertEqual(props.resistivity, 1.7)


class TestProcessParameters(unittest.TestCase):
    """Test process parameters data structures"""
    
    def test_process_parameters_defaults(self):
        """Test default process parameters"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        params = ProcessParameters()
        
        self.assertEqual(params.technique, MetallizationTechnique.PVD_SPUTTERING)
        self.assertEqual(params.equipment, EquipmentType.APPLIED_MATERIALS_ENDURA)
        self.assertEqual(params.target_thickness, 100.0)
        self.assertEqual(params.temperature, 25.0)
        self.assertEqual(params.pressure, 1e-3)
        self.assertEqual(params.power, 1000.0)
    
    def test_process_parameters_custom(self):
        """Test custom process parameters"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        params = ProcessParameters(
            technique=MetallizationTechnique.ALD_THERMAL,
            equipment=EquipmentType.ASM_PULSAR,
            target_thickness=50.0,
            temperature=300.0,
            pressure=1e-4,
            power=0.0
        )
        
        self.assertEqual(params.technique, MetallizationTechnique.ALD_THERMAL)
        self.assertEqual(params.equipment, EquipmentType.ASM_PULSAR)
        self.assertEqual(params.target_thickness, 50.0)
        self.assertEqual(params.temperature, 300.0)
        self.assertEqual(params.pressure, 1e-4)
        self.assertEqual(params.power, 0.0)


class TestSimulationResults(unittest.TestCase):
    """Test simulation results data structures"""
    
    def test_simulation_results_defaults(self):
        """Test default simulation results"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        results = SimulationResults()
        
        self.assertFalse(results.success)
        self.assertEqual(results.actual_thickness, 0.0)
        self.assertEqual(results.uniformity, 0.0)
        self.assertEqual(results.step_coverage, 0.0)
        self.assertEqual(results.grain_size, 0.0)
        self.assertEqual(results.stress, 0.0)
        self.assertEqual(results.resistivity, 0.0)
        self.assertEqual(results.surface_roughness, 0.0)
        self.assertEqual(results.microstructure, "")
        self.assertEqual(results.defects, [])
        self.assertEqual(results.process_time, 0.0)
        self.assertEqual(results.equipment_used, "")
        self.assertEqual(results.backend_used, "fallback")


class TestEnhancedMetallizationBridge(unittest.TestCase):
    """Test enhanced metallization bridge functionality"""
    
    def setUp(self):
        """Set up test environment"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        # Create bridge without database for testing
        self.bridge = EnhancedMetallizationBridge()
    
    def test_bridge_initialization(self):
        """Test bridge initialization"""
        self.assertIsInstance(self.bridge, EnhancedMetallizationBridge)
        self.assertIsInstance(self.bridge.metal_database, dict)
        self.assertIsInstance(self.bridge.equipment_database, dict)
        self.assertIsInstance(self.bridge.device_database, dict)
    
    def test_get_available_metals(self):
        """Test getting available metals"""
        metals = self.bridge.get_available_metals()
        
        self.assertIsInstance(metals, list)
        self.assertGreater(len(metals), 0)
        self.assertIn('Cu', metals)
        self.assertIn('Al', metals)
        self.assertIn('W', metals)
    
    def test_get_metal_properties(self):
        """Test getting metal properties"""
        cu_props = self.bridge.get_metal_properties('Cu')
        
        self.assertIsNotNone(cu_props)
        self.assertEqual(cu_props.symbol, 'Cu')
        self.assertEqual(cu_props.name, 'Copper')
        self.assertGreater(cu_props.density, 0)
        self.assertGreater(cu_props.melting_point, 0)
    
    def test_get_available_equipment(self):
        """Test getting available equipment"""
        equipment = self.bridge.get_available_equipment()
        
        self.assertIsInstance(equipment, list)
        self.assertGreater(len(equipment), 0)
    
    def test_simulate_metallization_pvd_sputtering(self):
        """Test PVD sputtering simulation"""
        params = ProcessParameters(
            technique=MetallizationTechnique.PVD_SPUTTERING,
            target_thickness=100.0,
            temperature=200.0,
            pressure=3e-3,
            power=2000.0
        )
        
        result = self.bridge.simulate_metallization('test_device', 'Cu', params)
        
        self.assertIsInstance(result, SimulationResults)
        self.assertTrue(result.success)
        self.assertGreater(result.actual_thickness, 0)
        self.assertGreater(result.uniformity, 0)
        self.assertGreater(result.step_coverage, 0)
        self.assertGreater(result.process_time, 0)
    
    def test_simulate_metallization_ald(self):
        """Test ALD simulation"""
        params = ProcessParameters(
            technique=MetallizationTechnique.ALD_THERMAL,
            target_thickness=50.0,
            temperature=300.0,
            pressure=1e-3,
            power=0.0
        )
        
        result = self.bridge.simulate_metallization('test_device', 'TiN', params)
        
        self.assertIsInstance(result, SimulationResults)
        self.assertTrue(result.success)
        self.assertGreater(result.actual_thickness, 0)
        self.assertGreater(result.uniformity, 95.0)  # ALD should have high uniformity
        self.assertGreater(result.step_coverage, 95.0)  # ALD should have high step coverage
    
    def test_simulate_metallization_electroplating(self):
        """Test electroplating simulation"""
        params = ProcessParameters(
            technique=MetallizationTechnique.ECD_ELECTROPLATING,
            target_thickness=200.0,
            temperature=25.0,
            current_density=10.0
        )
        
        result = self.bridge.simulate_metallization('test_device', 'Cu', params)
        
        self.assertIsInstance(result, SimulationResults)
        self.assertTrue(result.success)
        self.assertGreater(result.actual_thickness, 0)
        self.assertEqual(result.step_coverage, 100.0)  # Electroplating should have perfect step coverage
    
    def test_simulate_unknown_metal(self):
        """Test simulation with unknown metal"""
        params = ProcessParameters()
        
        result = self.bridge.simulate_metallization('test_device', 'UnknownMetal', params)
        
        self.assertIsInstance(result, SimulationResults)
        self.assertFalse(result.success)


class TestIndustrialMetallizationExamples(unittest.TestCase):
    """Test industrial metallization examples"""
    
    def setUp(self):
        """Set up test environment"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        self.examples = IndustrialMetallizationExamples()
        self.bridge = EnhancedMetallizationBridge()
    
    def test_examples_initialization(self):
        """Test examples initialization"""
        self.assertIsInstance(self.examples, IndustrialMetallizationExamples)
        self.assertIsInstance(self.examples.applications, dict)
        self.assertGreater(len(self.examples.applications), 0)
    
    def test_get_available_applications(self):
        """Test getting available applications"""
        apps = self.examples.get_available_applications()
        
        self.assertIsInstance(apps, list)
        self.assertGreater(len(apps), 0)
        self.assertIn('advanced_interconnects', apps)
        self.assertIn('power_devices', apps)
        self.assertIn('mems_devices', apps)
    
    def test_get_application_info(self):
        """Test getting application information"""
        app_info = self.examples.get_application_info('advanced_interconnects')
        
        self.assertIsNotNone(app_info)
        self.assertIn('name', app_info)
        self.assertIn('industry', app_info)
        self.assertIn('description', app_info)
        self.assertIn('process_flow', app_info)
        self.assertIn('challenges', app_info)
        self.assertIsInstance(app_info['process_flow'], list)
        self.assertGreater(len(app_info['process_flow']), 0)
    
    def test_get_process_parameters_for_application(self):
        """Test getting process parameters for application"""
        params = self.examples.get_process_parameters_for_application('advanced_interconnects', 0)
        
        self.assertIsNotNone(params)
        self.assertIsInstance(params, ProcessParameters)
        self.assertGreater(params.target_thickness, 0)
    
    def test_simulate_application(self):
        """Test simulating complete application"""
        result = self.examples.simulate_application('advanced_interconnects', self.bridge)
        
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)
        self.assertIn('steps', result)
        self.assertIn('overall_metrics', result)
        self.assertTrue(result['success'])
        self.assertIsInstance(result['steps'], list)
        self.assertGreater(len(result['steps']), 0)


@unittest.skipUnless(DATABASE_AVAILABLE, "Database manager not available")
class TestMetallizationDatabaseManager(unittest.TestCase):
    """Test metallization database manager"""
    
    def setUp(self):
        """Set up test database"""
        # Use in-memory SQLite for testing
        self.test_db_config = {
            'host': 'localhost',
            'port': '5432',
            'database': 'test_semipro',
            'user': 'test_user',
            'password': 'test_pass'
        }
        
        # Mock the database connection for testing
        with patch('psycopg2.connect') as mock_connect:
            mock_conn = Mock()
            mock_connect.return_value = mock_conn
            self.db_manager = MetallizationDatabaseManager(self.test_db_config)
    
    def test_database_manager_initialization(self):
        """Test database manager initialization"""
        self.assertIsInstance(self.db_manager, MetallizationDatabaseManager)
    
    @patch('psycopg2.connect')
    def test_store_process(self, mock_connect):
        """Test storing process in database"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_connect.return_value = mock_conn
        
        process = MetallizationProcess(
            process_id="test-id",
            wafer_id="wafer-123",
            process_name="Test Process",
            process_type="pvd_sputtering",
            metal_symbol="Cu",
            target_thickness_nm=100.0
        )
        
        # Mock successful execution
        mock_cursor.execute.return_value = None
        
        result = self.db_manager.store_process(process)
        
        # Verify cursor.execute was called
        mock_cursor.execute.assert_called()


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete metallization module"""
    
    def setUp(self):
        """Set up integration test environment"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        self.bridge = EnhancedMetallizationBridge()
        self.examples = IndustrialMetallizationExamples()
    
    def test_end_to_end_simulation(self):
        """Test complete end-to-end simulation workflow"""
        # Get application parameters
        params = self.examples.get_process_parameters_for_application('advanced_interconnects', 0)
        self.assertIsNotNone(params)
        
        # Run simulation
        result = self.bridge.simulate_metallization('integration_test', 'Ta', params)
        
        # Verify results
        self.assertTrue(result.success)
        self.assertGreater(result.actual_thickness, 0)
        self.assertGreater(result.uniformity, 0)
        self.assertGreater(result.step_coverage, 0)
        self.assertIn(result.backend_used, ['cython', 'fallback'])
    
    def test_multiple_applications_simulation(self):
        """Test simulating multiple industrial applications"""
        applications = ['advanced_interconnects', 'power_devices', 'mems_devices']
        
        for app_name in applications:
            with self.subTest(application=app_name):
                result = self.examples.simulate_application(app_name, self.bridge)
                
                self.assertTrue(result['success'])
                self.assertGreater(len(result['steps']), 0)
                self.assertIn('overall_metrics', result)
                
                # Check that all steps completed successfully
                success_rate = result['overall_metrics'].get('success_rate', 0)
                self.assertGreater(success_rate, 0)


class TestPerformance(unittest.TestCase):
    """Performance tests for metallization module"""
    
    def setUp(self):
        """Set up performance test environment"""
        if not BRIDGE_AVAILABLE:
            self.skipTest("Bridge not available")
        
        self.bridge = EnhancedMetallizationBridge()
    
    def test_simulation_performance(self):
        """Test simulation performance"""
        import time
        
        params = ProcessParameters(
            technique=MetallizationTechnique.PVD_SPUTTERING,
            target_thickness=100.0
        )
        
        start_time = time.time()
        
        # Run multiple simulations
        for i in range(10):
            result = self.bridge.simulate_metallization(f'perf_test_{i}', 'Cu', params)
            self.assertTrue(result.success)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / 10
        
        # Each simulation should complete in reasonable time
        self.assertLess(avg_time, 1.0)  # Less than 1 second per simulation


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestMetalProperties,
        TestProcessParameters,
        TestSimulationResults,
        TestEnhancedMetallizationBridge,
        TestIndustrialMetallizationExamples,
        TestMetallizationDatabaseManager,
        TestIntegration,
        TestPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
