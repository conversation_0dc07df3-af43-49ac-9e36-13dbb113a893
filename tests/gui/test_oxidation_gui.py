#!/usr/bin/env python3
"""
Test Enhanced Oxidation GUI Integration
=======================================

Test the enhanced oxidation panel with industrial examples and real-time monitoring.
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_oxidation_gui():
    """Test the enhanced oxidation GUI panel"""
    print("🔥 Testing Enhanced Oxidation GUI Integration")
    print("=" * 50)
    
    try:
        # Import the enhanced oxidation panel
        from python.gui.oxidation_panel import OxidationPanel
        from python.gui.oxidation_log_window import OxidationLogWindow
        
        # Create QApplication
        app = QApplication(sys.argv)
        
        # Create main window
        main_window = QMainWindow()
        main_window.setWindowTitle("SemiPRO Enhanced Oxidation Module Test")
        main_window.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create oxidation panel
        print("✅ Creating enhanced oxidation panel...")
        oxidation_panel = OxidationPanel()
        layout.addWidget(oxidation_panel)
        
        # Create log window
        print("✅ Creating oxidation log window...")
        log_window = OxidationLogWindow()
        
        # Connect log window to panel
        if hasattr(oxidation_panel, 'log_signal'):
            oxidation_panel.log_signal.connect(log_window.add_oxidation_log)
        
        # Show windows
        main_window.show()
        log_window.show()
        
        print("✅ Enhanced oxidation GUI launched successfully!")
        print("\n🔥 GUI Features Available:")
        print("- Basic Oxidation tab with temperature/time controls")
        print("- Industrial Processes tab with real-world examples")
        print("- Process Monitoring tab with real-time visualization")
        print("- Results Analysis tab with comparison charts")
        print("- Dedicated oxidation log window")
        print("- Industrial examples: 180nm CMOS, 90nm CMOS, Flash, DRAM, STI")
        print("- Real-time process monitoring with matplotlib integration")
        print("- Fallback support for graceful degradation")
        
        print("\n🎉 Close the GUI windows to complete the test.")
        
        # Run the application
        return app.exec()
        
    except Exception as e:
        print(f"❌ Error testing oxidation GUI: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_oxidation_gui())
