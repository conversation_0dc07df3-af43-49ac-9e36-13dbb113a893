#!/usr/bin/env python3
"""
Simple test script for 3D oxidation visualization functionality.

This script tests the 3D visualization capabilities using the working
oxidation module setup.

Author: Dr. <PERSON><PERSON>
"""

import sys
import os
import traceback
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def test_3d_visualization_simple():
    """Test 3D visualization with simple approach"""
    print("🧪 Testing 3D Visualization (Simple)...")
    
    try:
        # Import using the working approach from oxidation_module_test.py
        sys.path.append('src/python')
        from oxidation import OxidationManager
        
        # Initialize manager
        oxidation_manager = OxidationManager()
        print("✅ OxidationManager initialized successfully")
        
        # Check if 3D visualization methods exist
        methods_to_check = [
            'generate_3d_visualization',
            'generate_process_animation', 
            'generate_cross_section_view'
        ]
        
        available_methods = []
        for method in methods_to_check:
            if hasattr(oxidation_manager, method):
                available_methods.append(method)
                print(f"✅ Method '{method}' found")
            else:
                print(f"❌ Method '{method}' not found")
        
        # Test 3D visualizer class
        print("\n🎨 Testing 3D Visualizer Class...")
        try:
            from oxidation.oxidation_3d_visualizer import Oxidation3DVisualizer
            visualizer = Oxidation3DVisualizer()
            print("✅ Oxidation3DVisualizer instantiated successfully")
            
            # Test with sample data
            sample_data = {
                'thickness_map': [[50.0 for _ in range(10)] for _ in range(10)],
                'stress_map': [[200.0 for _ in range(10)] for _ in range(10)],
                'interface_quality_map': [[1.0 for _ in range(10)] for _ in range(10)],
                'min_thickness': 45.0,
                'max_thickness': 55.0,
                'min_stress': 180.0,
                'max_stress': 220.0
            }
            
            # Test matplotlib visualization
            result = visualizer.create_matplotlib_3d_oxidation(sample_data)
            if result['success']:
                print("✅ Matplotlib 3D visualization working")
            else:
                print(f"❌ Matplotlib visualization failed: {result.get('error', 'Unknown')}")
                
        except ImportError as e:
            print(f"❌ 3D Visualizer import failed: {e}")
        
        # Test if 3D visualization methods work (if available)
        if available_methods:
            print(f"\n📊 Testing available methods: {available_methods}")
            
            # Test parameters
            test_params = {
                'target_thickness': 50.0,
                'temperature': 1273.15,
                'time': 7200,
                'atmosphere': 'dry_oxygen',
                'pressure': 1.0
            }
            
            # Test each available method
            for method in available_methods:
                try:
                    method_func = getattr(oxidation_manager, method)
                    if method == 'generate_3d_visualization':
                        result = method_func(test_params, 20, 20)
                    elif method == 'generate_process_animation':
                        result = method_func(test_params, 5)
                    elif method == 'generate_cross_section_view':
                        result = method_func(test_params, 'x', 0.5)
                    
                    if result and result.get('success', False):
                        print(f"✅ Method '{method}' executed successfully")
                    else:
                        print(f"❌ Method '{method}' failed: {result.get('error', 'Unknown') if result else 'No result'}")
                        
                except Exception as e:
                    print(f"❌ Method '{method}' error: {e}")
        
        print(f"\n✅ 3D Visualization test completed!")
        print(f"   - Available methods: {len(available_methods)}/{len(methods_to_check)}")
        return len(available_methods) > 0
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        traceback.print_exc()
        return False

def test_gui_integration_simple():
    """Test GUI integration with simple approach"""
    print("\n🖥️ Testing GUI Integration (Simple)...")
    
    try:
        # Test if GUI can be imported
        from PySide6.QtWidgets import QApplication
        
        # Create QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ QApplication created successfully")
        
        # Try to import oxidation panel
        try:
            sys.path.append('src/python')
            from gui.oxidation_panel import OxidationPanel
            
            panel = OxidationPanel()
            print("✅ OxidationPanel created successfully")
            
            # Check tab count
            tab_count = panel.tab_widget.count()
            tab_names = [panel.tab_widget.tabText(i) for i in range(tab_count)]
            print(f"✅ GUI has {tab_count} tabs: {tab_names}")
            
            # Check for 3D visualization tab
            if "3D Visualization" in tab_names:
                print("✅ 3D Visualization tab found in GUI")
                return True
            else:
                print("❌ 3D Visualization tab not found in GUI")
                return False
                
        except Exception as e:
            print(f"❌ GUI panel import failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ GUI test error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run simple 3D visualization tests"""
    print("🚀 Simple 3D Oxidation Visualization Tests")
    print("=" * 60)
    
    # Test results
    backend_success = test_3d_visualization_simple()
    gui_success = test_gui_integration_simple()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Backend Tests: {'✅ PASS' if backend_success else '❌ FAIL'}")
    print(f"   GUI Tests: {'✅ PASS' if gui_success else '❌ FAIL'}")
    
    overall_success = backend_success and gui_success
    print(f"\n🎯 Overall Result: {'✅ TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 3D Oxidation Visualization is ready!")
        print("   - Backend methods implemented ✅")
        print("   - GUI integration complete ✅")
        print("   - 3D visualizer class working ✅")
        print("\n🚀 Next steps:")
        print("   - Run: python launch_gui.py")
        print("   - Navigate to Oxidation → 3D Visualization tab")
        print("   - Test 3D surface views and animations")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
