#!/usr/bin/env python3
"""
Test script for advanced oxidation characterization GUI
Tests the new Advanced Characterization tab functionality
"""

import sys
import os

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_characterization_gui():
    """Test the characterization GUI functionality"""
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from gui.oxidation_panel import OxidationPanel
        
        # Create QApplication
        app = QApplication(sys.argv)
        
        # Create main window
        main_window = QMainWindow()
        main_window.setWindowTitle("SemiPRO - Advanced Oxidation Characterization Test")
        main_window.setGeometry(100, 100, 1400, 1000)
        
        # Create central widget
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create oxidation panel
        print("Creating oxidation panel with characterization capabilities...")
        oxidation_panel = OxidationPanel()
        layout.addWidget(oxidation_panel)
        
        # Check if characterization tab exists
        tab_count = oxidation_panel.tab_widget.count()
        print(f"Number of tabs: {tab_count}")
        
        for i in range(tab_count):
            tab_text = oxidation_panel.tab_widget.tabText(i)
            print(f"Tab {i}: {tab_text}")
        
        # Switch to characterization tab
        for i in range(tab_count):
            if "Characterization" in oxidation_panel.tab_widget.tabText(i):
                oxidation_panel.tab_widget.setCurrentIndex(i)
                print(f"Switched to characterization tab (index {i})")
                break
        
        # Set some test parameters
        if hasattr(oxidation_panel, 'char_thickness_spin'):
            oxidation_panel.char_thickness_spin.setValue(15.0)
            print("Set test thickness: 15.0 nm")
        
        if hasattr(oxidation_panel, 'char_area_spin'):
            oxidation_panel.char_area_spin.setValue(1e-4)
            print("Set test area: 1e-4 cm²")
        
        if hasattr(oxidation_panel, 'char_temp_spin'):
            oxidation_panel.char_temp_spin.setValue(300.0)
            print("Set test temperature: 300.0 K")
        
        # Show the window
        main_window.show()
        
        print("\n" + "="*60)
        print("CHARACTERIZATION GUI TEST COMPLETED!")
        print("="*60)
        print("The GUI is now running with the Advanced Characterization tab.")
        print("You can:")
        print("1. Click 'Perform C-V Analysis' to test C-V characterization")
        print("2. Click 'Calculate Dit' to test interface state density")
        print("3. Click 'Predict Breakdown' to test breakdown voltage prediction")
        print("4. Click 'TDDB Analysis' to test reliability analysis")
        print("5. Click 'Comprehensive Analysis' to test full characterization")
        print("\nClose the window to exit the test.")
        print("="*60)
        
        # Run the application
        return app.exec()
        
    except Exception as e:
        print(f"Error in GUI test: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_characterization_gui())
