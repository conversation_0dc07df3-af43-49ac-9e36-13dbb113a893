#!/usr/bin/env python3
"""
Unified GUI System Test Suite
=============================

Comprehensive test suite for the unified SemiPRO GUI system,
testing all components, integrations, and functionality.

Author: Dr<PERSON> <PERSON>
"""

import sys
import os
import unittest
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestUnifiedGUISystem(unittest.TestCase):
    """Test suite for the unified GUI system"""

    def setUp(self):
        """Set up test environment"""
        self.test_results = {}
        
    def test_pyside6_availability(self):
        """Test PySide6 availability"""
        try:
            from PySide6.QtWidgets import QApplication, QMainWindow
            from PySide6.QtCore import Qt, Signal
            from PySide6.QtGui import QFont, QColor
            self.test_results['pyside6'] = True
            logger.info("✓ PySide6: Available")
        except ImportError as e:
            self.test_results['pyside6'] = False
            logger.error(f"✗ PySide6: {e}")
            self.fail("PySide6 not available")
            
    def test_matplotlib_availability(self):
        """Test matplotlib availability"""
        try:
            import matplotlib
            matplotlib.use('Agg')  # Use non-interactive backend for testing
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg
            from matplotlib.figure import Figure
            self.test_results['matplotlib'] = True
            logger.info("✓ Matplotlib: Available")
        except ImportError as e:
            self.test_results['matplotlib'] = False
            logger.warning(f"⚠ Matplotlib: {e}")
            
    def test_orchestrator_bridge_import(self):
        """Test orchestrator bridge import"""
        try:
            from python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            self.test_results['orchestrator_bridge'] = True
            logger.info("✓ Orchestrator Bridge: Available")
        except ImportError as e:
            self.test_results['orchestrator_bridge'] = False
            logger.warning(f"⚠ Orchestrator Bridge: {e}")
            
    def test_unified_main_window_import(self):
        """Test unified main window import"""
        try:
            from python.gui.unified_main_window import UnifiedMainWindow
            self.test_results['unified_main_window'] = True
            logger.info("✓ Unified Main Window: Available")
        except ImportError as e:
            self.test_results['unified_main_window'] = False
            logger.error(f"✗ Unified Main Window: {e}")
            self.fail("Unified Main Window not available")
            
    def test_enhanced_process_flow_tree_import(self):
        """Test enhanced process flow tree import"""
        try:
            from python.gui.enhanced_process_flow_tree import EnhancedProcessFlowTree
            self.test_results['enhanced_process_flow_tree'] = True
            logger.info("✓ Enhanced Process Flow Tree: Available")
        except ImportError as e:
            self.test_results['enhanced_process_flow_tree'] = False
            logger.warning(f"⚠ Enhanced Process Flow Tree: {e}")
            
    def test_orchestrator_panel_import(self):
        """Test orchestrator panel import"""
        try:
            from python.gui.orchestrator_panel import OrchestratorPanel
            self.test_results['orchestrator_panel'] = True
            logger.info("✓ Orchestrator Panel: Available")
        except ImportError as e:
            self.test_results['orchestrator_panel'] = False
            logger.warning(f"⚠ Orchestrator Panel: {e}")
            
    def test_industrial_application_chooser_import(self):
        """Test industrial application chooser import"""
        try:
            from python.gui.industrial_application_chooser import IndustrialApplicationChooser
            self.test_results['industrial_application_chooser'] = True
            logger.info("✓ Industrial Application Chooser: Available")
        except ImportError as e:
            self.test_results['industrial_application_chooser'] = False
            logger.warning(f"⚠ Industrial Application Chooser: {e}")
            
    def test_enhanced_visualization_panel_import(self):
        """Test enhanced visualization panel import"""
        try:
            from python.gui.enhanced_visualization_panel import EnhancedVisualizationPanel
            self.test_results['enhanced_visualization_panel'] = True
            logger.info("✓ Enhanced Visualization Panel: Available")
        except ImportError as e:
            self.test_results['enhanced_visualization_panel'] = False
            logger.warning(f"⚠ Enhanced Visualization Panel: {e}")
            
    def test_base_module_panel_import(self):
        """Test base module panel import"""
        try:
            from python.gui.base_module_panel import BaseModulePanel
            self.test_results['base_module_panel'] = True
            logger.info("✓ Base Module Panel: Available")
        except ImportError as e:
            self.test_results['base_module_panel'] = False
            logger.warning(f"⚠ Base Module Panel: {e}")
            
    def test_module_panels_import(self):
        """Test individual module panels import"""
        modules = [
            'geometry_panel', 'oxidation_panel', 'doping_panel',
            'photolithography_panel', 'deposition_panel', 'etching_panel',
            'metallization_panel', 'packaging_panel', 'thermal_panel',
            'reliability_panel'
        ]
        
        available_modules = []
        for module in modules:
            try:
                __import__(f'python.gui.{module}')
                available_modules.append(module)
                logger.info(f"✓ {module}: Available")
            except ImportError:
                logger.warning(f"⚠ {module}: Not available")
                
        self.test_results['available_modules'] = available_modules
        self.test_results['module_count'] = len(available_modules)
        
    def test_enhanced_modules_import(self):
        """Test enhanced module panels import"""
        enhanced_modules = [
            'enhanced_deposition_panel', 'enhanced_etching_panel',
            'enhanced_metallization_panel', 'enhanced_lithography_panel',
            'enhanced_packaging_panel', 'enhanced_thermal_panel'
        ]
        
        available_enhanced = []
        for module in enhanced_modules:
            try:
                __import__(f'python.gui.{module}')
                available_enhanced.append(module)
                logger.info(f"✓ {module}: Available")
            except ImportError:
                logger.warning(f"⚠ {module}: Not available")
                
        self.test_results['available_enhanced_modules'] = available_enhanced
        self.test_results['enhanced_module_count'] = len(available_enhanced)
        
    def test_orchestrator_bridge_functionality(self):
        """Test orchestrator bridge basic functionality"""
        if not self.test_results.get('orchestrator_bridge', False):
            self.skipTest("Orchestrator bridge not available")
            
        try:
            from python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            
            # Test basic methods
            applications = bridge.get_industrial_applications()
            self.assertIsInstance(applications, list)
            self.assertGreater(len(applications), 0)
            
            module_status = bridge.get_module_status()
            self.assertIsInstance(module_status, dict)
            
            available_modules = bridge.get_available_modules()
            self.assertIsInstance(available_modules, list)
            
            self.test_results['orchestrator_functionality'] = True
            logger.info("✓ Orchestrator Bridge: Functionality test passed")
            
        except Exception as e:
            self.test_results['orchestrator_functionality'] = False
            logger.error(f"✗ Orchestrator Bridge functionality: {e}")
            
    def test_unified_main_window_instantiation(self):
        """Test unified main window instantiation"""
        if not self.test_results.get('unified_main_window', False):
            self.skipTest("Unified main window not available")
            
        try:
            from PySide6.QtWidgets import QApplication
            from python.gui.unified_main_window import UnifiedMainWindow
            
            # Create QApplication if not exists
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
                
            # Test window creation
            window = UnifiedMainWindow()
            self.assertIsNotNone(window)
            
            # Test basic properties
            self.assertIsNotNone(window.status_broadcaster)
            self.assertIsNotNone(window.module_windows)
            self.assertIsNotNone(window.process_flow_tree)
            self.assertIsNotNone(window.centralized_logger)
            
            self.test_results['unified_window_instantiation'] = True
            logger.info("✓ Unified Main Window: Instantiation test passed")
            
        except Exception as e:
            self.test_results['unified_window_instantiation'] = False
            logger.error(f"✗ Unified Main Window instantiation: {e}")
            
    def test_process_flow_tree_functionality(self):
        """Test process flow tree functionality"""
        if not self.test_results.get('enhanced_process_flow_tree', False):
            self.skipTest("Enhanced process flow tree not available")
            
        try:
            from PySide6.QtWidgets import QApplication
            from python.gui.enhanced_process_flow_tree import EnhancedProcessFlowTree
            
            # Create QApplication if not exists
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
                
            # Test tree creation
            tree = EnhancedProcessFlowTree()
            self.assertIsNotNone(tree)
            self.assertIsNotNone(tree.process_flow)
            
            # Test basic functionality
            ready_steps = tree.get_ready_steps()
            self.assertIsInstance(ready_steps, list)
            
            summary = tree.get_flow_summary()
            self.assertIsInstance(summary, dict)
            self.assertIn('total_steps', summary)
            
            self.test_results['process_flow_tree_functionality'] = True
            logger.info("✓ Enhanced Process Flow Tree: Functionality test passed")
            
        except Exception as e:
            self.test_results['process_flow_tree_functionality'] = False
            logger.error(f"✗ Enhanced Process Flow Tree functionality: {e}")
            
    def test_industrial_application_chooser_functionality(self):
        """Test industrial application chooser functionality"""
        if not self.test_results.get('industrial_application_chooser', False):
            self.skipTest("Industrial application chooser not available")
            
        try:
            from PySide6.QtWidgets import QApplication
            from python.gui.industrial_application_chooser import IndustrialApplicationChooser
            
            # Create QApplication if not exists
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
                
            # Test chooser creation
            chooser = IndustrialApplicationChooser()
            self.assertIsNotNone(chooser)
            self.assertIsInstance(chooser.applications, list)
            self.assertGreater(len(chooser.applications), 0)
            
            self.test_results['industrial_chooser_functionality'] = True
            logger.info("✓ Industrial Application Chooser: Functionality test passed")
            
        except Exception as e:
            self.test_results['industrial_chooser_functionality'] = False
            logger.error(f"✗ Industrial Application Chooser functionality: {e}")
            
    def test_enhanced_visualization_panel_functionality(self):
        """Test enhanced visualization panel functionality"""
        if not self.test_results.get('enhanced_visualization_panel', False):
            self.skipTest("Enhanced visualization panel not available")
            
        try:
            from PySide6.QtWidgets import QApplication
            from python.gui.enhanced_visualization_panel import EnhancedVisualizationPanel
            
            # Create QApplication if not exists
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
                
            # Test panel creation
            panel = EnhancedVisualizationPanel()
            self.assertIsNotNone(panel)
            self.assertIsNotNone(panel.current_data)
            
            self.test_results['enhanced_viz_functionality'] = True
            logger.info("✓ Enhanced Visualization Panel: Functionality test passed")
            
        except Exception as e:
            self.test_results['enhanced_viz_functionality'] = False
            logger.error(f"✗ Enhanced Visualization Panel functionality: {e}")
            
    def test_system_integration(self):
        """Test overall system integration"""
        try:
            from PySide6.QtWidgets import QApplication
            from python.gui.unified_main_window import UnifiedMainWindow
            
            # Create QApplication if not exists
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
                
            # Create unified window
            window = UnifiedMainWindow()
            
            # Test signal connections
            self.assertTrue(hasattr(window.status_broadcaster, 'status_updated'))
            self.assertTrue(hasattr(window.status_broadcaster, 'progress_updated'))
            self.assertTrue(hasattr(window.status_broadcaster, 'log_message'))
            
            # Test menu actions
            self.assertTrue(hasattr(window, 'run_process_flow'))
            self.assertTrue(hasattr(window, 'pause_simulation'))
            self.assertTrue(hasattr(window, 'resume_simulation'))
            self.assertTrue(hasattr(window, 'stop_simulation'))
            
            self.test_results['system_integration'] = True
            logger.info("✓ System Integration: Test passed")
            
        except Exception as e:
            self.test_results['system_integration'] = False
            logger.error(f"✗ System Integration: {e}")
            
    def tearDown(self):
        """Clean up after tests"""
        pass
        
    def print_test_summary(self):
        """Print comprehensive test summary"""
        print("\n" + "="*70)
        print("🧪 UNIFIED GUI SYSTEM TEST SUMMARY")
        print("="*70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result is True)
        
        print(f"📊 Overall Results: {passed_tests}/{total_tests} tests passed")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n📋 Component Availability:")
        components = [
            ('PySide6', 'pyside6'),
            ('Matplotlib', 'matplotlib'),
            ('Orchestrator Bridge', 'orchestrator_bridge'),
            ('Unified Main Window', 'unified_main_window'),
            ('Enhanced Process Flow Tree', 'enhanced_process_flow_tree'),
            ('Orchestrator Panel', 'orchestrator_panel'),
            ('Industrial App Chooser', 'industrial_application_chooser'),
            ('Enhanced Visualization', 'enhanced_visualization_panel'),
            ('Base Module Panel', 'base_module_panel')
        ]
        
        for name, key in components:
            status = "✓" if self.test_results.get(key, False) else "✗"
            print(f"  {status} {name}")
            
        print(f"\n📦 Module Panels: {self.test_results.get('module_count', 0)} available")
        print(f"🚀 Enhanced Modules: {self.test_results.get('enhanced_module_count', 0)} available")
        
        print("\n🔧 Functionality Tests:")
        func_tests = [
            ('Orchestrator Bridge', 'orchestrator_functionality'),
            ('Unified Window Creation', 'unified_window_instantiation'),
            ('Process Flow Tree', 'process_flow_tree_functionality'),
            ('Industrial Chooser', 'industrial_chooser_functionality'),
            ('Enhanced Visualization', 'enhanced_viz_functionality'),
            ('System Integration', 'system_integration')
        ]
        
        for name, key in func_tests:
            if key in self.test_results:
                status = "✓" if self.test_results[key] else "✗"
                print(f"  {status} {name}")
                
        print("\n🎯 System Status:")
        if passed_tests >= total_tests * 0.8:
            print("  🟢 EXCELLENT: System is ready for production use")
        elif passed_tests >= total_tests * 0.6:
            print("  🟡 GOOD: System is functional with minor limitations")
        else:
            print("  🔴 NEEDS WORK: System requires additional components")
            
        print("="*70)

def main():
    """Run the unified GUI system tests"""
    print("🚀 Starting Unified GUI System Test Suite...")
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestUnifiedGUISystem)
    
    # Run tests with custom result handling
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    # Get the test instance to access results
    test_instance = TestUnifiedGUISystem()
    
    # Run all test methods manually to collect results
    test_methods = [method for method in dir(test_instance) if method.startswith('test_')]
    
    for method_name in test_methods:
        try:
            method = getattr(test_instance, method_name)
            method()
        except Exception as e:
            logger.error(f"Test {method_name} failed: {e}")
            
    # Print summary
    test_instance.print_test_summary()
    
    return len(test_instance.test_results), sum(1 for r in test_instance.test_results.values() if r is True)

if __name__ == "__main__":
    total, passed = main()
    sys.exit(0 if passed >= total * 0.8 else 1)
