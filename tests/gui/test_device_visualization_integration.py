#!/usr/bin/env python3
"""
Test Device Visualization Integration
=====================================

Test script to verify that the enhanced metallization module properly
integrates with device visualization and creates proper device structures.
"""

import sys
import os

# Add the src/python directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_metallization_device_integration():
    """Test metallization module device integration"""
    print("Testing Enhanced Metallization Device Integration")
    print("=" * 50)
    
    try:
        # Import required modules
        from enhanced_metallization_bridge import EnhancedMetallizationBridge, DeviceStructure
        from enhanced_industrial_metallization_examples import IndustrialMetallizationExamples
        
        print("✓ Successfully imported metallization modules")
        
        # Initialize bridge and examples
        bridge = EnhancedMetallizationBridge()
        examples = IndustrialMetallizationExamples()
        
        print("✓ Successfully initialized metallization components")
        
        # Test device structure creation
        device = DeviceStructure(
            name="Test Device",
            device_type="Test",
            technology_node="Test"
        )
        
        print("✓ Successfully created device structure")
        print(f"  Device name: {device.name}")
        print(f"  Device type: {device.device_type}")
        print(f"  Technology node: {device.technology_node}")
        
        # Test industrial application
        print("\nTesting industrial application...")
        result = examples.run_application("advanced_interconnects")
        
        print("✓ Successfully ran industrial application")
        print(f"  Overall success: {result.get('overall_success', False)}")
        print(f"  Has device structure: {'device_structure' in result}")
        
        if 'device_structure' in result:
            device_struct = result['device_structure']
            print(f"  Device name: {device_struct.name}")
            print(f"  Device type: {device_struct.device_type}")
            print(f"  Technology node: {device_struct.technology_node}")
            print(f"  Metal layers: {len(device_struct.metal_layers)}")
        
        # Test performance metrics
        if 'performance_metrics' in result:
            metrics = result['performance_metrics']
            print(f"  Performance metrics available: {len(metrics)} metrics")
            for key, value in list(metrics.items())[:3]:  # Show first 3 metrics
                print(f"    {key}: {value}")
        
        print("\n✓ All device integration tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_signal_integration():
    """Test GUI signal integration"""
    print("\nTesting GUI Signal Integration")
    print("=" * 30)
    
    try:
        # Test enhanced metallization panel signal
        from gui.enhanced_metallization_panel import EnhancedMetallizationPanel
        
        print("✓ Successfully imported enhanced metallization panel")
        
        # Check if panel has the required signal
        panel = EnhancedMetallizationPanel()
        
        if hasattr(panel, 'metallization_updated'):
            print("✓ Enhanced metallization panel has metallization_updated signal")
        else:
            print("✗ Enhanced metallization panel missing metallization_updated signal")
            return False
        
        # Test enhanced etching panel signal
        from gui.enhanced_etching_panel import EnhancedEtchingPanel
        
        print("✓ Successfully imported enhanced etching panel")
        
        etching_panel = EnhancedEtchingPanel()
        
        if hasattr(etching_panel, 'etching_updated'):
            print("✓ Enhanced etching panel has etching_updated signal")
        else:
            print("✗ Enhanced etching panel missing etching_updated signal")
            return False
        
        print("\n✓ All GUI signal integration tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ GUI signal test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("SemiPRO Device Visualization Integration Test")
    print("=" * 50)
    
    # Run tests
    device_test_passed = test_metallization_device_integration()
    gui_test_passed = test_gui_signal_integration()
    
    print("\n" + "=" * 50)
    print("FINAL RESULTS")
    print("=" * 50)
    print(f"Device Integration Test: {'PASSED' if device_test_passed else 'FAILED'}")
    print(f"GUI Signal Integration Test: {'PASSED' if gui_test_passed else 'FAILED'}")
    
    if device_test_passed and gui_test_passed:
        print("\n🎉 ALL INTEGRATION TESTS PASSED!")
        print("Enhanced metallization module is properly integrated with device visualization!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the integration issues above.")
        sys.exit(1)
