#!/usr/bin/env python3
"""
Test script for the enhanced doping GUI panel
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# Add src/python to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

try:
    from gui.doping_panel import DopingPanel
    GUI_AVAILABLE = True
except ImportError as e:
    print(f"GUI modules not available: {e}")
    GUI_AVAILABLE = False

class TestDopingGUI(QMainWindow):
    """Test window for doping GUI"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Enhanced Doping Panel Test")
        self.setGeometry(100, 100, 1400, 900)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create doping panel
        self.doping_panel = DopingPanel()
        layout.addWidget(self.doping_panel)
        
        # Connect signals
        self.doping_panel.doping_updated.connect(self.on_doping_updated)
        
    def on_doping_updated(self, results):
        """Handle doping results update"""
        print(f"Doping results updated: {type(results)}")
        if hasattr(results, 'sheet_resistance'):
            print(f"Sheet resistance: {results.sheet_resistance:.1f} Ω/sq")
        if hasattr(results, 'junction_depth'):
            print(f"Junction depth: {results.junction_depth:.1f} nm")

def test_doping_gui():
    """Test the doping GUI"""
    if not GUI_AVAILABLE:
        print("❌ GUI modules not available")
        return False

    print("🧪 Testing Enhanced Doping GUI Panel")
    print("=" * 50)

    # Get or create application
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # Create test window
        window = TestDopingGUI()
        
        # Test basic functionality
        print("✓ DopingPanel created successfully")
        print(f"✓ Panel has {window.doping_panel.children().__len__()} child widgets")
        
        # Test parameter access
        params = window.doping_panel.get_current_parameters()
        print(f"✓ Current parameters retrieved: {len(params)} parameters")
        
        # Test parameter setting
        test_params = {
            'ion_species': 'B',
            'energy': 25.0,
            'dose': '1e16'
        }
        window.doping_panel.set_parameters(test_params)
        print("✓ Parameters set successfully")
        
        # Test reset
        window.doping_panel.reset_parameters()
        print("✓ Parameters reset successfully")
        
        # Test status
        status = window.doping_panel.get_simulation_status()
        print(f"✓ Simulation status: {status}")
        
        # Test without showing window (headless mode)
        print("✓ GUI window created (headless mode)")
        print("\n🎉 Enhanced Doping GUI Panel test completed successfully!")

        # Return success without running event loop
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        return False

def test_doping_components():
    """Test individual doping components"""
    print("\n🔧 Testing Doping Components")
    print("=" * 50)
    
    try:
        # Test imports
        from gui.doping_panel import DopingVisualizationWidget, DopingSimulationThread, MockWafer
        print("✓ All doping GUI components imported successfully")
        
        # Test MockWafer
        wafer = MockWafer()
        print(f"✓ MockWafer created: {wafer.diameter}mm diameter")
        
        # Test visualization widget (without showing)
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            
        viz_widget = DopingVisualizationWidget()
        print("✓ DopingVisualizationWidget created successfully")
        print(f"✓ Visualization has {viz_widget.plot_type_combo.count()} plot types")
        
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Enhanced Doping GUI Test Suite")
    print("=" * 60)
    
    # Test components first
    component_test = test_doping_components()
    
    if component_test:
        # Test full GUI
        gui_test = test_doping_gui()
        
        if gui_test:
            print("\n🎉 All tests passed!")
            return 0
        else:
            print("\n❌ GUI test failed")
            return 1
    else:
        print("\n❌ Component test failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
