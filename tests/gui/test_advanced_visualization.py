#!/usr/bin/env python3
"""
Advanced Visualization Test
===========================

Test the advanced visualization features including:
- 3D surface plots
- Cross-sectional views
- Contour plots
- Process flow diagrams
- Comparative analysis
"""

import sys
import os
import json
import subprocess
import time
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_advanced_visualization():
    """Test advanced visualization features"""
    
    print("🎨 ADVANCED VISUALIZATION TEST")
    print("=" * 50)
    
    # Test scenarios for different modules
    test_scenarios = {
        "oxidation": {
            "config": {
                "wafer": {
                    "name": "viz_test_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "oxidation",
                    "temperature": 1000.0,
                    "time": 0.5,
                    "atmosphere": "dry",
                    "target_thickness": 0.1
                }
            }
        },
        "deposition": {
            "config": {
                "wafer": {
                    "name": "viz_test_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "deposition",
                    "material": "SiO2",
                    "thickness": 0.2,
                    "technique": "CVD",
                    "temperature": 600.0,
                    "pressure": 1.0
                }
            }
        },
        "etching": {
            "config": {
                "wafer": {
                    "name": "viz_test_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "etching",
                    "material": "Silicon",
                    "etch_depth": 2.0,
                    "chemistry": "SF6/O2",
                    "pressure": 0.01,
                    "power": 300.0,
                    "temperature": 20.0
                }
            }
        },
        "doping": {
            "config": {
                "wafer": {
                    "name": "viz_test_wafer",
                    "diameter": 300.0,
                    "thickness": 0.725,
                    "material": "Silicon",
                    "crystal_orientation": "100"
                },
                "process": {
                    "operation": "doping",
                    "dopant_type": "phosphorus",
                    "concentration": 1e15,
                    "energy": 50.0,
                    "temperature": 1000.0,
                    "time": 0.5
                }
            }
        }
    }
    
    print("🔬 Testing simulation data generation...")
    
    # Run simulations and collect results
    simulation_results = {}
    success_count = 0
    
    for module_name, scenario in test_scenarios.items():
        print(f"\n📊 Testing {module_name.title()} Module:")
        
        # Create config file
        config_file = f"config/viz_test_{module_name}.json"
        os.makedirs("config", exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(scenario['config'], f, indent=2)
        
        try:
            # Run C++ simulation
            simulator_path = project_root / "build" / "simulator"
            
            result = subprocess.run([
                str(simulator_path),
                "--process", module_name,
                "--config", config_file
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"   ✅ Simulation successful")
                
                # Parse results using GUI parser
                from gui.enhanced_main_window import SimulationWorker
                worker = SimulationWorker(module_name, scenario['config'])
                parsed_results = worker.parse_simulation_output(result.stdout)
                
                simulation_results[module_name] = parsed_results
                success_count += 1
                
                print(f"   📈 Results: {len(parsed_results)} metrics parsed")
                for key, value in list(parsed_results.items())[:3]:  # Show first 3
                    print(f"      • {key}: {value}")
                
            else:
                print(f"   ❌ Simulation failed")
                simulation_results[module_name] = {}
                
        except Exception as e:
            print(f"   💥 Exception: {e}")
            simulation_results[module_name] = {}
    
    print(f"\n🎯 Simulation Results Summary:")
    print(f"   ✅ Successful simulations: {success_count}/4")
    print(f"   📊 Success rate: {success_count/4*100:.1f}%")
    
    # Test advanced visualization features
    print(f"\n🎨 Testing Advanced Visualization Features...")
    
    try:
        from gui.advanced_visualization import AdvancedVisualizationWidget
        from PySide6.QtWidgets import QApplication
        
        # Create Qt application for testing
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create advanced visualization widget
        viz_widget = AdvancedVisualizationWidget()
        
        visualization_tests = [
            "2D Profile",
            "3D Surface", 
            "Cross-Section",
            "Contour Plot",
            "Process Flow",
            "Comparative Analysis"
        ]
        
        viz_success_count = 0
        
        for viz_type in visualization_tests:
            print(f"\n🖼️  Testing {viz_type}:")
            
            try:
                # Set plot type
                viz_widget.plot_type_combo.setCurrentText(viz_type)
                
                # Test with each module's results
                for module_name, results in simulation_results.items():
                    if results:  # Only test if we have results
                        viz_widget.plot_advanced_results(module_name, results)
                        print(f"   ✅ {module_name.title()}: {viz_type} rendered successfully")
                
                viz_success_count += 1
                print(f"   🎉 {viz_type}: ALL MODULES TESTED")
                
            except Exception as e:
                print(f"   ❌ {viz_type}: Failed - {e}")
        
        print(f"\n🏆 ADVANCED VISUALIZATION TEST RESULTS")
        print("=" * 50)
        print(f"✅ Successful visualization types: {viz_success_count}/{len(visualization_tests)}")
        print(f"📊 Visualization success rate: {viz_success_count/len(visualization_tests)*100:.1f}%")
        
        if viz_success_count == len(visualization_tests):
            print("🎉 ALL ADVANCED VISUALIZATION FEATURES WORKING!")
            print("🎨 Available visualization types:")
            for viz_type in visualization_tests:
                print(f"   ✅ {viz_type}")
            
            print(f"\n🚀 Advanced Visualization Ready:")
            print("   • Launch: python launch_gui.py")
            print("   • Navigate to any module tab")
            print("   • Click 'Advanced View' tab")
            print("   • Select visualization type from dropdown")
            print("   • Adjust 3D view angles with sliders")
            print("   • Toggle grid and colorbar options")
            print("   • View cross-sections at different positions")
            
            return True
        else:
            print("⚠️  Some visualization features failed")
            return False
            
    except ImportError as e:
        print(f"❌ Advanced visualization not available: {e}")
        return False
    except Exception as e:
        print(f"💥 Visualization test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_advanced_visualization()
    sys.exit(0 if success else 1)
