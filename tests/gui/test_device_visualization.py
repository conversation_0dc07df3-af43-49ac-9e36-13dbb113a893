#!/usr/bin/env python3
"""
Test Device Visualization
========================

Test script to verify that device visualization is working properly
with the enhanced etching module.
"""

import sys
import os
from pathlib import Path

# Add src/python to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "python"))

def test_device_visualization():
    """Test device visualization with real simulation data"""
    print("🧪 Testing Device Visualization")
    print("=" * 40)
    
    try:
        # Import required modules
        from enhanced_industrial_etching_examples import EnhancedIndustrialEtchingExamples
        from gui.etching_visualization_widget import EtchingVisualizationWidget
        
        # Create examples instance
        examples = EnhancedIndustrialEtchingExamples()
        print("✓ Enhanced etching examples loaded")
        
        # Run a simulation
        print("\n📊 Running MEMS accelerometer simulation...")
        result = examples.simulate_industrial_application('mems_accelerometer')
        
        if result.get('success', False):
            print("✓ Simulation completed successfully")
            
            # Check result structure
            print(f"\n📋 Result structure:")
            for key, value in result.items():
                print(f"  {key}: {type(value)}")
                
                if key == 'device_structure':
                    device = value
                    print(f"    Device name: {getattr(device, 'name', 'N/A')}")
                    print(f"    Device type: {getattr(device, 'device_type', 'N/A')}")
                    print(f"    Technology: {getattr(device, 'technology_node', 'N/A')}")
                    print(f"    Layers: {len(getattr(device, 'layers', []))}")
                    print(f"    Features: {len(getattr(device, 'target_features', []))}")
                    
                    # Print layer details
                    layers = getattr(device, 'layers', [])
                    if layers:
                        print(f"    Layer details:")
                        for i, layer in enumerate(layers):
                            print(f"      {i+1}. {layer.get('material', 'Unknown')} - {layer.get('thickness', 0):.2f}μm")
                    
                    # Print feature details
                    features = getattr(device, 'target_features', [])
                    if features:
                        print(f"    Feature details:")
                        for i, feature in enumerate(features):
                            print(f"      {i+1}. {feature.get('name', 'Unknown')} - {feature.get('width', 0):.2f}μm x {feature.get('depth', 0):.2f}μm")
                
                elif key == 'quality_assessment':
                    quality = value
                    print(f"    Overall score: {quality.get('overall_quality_score', 0):.1f}%")
                    print(f"    Grade: {quality.get('quality_grade', 'N/A')}")
                    print(f"    Meets specs: {quality.get('meets_specifications', False)}")
            
            # Test visualization widget
            print(f"\n🎨 Testing visualization widget...")
            try:
                viz_widget = EtchingVisualizationWidget()
                print("✓ Visualization widget created")
                
                # Update with results
                viz_widget.update_visualization(result)
                print("✓ Visualization updated with simulation results")
                
                print(f"\n✅ Device visualization test completed successfully!")
                print(f"   The enhanced etching module now properly displays:")
                print(f"   • Device cross-sections with actual layers")
                print(f"   • Etched features and dimensions")
                print(f"   • Quality assessment metrics")
                print(f"   • Process evolution data")
                
                return True
                
            except Exception as e:
                print(f"❌ Visualization widget error: {e}")
                return False
        else:
            print(f"❌ Simulation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_all_applications():
    """Test visualization with all industrial applications"""
    print("\n🏭 Testing All Industrial Applications")
    print("=" * 40)
    
    try:
        from enhanced_industrial_etching_examples import EnhancedIndustrialEtchingExamples
        
        examples = EnhancedIndustrialEtchingExamples()
        applications = examples.get_available_applications()
        
        print(f"Available applications: {len(applications)}")
        
        success_count = 0
        for app_name in applications:
            print(f"\n📱 Testing {app_name}...")
            try:
                result = examples.simulate_industrial_application(app_name)
                if result.get('success', False):
                    device = result.get('device_structure')
                    if device:
                        print(f"  ✓ Device: {getattr(device, 'name', 'Unknown')}")
                        print(f"  ✓ Type: {getattr(device, 'device_type', 'Unknown')}")
                        print(f"  ✓ Layers: {len(getattr(device, 'layers', []))}")
                        print(f"  ✓ Features: {len(getattr(device, 'target_features', []))}")
                        success_count += 1
                    else:
                        print(f"  ❌ No device structure in result")
                else:
                    print(f"  ❌ Simulation failed")
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        print(f"\n📊 Summary: {success_count}/{len(applications)} applications successful")
        return success_count == len(applications)
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main test function"""
    print("🔬 Enhanced Etching Device Visualization Test")
    print("=" * 50)
    
    # Test basic device visualization
    test1_success = test_device_visualization()
    
    # Test all applications
    test2_success = test_all_applications()
    
    print(f"\n🎯 Final Results:")
    print(f"  Device Visualization Test: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"  All Applications Test: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 All tests passed! Device visualization is working correctly.")
        print(f"   The enhanced etching module now shows real device structures")
        print(f"   instead of just simulation parameters.")
        return 0
    else:
        print(f"\n❌ Some tests failed. Device visualization needs more work.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
