#!/usr/bin/env python3
"""
Test Geometry GUI Module
========================

Test the enhanced geometry module with industrial examples integration.

Author: Dr. <PERSON><PERSON>
"""

import sys
import os

# Add the src/python directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from gui.geometry_panel import GeometryPanel

class TestGeometryWindow(QMainWindow):
    """Test window for geometry panel"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SemiPRO - Geometry Module Test")
        self.setGeometry(100, 100, 1400, 900)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create geometry panel
        self.geometry_panel = GeometryPanel()
        layout.addWidget(self.geometry_panel)

def main():
    """Main function to test geometry GUI"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("SemiPRO Geometry Test")
    app.setApplicationVersion("1.0")
    
    # Create and show test window
    window = TestGeometryWindow()
    window.show()
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
