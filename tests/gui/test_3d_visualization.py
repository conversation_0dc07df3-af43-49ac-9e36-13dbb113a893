#!/usr/bin/env python3
"""
Test script for 3D oxidation visualization functionality.

This script tests the comprehensive 3D visualization capabilities
including surface views, cross-sections, stress distributions,
interface quality maps, and process animations.

Author: Dr<PERSON><PERSON>
"""

import sys
import os
import traceback
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def test_3d_visualization_backend():
    """Test 3D visualization backend functionality"""
    print("🧪 Testing 3D Visualization Backend...")
    
    try:
        from oxidation import OxidationManager
        from geometry import GeometryManager
        
        # Initialize managers
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager(wafer_diameter=200.0, wafer_thickness=0.5)

        # Get wafer from geometry manager
        wafer = geometry_manager.wafer
        oxidation_manager.set_wafer(wafer)
        
        # Test parameters
        test_params = {
            'target_thickness': 50.0,  # nm
            'temperature': 1273.15,    # K (1000°C)
            'time': 7200,              # seconds (2 hours)
            'atmosphere': 'dry_oxygen',
            'pressure': 1.0
        }
        
        print("✅ Backend initialization successful")
        
        # Test 1: 3D Surface Visualization
        print("\n📊 Test 1: 3D Surface Visualization")
        viz_result = oxidation_manager.generate_3d_visualization(test_params, resolution_x=30, resolution_y=30)
        
        if viz_result['success']:
            viz_data = viz_result['visualization_data']
            print(f"✅ 3D visualization generated successfully")
            print(f"   - Grid resolution: {viz_data['grid_resolution_x']}x{viz_data['grid_resolution_y']}")
            print(f"   - Thickness range: {viz_data['min_thickness']:.2f} - {viz_data['max_thickness']:.2f} nm")
            print(f"   - Stress range: {viz_data['min_stress']:.2f} - {viz_data['max_stress']:.2f} MPa")
            print(f"   - Vertices count: {len(viz_data['vertices_x'])}")
            print(f"   - Faces count: {len(viz_data['faces']) // 3}")
        else:
            print(f"❌ 3D visualization failed: {viz_result.get('error', 'Unknown error')}")
        
        # Test 2: Process Animation
        print("\n🎬 Test 2: Process Animation")
        anim_result = oxidation_manager.generate_process_animation(test_params, num_frames=10)
        
        if anim_result['success']:
            frames = anim_result['frames']
            print(f"✅ Process animation generated successfully")
            print(f"   - Number of frames: {len(frames)}")
            print(f"   - Time range: 0 - {frames[-1]['time']:.1f} seconds")
            print(f"   - Process stages: {[f['description'] for f in frames[::3]]}")
        else:
            print(f"❌ Process animation failed: {anim_result.get('error', 'Unknown error')}")
        
        # Test 3: Cross-Section Views
        print("\n✂️ Test 3: Cross-Section Views")
        for direction in ['x', 'y']:
            cross_result = oxidation_manager.generate_cross_section_view(test_params, direction, 0.5)
            
            if cross_result['success']:
                cross_data = cross_result  # Data is directly in the result
                print(f"✅ Cross-section ({direction}-direction) generated successfully")
                print(f"   - Vertices count: {len(cross_data.get('vertices_x', []))}")
                print(f"   - Direction: {cross_data.get('cross_section_direction', 'unknown')}")
                print(f"   - Position: {cross_data.get('cross_section_position', 0):.2f}")
            else:
                print(f"❌ Cross-section ({direction}) failed: {cross_result.get('error', 'Unknown error')}")
        
        print("\n✅ All backend 3D visualization tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Backend test error: {e}")
        traceback.print_exc()
        return False

def test_3d_visualization_gui():
    """Test 3D visualization GUI functionality"""
    print("\n🖥️ Testing 3D Visualization GUI...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.oxidation_panel import OxidationPanel
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create oxidation panel
        panel = OxidationPanel()
        
        # Check if 3D visualization tab exists
        tab_count = panel.tab_widget.count()
        tab_names = [panel.tab_widget.tabText(i) for i in range(tab_count)]
        
        print(f"✅ GUI initialization successful")
        print(f"   - Total tabs: {tab_count}")
        print(f"   - Tab names: {tab_names}")
        
        # Check for 3D visualization tab
        if "3D Visualization" in tab_names:
            viz_tab_index = tab_names.index("3D Visualization")
            print(f"✅ 3D Visualization tab found at index {viz_tab_index}")
            
            # Switch to 3D visualization tab
            panel.tab_widget.setCurrentIndex(viz_tab_index)
            
            # Check if required widgets exist
            required_widgets = [
                'viz_thickness_spin', 'viz_temperature_spin', 'viz_time_spin',
                'viz_resolution_spin', 'viz_type_combo', 'generate_3d_button',
                'viz_3d_canvas', 'viz_3d_results_text'
            ]
            
            missing_widgets = []
            for widget_name in required_widgets:
                if not hasattr(panel, widget_name):
                    missing_widgets.append(widget_name)
            
            if not missing_widgets:
                print("✅ All required 3D visualization widgets found")
                
                # Test widget functionality
                panel.viz_thickness_spin.setValue(75.0)
                panel.viz_temperature_spin.setValue(1050.0)
                panel.viz_time_spin.setValue(3.0)
                panel.viz_resolution_spin.setValue(40)
                
                print("✅ Widget parameter setting successful")
                
                # Test visualization type changes
                for viz_type in ["3D Surface View", "Cross-Section View", "Process Animation"]:
                    panel.viz_type_combo.setCurrentText(viz_type)
                    panel.on_viz_type_changed(viz_type)
                    print(f"✅ Visualization type '{viz_type}' set successfully")
                
            else:
                print(f"❌ Missing widgets: {missing_widgets}")
                return False
        else:
            print("❌ 3D Visualization tab not found")
            return False
        
        print("\n✅ All GUI 3D visualization tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ GUI test error: {e}")
        traceback.print_exc()
        return False

def test_3d_visualizer_class():
    """Test the 3D visualizer class directly"""
    print("\n🎨 Testing 3D Visualizer Class...")
    
    try:
        from oxidation.oxidation_3d_visualizer import Oxidation3DVisualizer
        
        # Create visualizer
        visualizer = Oxidation3DVisualizer()
        print("✅ 3D Visualizer class instantiated successfully")
        
        # Test with sample data
        sample_viz_data = {
            'thickness_map': [[50.0 * (1 - 0.1 * (i + j) / 20) for i in range(20)] for j in range(20)],
            'stress_map': [[200.0 * (1 + 0.5 * (i + j) / 20) for i in range(20)] for j in range(20)],
            'interface_quality_map': [[1.0 - 0.3 * (i + j) / 20 for i in range(20)] for j in range(20)],
            'min_thickness': 40.0,
            'max_thickness': 50.0,
            'min_stress': 200.0,
            'max_stress': 300.0
        }
        
        # Test matplotlib visualization
        print("\n📈 Testing matplotlib 3D visualization...")
        matplotlib_result = visualizer.create_matplotlib_3d_oxidation(sample_viz_data)
        
        if matplotlib_result['success']:
            print("✅ Matplotlib 3D visualization successful")
            print(f"   - Figure created: {matplotlib_result['figure'] is not None}")
            print(f"   - Surfaces count: {len(matplotlib_result.get('surfaces', []))}")
        else:
            print(f"❌ Matplotlib visualization failed: {matplotlib_result.get('error', 'Unknown error')}")
        
        # Test animation
        print("\n🎬 Testing process animation...")
        sample_frames = [
            {
                'time_stamp': i * 0.5,
                'thickness_snapshot': [[25.0 * (i + 1) / 10 * (1 - 0.1 * (j + k) / 20) for j in range(20)] for k in range(20)],
                'process_progress': (i + 1) * 10,
                'process_stage': f"Stage {i + 1}"
            }
            for i in range(10)
        ]
        
        animation_result = visualizer.create_process_animation(sample_frames)
        
        if animation_result['success']:
            print("✅ Process animation successful")
            print(f"   - Animation created: {animation_result['animation'] is not None}")
            print(f"   - Figure created: {animation_result['figure'] is not None}")
        else:
            print(f"❌ Process animation failed: {animation_result.get('error', 'Unknown error')}")
        
        print("\n✅ All 3D visualizer class tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ 3D Visualizer class test error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all 3D visualization tests"""
    print("🚀 Starting 3D Oxidation Visualization Tests")
    print("=" * 60)
    
    # Test results
    backend_success = test_3d_visualization_backend()
    gui_success = test_3d_visualization_gui()
    visualizer_success = test_3d_visualizer_class()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Backend Tests: {'✅ PASS' if backend_success else '❌ FAIL'}")
    print(f"   GUI Tests: {'✅ PASS' if gui_success else '❌ FAIL'}")
    print(f"   Visualizer Tests: {'✅ PASS' if visualizer_success else '❌ FAIL'}")
    
    overall_success = backend_success and gui_success and visualizer_success
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 3D Oxidation Visualization implementation is ready!")
        print("   - 3D surface visualization ✅")
        print("   - Cross-sectional views ✅")
        print("   - Stress distribution mapping ✅")
        print("   - Interface quality visualization ✅")
        print("   - Process animation ✅")
        print("   - GUI integration ✅")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
