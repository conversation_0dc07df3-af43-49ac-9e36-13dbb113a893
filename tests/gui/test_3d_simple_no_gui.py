#!/usr/bin/env python3
"""
Simple 3D Oxidation Test (No GUI)
=================================

Test 3D oxidation functionality without importing GUI components.

Author: Dr<PERSON>
"""

import sys
import os

# Add src/python to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_3d_oxidation_backend():
    """Test 3D oxidation backend functionality"""
    print("🧪 Testing 3D Oxidation Backend...")
    
    try:
        from oxidation import OxidationManager
        from geometry import GeometryManager
        
        # Initialize managers
        oxidation_manager = OxidationManager()
        geometry_manager = GeometryManager(wafer_diameter=200.0, wafer_thickness=0.5)
        
        # Set wafer
        wafer = geometry_manager.wafer
        oxidation_manager.set_wafer(wafer)
        
        print("✅ Managers initialized successfully")
        
        # Test 3D visualization
        print("🎨 Testing 3D visualization...")
        viz_result = oxidation_manager.generate_3d_visualization(
            thickness=50.0,
            temperature=1000.0,
            time=2.0,
            resolution=20
        )
        
        if viz_result and viz_result.get('success'):
            print("✅ 3D visualization working")
            viz_data = viz_result.get('visualization_data', {})
            print(f"   - Thickness map: {len(viz_data.get('thickness_map', []))} points")
            print(f"   - Stress map: {len(viz_data.get('stress_map', []))} points")
            print(f"   - Vertices: {len(viz_data.get('vertices_x', []))} points")
        else:
            print("❌ 3D visualization failed")
            return False
        
        # Test process animation
        print("🎬 Testing process animation...")
        anim_result = oxidation_manager.generate_process_animation(
            thickness=30.0,
            temperature=950.0,
            time=1.5,
            frames=10
        )
        
        if anim_result and anim_result.get('success'):
            print("✅ Process animation working")
            frames = anim_result.get('animation_frames', [])
            print(f"   - Animation frames: {len(frames)}")
        else:
            print("❌ Process animation failed")
            return False
        
        # Test cross-section view
        print("✂️ Testing cross-section view...")
        cross_result = oxidation_manager.generate_cross_section_view(
            thickness=40.0,
            temperature=1050.0,
            time=1.0,
            direction='x',
            position=0.5
        )
        
        if cross_result and cross_result.get('success'):
            print("✅ Cross-section view working")
            cross_data = cross_result.get('cross_section_data', {})
            print(f"   - Cross-section points: {len(cross_data.get('positions', []))}")
        else:
            print("❌ Cross-section view failed")
            return False
        
        print("🎉 All 3D oxidation backend tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in 3D oxidation backend test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 3D Oxidation Backend Test (No GUI)")
    print("=" * 50)
    
    success = test_3d_oxidation_backend()
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
