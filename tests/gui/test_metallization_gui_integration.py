#!/usr/bin/env python3
"""
Test Metallization GUI Integration
=================================

Test script to verify that the enhanced metallization module is properly
integrated into the main GUI and accessible through the interface.
"""

import sys
import os
from pathlib import Path

# Add the src/python directory to the path
current_dir = Path(__file__).parent
src_python_dir = current_dir / 'src' / 'python'
sys.path.insert(0, str(src_python_dir))

def test_metallization_gui_integration():
    """Test metallization GUI integration"""
    print("🔍 TESTING METALLIZATION GUI INTEGRATION")
    print("=" * 50)
    
    try:
        # Test 1: Import dependencies
        print("1. Testing dependencies...")
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("   ✓ PySide6 available")
        
        # Test 2: Import enhanced metallization components
        print("\n2. Testing enhanced metallization imports...")
        from enhanced_metallization_bridge import EnhancedMetallizationBridge
        from gui.enhanced_metallization_panel import EnhancedMetallizationPanel
        print("   ✓ Enhanced metallization components imported")
        
        # Test 3: Import main window
        print("\n3. Testing main window import...")
        from gui.enhanced_main_window import EnhancedMainWindow
        print("   ✓ Enhanced main window imported")
        
        # Test 4: Create main window and check metallization integration
        print("\n4. Testing main window metallization integration...")
        main_window = EnhancedMainWindow()
        
        # Check if metallization panel exists
        if hasattr(main_window, 'metallization_panel'):
            print("   ✓ Main window has metallization_panel")
            
            # Check if it's the enhanced version
            if isinstance(main_window.metallization_panel, EnhancedMetallizationPanel):
                print("   ✓ Using EnhancedMetallizationPanel")
            else:
                print(f"   ⚠ Using {type(main_window.metallization_panel).__name__}")
        else:
            print("   ✗ Main window missing metallization_panel")
            return False
        
        # Test 5: Check signal integration
        print("\n5. Testing signal integration...")
        panel = main_window.metallization_panel
        
        if hasattr(panel, 'metallization_updated'):
            print("   ✓ Panel has metallization_updated signal")
            
            # Check if signal is connected
            signal = panel.metallization_updated
            if signal.receivers() > 0:
                print("   ✓ Signal is connected to main window")
            else:
                print("   ⚠ Signal has no receivers")
        else:
            print("   ✗ Panel missing metallization_updated signal")
            return False
        
        # Test 6: Check tab integration
        print("\n6. Testing tab integration...")
        tab_widget = main_window.tab_widget
        metallization_tab_found = False
        
        for i in range(tab_widget.count()):
            tab_text = tab_widget.tabText(i)
            if "Metallization" in tab_text:
                metallization_tab_found = True
                widget = tab_widget.widget(i)
                if isinstance(widget, EnhancedMetallizationPanel):
                    print(f"   ✓ Found enhanced metallization tab: '{tab_text}'")
                else:
                    print(f"   ⚠ Found metallization tab but not enhanced: '{tab_text}'")
                break
        
        if not metallization_tab_found:
            print("   ✗ Metallization tab not found")
            return False
        
        # Test 7: Check device visualization integration
        print("\n7. Testing device visualization integration...")
        
        if hasattr(main_window, 'device_viz_panel'):
            print("   ✓ Main window has device visualization panel")
        else:
            print("   ⚠ Main window missing device visualization panel")
        
        if hasattr(main_window, '_adapt_metallization_results_for_device_viz'):
            print("   ✓ Main window has metallization adapter method")
        else:
            print("   ✗ Main window missing metallization adapter method")
            return False
        
        # Test 8: Test industrial applications integration
        print("\n8. Testing industrial applications integration...")
        
        if hasattr(panel, 'examples'):
            print("   ✓ Panel has industrial examples")
            
            # Check if examples are available
            examples = panel.examples
            if hasattr(examples, 'applications') and examples.applications:
                app_count = len(examples.applications)
                print(f"   ✓ {app_count} industrial applications available")
            else:
                print("   ⚠ No industrial applications found")
        else:
            print("   ✗ Panel missing industrial examples")
            return False
        
        # Test 9: Test simulation capability
        print("\n9. Testing simulation capability...")
        
        try:
            # Test a simple simulation
            bridge = EnhancedMetallizationBridge()
            result = bridge.simulate_metallization(
                material='Cu',
                thickness=100.0,
                method='PVD',
                device_name='Test Device'
            )
            
            if result and result.get('success', False):
                print("   ✓ Simulation capability working")
            else:
                print("   ⚠ Simulation returned no results")
        except Exception as e:
            print(f"   ⚠ Simulation test failed: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 METALLIZATION GUI INTEGRATION TEST PASSED!")
        print("\nThe enhanced metallization module is properly integrated!")
        print("\nTo use the integration:")
        print("1. Run: cd src/python && python gui/semipro_gui_launcher.py")
        print("2. Navigate to the '⚡ Metallization' tab")
        print("3. Select an industrial application from the dropdown")
        print("4. Click 'Run Simulation' to see results")
        print("5. Check the visualization tabs for device results")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Metallization GUI integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_metallization_gui_integration()
    sys.exit(0 if success else 1)
