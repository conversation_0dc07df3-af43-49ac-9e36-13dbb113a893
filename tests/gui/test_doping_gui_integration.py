#!/usr/bin/env python3
"""
Test script for doping module GUI integration
============================================

Tests the integration of the doping module with the enhanced simulator GUI.
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_doping_module_import():
    """Test if doping module can be imported"""
    print("\n🧪 Testing Doping Module Import...")
    
    try:
        from doping.doping_manager import DopingManager, ImplantationParameters, IonSpecies
        print("✅ DopingManager imported successfully")
        
        # Test basic functionality
        manager = DopingManager()
        print("✅ DopingManager created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Doping module import failed: {e}")
        return False

def test_gui_integration_without_qt():
    """Test GUI integration without actually creating Qt widgets"""
    print("\n🖥️ Testing GUI Integration (Import Only)...")
    
    try:
        # Test if the enhanced simulator GUI can find doping references
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python', 'gui'))
        
        # Read the GUI file to check for doping integration
        gui_file = Path('src/python/gui/enhanced_simulator_gui.py')
        if gui_file.exists():
            content = gui_file.read_text()
            
            # Check for key doping integrations
            checks = [
                ('doping module button', '"Doping"' in content),
                ('doping panel import', 'doping_panel' in content),
                ('doping process steps', 'Source/Drain' in content),
                ('doping parameters', 'dopant_type' in content),
                ('doping visualization', 'Ion Implantation' in content)
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {check_name}")
            
            all_passed = all(result for _, result in checks)
            if all_passed:
                print("✅ All GUI integration checks passed")
            else:
                print("⚠️  Some GUI integration checks failed")
            
            return all_passed
        else:
            print("❌ Enhanced simulator GUI file not found")
            return False
            
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        return False

def test_launch_script_integration():
    """Test launch script integration"""
    print("\n🚀 Testing Launch Script Integration...")
    
    try:
        launch_file = Path('launch_enhanced_semipro.py')
        if launch_file.exists():
            content = launch_file.read_text()
            
            # Check for doping-related imports or references
            has_doping_ref = 'doping' in content.lower()
            has_gui_import = 'enhanced_simulator_gui' in content
            
            print(f"   {'✅' if has_gui_import else '❌'} Enhanced GUI import found")
            print(f"   {'✅' if has_doping_ref else '⚠️ '} Doping references {'found' if has_doping_ref else 'not found (expected)'}")
            
            print("✅ Launch script integration verified")
            return True
        else:
            print("❌ Launch script not found")
            return False
            
    except Exception as e:
        print(f"❌ Launch script test failed: {e}")
        return False

def test_database_schema_integration():
    """Test database schema integration"""
    print("\n🗄️ Testing Database Schema Integration...")
    
    try:
        schema_files = [
            'database/schemas/doping_schema_extension.sql',
            'postgresql_schema_design.sql'
        ]
        
        found_schemas = []
        for schema_file in schema_files:
            if Path(schema_file).exists():
                found_schemas.append(schema_file)
                print(f"   ✅ Found schema: {schema_file}")
        
        if found_schemas:
            # Check main schema for doping module reference
            main_schema = Path('postgresql_schema_design.sql')
            if main_schema.exists():
                content = main_schema.read_text()
                has_doping_module = "'doping'" in content
                print(f"   {'✅' if has_doping_module else '❌'} Doping module in main schema")
            
            print("✅ Database schema integration verified")
            return True
        else:
            print("⚠️  No database schemas found")
            return False
            
    except Exception as e:
        print(f"❌ Database schema test failed: {e}")
        return False

def test_industrial_processes_integration():
    """Test industrial processes integration"""
    print("\n🏭 Testing Industrial Processes Integration...")
    
    try:
        from doping.doping_manager import DopingManager
        
        # Test if we can create a manager (even if industrial processes fail)
        manager = DopingManager()
        print("✅ DopingManager created (fallback mode)")
        
        # Test if industrial processes file exists
        industrial_file = Path('src/python/doping/industrial_processes.py')
        if industrial_file.exists():
            print("✅ Industrial processes file found")
            
            # Check file content for key industrial processes
            content = industrial_file.read_text()
            processes = ['MOSFET', 'FinFET', 'Power', 'MEMS']
            
            for process in processes:
                if process in content:
                    print(f"   ✅ {process} process definitions found")
                else:
                    print(f"   ⚠️  {process} process definitions not found")
            
            return True
        else:
            print("❌ Industrial processes file not found")
            return False
            
    except Exception as e:
        print(f"❌ Industrial processes test failed: {e}")
        return False

def test_module_completeness():
    """Test overall module completeness"""
    print("\n📋 Testing Module Completeness...")
    
    components = [
        ('C++ Backend', 'src/cpp/physics/enhanced_doping.cpp'),
        ('C++ Header', 'src/cpp/physics/enhanced_doping.hpp'),
        ('Cython Binding', 'src/cython/doping.pyx'),
        ('Cython Header', 'src/cython/doping.pxd'),
        ('Python Manager', 'src/python/doping/doping_manager.py'),
        ('Industrial Processes', 'src/python/doping/industrial_processes.py'),
        ('Database Integration', 'src/python/doping/database_integration.py'),
        ('GUI Panel', 'src/python/gui/doping_panel.py'),
        ('Database Schema', 'database/schemas/doping_schema_extension.sql')
    ]
    
    found_components = 0
    total_components = len(components)
    
    for name, path in components:
        if Path(path).exists():
            print(f"   ✅ {name}")
            found_components += 1
        else:
            print(f"   ❌ {name} (missing: {path})")
    
    completion_rate = (found_components / total_components) * 100
    print(f"\n📊 Module Completeness: {found_components}/{total_components} ({completion_rate:.1f}%)")
    
    if completion_rate >= 80:
        print("✅ Module is substantially complete")
        return True
    else:
        print("⚠️  Module needs more components")
        return False

def main():
    """Run all integration tests"""
    print("🧪 Doping Module GUI Integration Test Suite")
    print("=" * 60)
    
    # Configure logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise
    
    tests = [
        ("Doping Module Import", test_doping_module_import),
        ("GUI Integration", test_gui_integration_without_qt),
        ("Launch Script Integration", test_launch_script_integration),
        ("Database Schema Integration", test_database_schema_integration),
        ("Industrial Processes Integration", test_industrial_processes_integration),
        ("Module Completeness", test_module_completeness)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Integration Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed >= len(results) * 0.8:  # 80% pass rate
        print("🎉 Doping module integration is successful!")
        print("\n📝 Next Steps:")
        print("   1. Install Qt dependencies for full GUI testing")
        print("   2. Set up PostgreSQL for database functionality")
        print("   3. Compile Cython extensions for enhanced performance")
        print("   4. Run full end-to-end simulation tests")
        return 0
    else:
        print("⚠️  Integration needs improvement. Check failed tests above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
