#!/usr/bin/env python3
"""
Advanced Visualization Demo
============================

Comprehensive demonstration of advanced visualization capabilities
showcasing industrial device visualization, process flow rendering,
and performance analysis.

Author: Dr<PERSON>
"""

import sys
import os
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def demo_semiconductor_fabrication():
    """Demo semiconductor fabrication visualization"""
    print("Semiconductor Fabrication Visualization Demo")
    print("=" * 50)
    
    try:
        from enhanced_advanced_visualization_bridge import EnhancedAdvancedVisualizationBridge
        
        bridge = EnhancedAdvancedVisualizationBridge()
        print("Enhanced visualization bridge initialized")
        
        # Create semiconductor fab system
        result = bridge.create_industrial_visualization("semiconductor_fab", "CMOS")
        if not result.get('success', False):
            print(f"Failed to create system: {result.get('error', 'Unknown')}")
            return False
        
        print(f"Created semiconductor fabrication system:")
        print(f"  Device type: CMOS Logic")
        print(f"  Available presets: {len(result.get('available_presets', []))}")
        print(f"  Creation time: {result.get('creation_time_s', 0):.3f}s")
        
        # Load fab process preset
        preset_result = bridge.load_visualization_preset("fab_process")
        if preset_result.get('success', False):
            print("  Loaded fab process visualization preset")
        
        # Render device structure
        device_result = bridge.render_device_structure("CMOS")
        if 'error' not in device_result:
            print(f"Device structure rendered:")
            print(f"  Frame rate: {device_result.get('frame_rate', 0):.1f} FPS")
            print(f"  Triangle count: {device_result.get('triangle_count', 0):,}")
            print(f"  Memory usage: {device_result.get('memory_usage_mb', 0):.1f} MB")
        
        # Render process flow
        process_steps = [
            "substrate_preparation", "well_formation", "gate_oxidation",
            "polysilicon_deposition", "gate_patterning", "source_drain_implant",
            "contact_formation", "metal1_deposition", "via_formation", "metal2_deposition"
        ]
        
        process_result = bridge.render_process_flow(process_steps)
        if 'error' not in process_result:
            print(f"Process flow rendered: {len(process_steps)} steps")
            print("  Process steps:")
            for i, step in enumerate(process_steps[:5]):  # Show first 5
                print(f"    {i+1}. {step.replace('_', ' ').title()}")
            if len(process_steps) > 5:
                print(f"    ... and {len(process_steps) - 5} more steps")
        
        # Performance metrics
        metrics = bridge.get_visualization_metrics()
        if 'error' not in metrics:
            viz_metrics = metrics.get('metrics', {})
            print(f"Visualization metrics:")
            print(f"  Application: {metrics.get('application', 'Unknown')}")
            print(f"  Frame rate: {viz_metrics.get('frame_rate', 0):.1f} FPS")
            print(f"  Memory usage: {viz_metrics.get('memory_usage_mb', 0):.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"Semiconductor fabrication demo failed: {e}")
        return False

def demo_mems_device_visualization():
    """Demo MEMS device visualization"""
    print("\nMEMS Device Visualization Demo")
    print("=" * 50)
    
    try:
        from enhanced_advanced_visualization_bridge import EnhancedAdvancedVisualizationBridge
        
        bridge = EnhancedAdvancedVisualizationBridge()
        
        # Create MEMS device system
        result = bridge.create_industrial_visualization("mems_device", "Accelerometer")
        if not result.get('success', False):
            print(f"Failed to create MEMS system: {result.get('error', 'Unknown')}")
            return False
        
        print(f"Created MEMS accelerometer system:")
        print(f"  Device type: 3-axis capacitive accelerometer")
        print(f"  Technology: Surface micromachining")
        
        # Load MEMS structure preset
        preset_result = bridge.load_visualization_preset("mems_structure")
        
        # Render device structure
        device_result = bridge.render_device_structure("Accelerometer")
        if 'error' not in device_result:
            print(f"MEMS device structure rendered:")
            print(f"  Performance: {device_result.get('frame_rate', 0):.1f} FPS")
        
        # Generate and render defect analysis
        defect_data = [
            [10.5, 20.3, 0.1],  # x, y, severity
            [15.2, 35.7, 0.3],
            [22.1, 18.9, 0.2],
            [8.7, 42.1, 0.4],
            [30.1, 25.6, 0.2]
        ]
        
        defect_result = bridge.render_defect_analysis(defect_data)
        if 'error' not in defect_result:
            print(f"Defect analysis rendered:")
            print(f"  Defects analyzed: {len(defect_data)}")
            print(f"  Critical defects: {sum(1 for d in defect_data if d[2] > 0.3)}")
            print(f"  Average severity: {sum(d[2] for d in defect_data) / len(defect_data):.2f}")
        
        return True
        
    except Exception as e:
        print(f"MEMS device demo failed: {e}")
        return False

def demo_power_device_analysis():
    """Demo power device analysis"""
    print("\nPower Device Analysis Demo")
    print("=" * 50)
    
    try:
        from enhanced_advanced_visualization_bridge import EnhancedAdvancedVisualizationBridge
        
        bridge = EnhancedAdvancedVisualizationBridge()
        
        # Create power device system
        result = bridge.create_industrial_visualization("power_device", "IGBT")
        if not result.get('success', False):
            print(f"Failed to create power device system: {result.get('error', 'Unknown')}")
            return False
        
        print(f"Created power IGBT system:")
        print(f"  Device type: 1200V/300A IGBT module")
        print(f"  Technology: Trench gate structure")
        
        # Load power analysis preset
        preset_result = bridge.load_visualization_preset("power_analysis")
        
        # Render device structure
        device_result = bridge.render_device_structure("IGBT")
        if 'error' not in device_result:
            print(f"Power device structure rendered:")
            print(f"  Rendering performance: {device_result.get('frame_rate', 0):.1f} FPS")
        
        # Performance benchmark
        benchmark_result = bridge.benchmark_visualization_performance(iterations=3)
        if 'error' not in benchmark_result:
            stats = benchmark_result.get('statistics', {})
            print(f"Performance benchmark results:")
            print(f"  Iterations: {benchmark_result.get('iterations', 0)}")
            print(f"  Average render time: {stats.get('avg_render_time_s', 0):.3f}s")
            print(f"  Performance score: {stats.get('performance_score', 0):.1f}")
        
        return True
        
    except Exception as e:
        print(f"Power device demo failed: {e}")
        return False

def demo_industrial_applications_overview():
    """Demo overview of all industrial applications"""
    print("\nIndustrial Applications Overview")
    print("=" * 50)
    
    try:
        from enhanced_advanced_visualization_bridge import EnhancedAdvancedVisualizationBridge
        
        bridge = EnhancedAdvancedVisualizationBridge()
        
        # Get all industrial applications
        applications = bridge.get_industrial_applications()
        print(f"Available industrial applications: {len(applications)}")
        
        for i, app in enumerate(applications, 1):
            print(f"\n{i}. {app['display_name']}")
            print(f"   Description: {app['description']}")
            print(f"   Device types: {', '.join(app['device_types'])}")
            print(f"   Key features: {', '.join(app['key_features'])}")
            print(f"   Typical layers: {app['typical_layers']}")
        
        # Test system creation for each application
        print(f"\nTesting system creation for all applications:")
        successful_systems = 0
        
        for app in applications:
            result = bridge.create_industrial_visualization(app['name'], 'default')
            if result.get('success', False):
                successful_systems += 1
                print(f"  ✓ {app['display_name']}: Created successfully")
            else:
                print(f"  ✗ {app['display_name']}: Failed")
        
        success_rate = (successful_systems / len(applications)) * 100
        print(f"\nSystem creation success rate: {success_rate:.1f}%")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"Industrial applications overview failed: {e}")
        return False

def demo_visualization_examples():
    """Demo industrial visualization examples"""
    print("\nIndustrial Visualization Examples Demo")
    print("=" * 50)
    
    try:
        from industrial_visualization_examples import IndustrialVisualizationExamples
        
        examples = IndustrialVisualizationExamples()
        
        # Run all examples
        results = examples.run_all_examples()
        
        print(f"Industrial visualization examples results:")
        print(f"  Total examples: {results.get('total_examples', 0)}")
        print(f"  Successful examples: {results.get('successful_examples', 0)}")
        print(f"  Success rate: {results.get('success_rate', 0):.1f}%")
        print(f"  Total execution time: {results.get('total_execution_time_s', 0):.3f}s")
        
        # Show performance data
        perf_data = results.get('performance_data', {})
        if perf_data:
            print(f"  Average execution time: {perf_data.get('avg_execution_time_s', 0):.3f}s")
        
        # Show individual results
        individual_results = results.get('results', {})
        print(f"\nIndividual example results:")
        
        for app_name, result in individual_results.items():
            if result.get('success', False):
                spec = result.get('specification', {})
                perf = result.get('performance_assessment', {})
                print(f"  ✓ {app_name}: {spec.get('application', 'Unknown')} - {perf.get('performance_level', 'Unknown')}")
            else:
                error = result.get('error', 'Unknown error')
                print(f"  ✗ {app_name}: Failed - {error}")
        
        return results.get('success_rate', 0) >= 70
        
    except Exception as e:
        print(f"Visualization examples demo failed: {e}")
        return False

def main():
    """Run comprehensive advanced visualization demo"""
    print("Advanced Visualization Comprehensive Demo")
    print("=" * 60)
    print("Showcasing industrial device visualization capabilities")
    print("=" * 60)
    
    start_time = time.time()
    
    demos = [
        ("Semiconductor Fabrication", demo_semiconductor_fabrication),
        ("MEMS Device Visualization", demo_mems_device_visualization),
        ("Power Device Analysis", demo_power_device_analysis),
        ("Industrial Applications Overview", demo_industrial_applications_overview),
        ("Visualization Examples", demo_visualization_examples)
    ]
    
    successful_demos = 0
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                print(f"\n✓ SUCCESS: {demo_name}")
                successful_demos += 1
            else:
                print(f"\n✗ FAILED: {demo_name}")
        except Exception as e:
            print(f"\n✗ ERROR: {demo_name} - {e}")
    
    total_time = time.time() - start_time
    success_rate = (successful_demos / len(demos)) * 100
    
    print("\n" + "=" * 60)
    print("Advanced Visualization Demo Summary")
    print("=" * 60)
    print(f"Demos run: {len(demos)}")
    print(f"Successful: {successful_demos}")
    print(f"Failed: {len(demos) - successful_demos}")
    print(f"Success rate: {success_rate:.1f}%")
    print(f"Total time: {total_time:.3f}s")
    
    print(f"\nAdvanced Visualization Status:")
    if success_rate >= 90:
        print("EXCELLENT - All advanced visualization capabilities working perfectly")
        print("Ready for production deployment with full industrial support")
    elif success_rate >= 80:
        print("GOOD - Advanced visualization working well with minor issues")
        print("Core industrial applications functional and ready for use")
    elif success_rate >= 60:
        print("FAIR - Advanced visualization partially working")
        print("Basic functionality available, some features need attention")
    else:
        print("POOR - Advanced visualization has significant issues")
        print("Major components need fixes before deployment")
    
    print(f"\nKey Capabilities Demonstrated:")
    print("- 7 industrial semiconductor and device applications")
    print("- Device structure visualization with authentic geometries")
    print("- Process flow rendering with step-by-step visualization")
    print("- Defect analysis with severity mapping and statistics")
    print("- Real-time performance monitoring and benchmarking")
    print("- Industrial-grade specifications and assessments")
    print("- Complete C++ to GUI integration pipeline")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
