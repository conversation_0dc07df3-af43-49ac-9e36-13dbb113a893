#!/usr/bin/env python3
"""
Simple GUI Test
===============

Test if PySide6 GUI can be displayed properly.
"""

import sys
import os

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont

    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("SemiPRO GUI Test")
            self.setGeometry(100, 100, 400, 300)

            central_widget = QWidget()
            self.setCentralWidget(central_widget)

            layout = QVBoxLayout(central_widget)

            title = QLabel("🚀 SemiPRO GUI Test")
            title.setAlignment(Qt.AlignCenter)
            font = QFont()
            font.setPointSize(18)
            font.setBold(True)
            title.setFont(font)
            layout.addWidget(title)

            status = QLabel("✅ PySide6 is working correctly!")
            status.setAlignment(Qt.AlignCenter)
            layout.addWidget(status)

            button = QPushButton("Close Test")
            button.clicked.connect(self.close)
            layout.addWidget(button)

    def main():
        app = QApplication(sys.argv)
        window = TestWindow()
        window.show()

        print("🖥️  GUI Test Window should be visible now!")
        print("📋 If you can see the window, PySide6 is working correctly.")
        print("❌ If no window appears, there may be a display issue.")

        return app.exec()

    if __name__ == "__main__":
        sys.exit(main())

except ImportError as e:
    print(f"❌ PySide6 import failed: {e}")
    print("📦 Install with: pip install PySide6")
    sys.exit(1)