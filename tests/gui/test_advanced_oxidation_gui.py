#!/usr/bin/env python3
"""
Test script for advanced oxidation GUI functionality
"""

import sys
import os
sys.path.append('src/python')

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from oxidation import OxidationManager
from gui.oxidation_panel import OxidationPanel

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SemiPRO - Advanced Oxidation Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create oxidation panel (it creates its own oxidation manager)
        self.oxidation_panel = OxidationPanel()

        # Get the oxidation manager from the panel
        self.oxidation_manager = self.oxidation_panel.oxidation_manager

        # Create dummy wafer if manager is available
        if self.oxidation_manager:
            wafer = self.oxidation_manager._create_dummy_wafer()
            self.oxidation_manager.set_wafer(wafer)
        
        layout.addWidget(self.oxidation_panel)
        
        print("Advanced Oxidation GUI Test Window created successfully!")
        if self.oxidation_manager and hasattr(self.oxidation_manager, 'get_advanced_recipes'):
            print("Available advanced recipes:", list(self.oxidation_manager.get_advanced_recipes().keys()))
        else:
            print("Advanced recipes not available (using fallback implementation)")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    try:
        window = TestWindow()
        window.show()
        
        print("GUI launched successfully!")
        print("You can now test the Advanced Industrial Examples tab in the oxidation panel.")
        print("Available processes:")
        print("1. SONOS Memory Oxide")
        print("2. High-k Gate Dielectric (HfO2)")
        print("3. MEMS Sacrificial Oxide")
        print("4. Power Device Gate Oxide")
        print("5. RF Device Isolation")
        print("6. Sensor Passivation")
        print("7. FinFET Gate Oxide")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error launching GUI: {e}")
        import traceback
        traceback.print_exc()
