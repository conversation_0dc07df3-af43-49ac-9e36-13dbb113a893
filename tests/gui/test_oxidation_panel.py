#!/usr/bin/env python3
"""
Simple test script for the oxidation panel visualization fixes
"""

import sys
import os

# Add the src/python directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
    from PyQt5.QtCore import Qt
    
    # Import the oxidation panel
    from gui.oxidation_panel import OxidationPanel
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Oxidation Panel Test")
            self.setGeometry(100, 100, 1400, 900)
            
            # Create central widget
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # Create layout
            layout = QVBoxLayout(central_widget)
            
            # Create oxidation panel
            self.oxidation_panel = OxidationPanel()
            layout.addWidget(self.oxidation_panel)
    
    def main():
        app = QApplication(sys.argv)
        
        # Create and show the test window
        window = TestWindow()
        window.show()
        
        print("✅ Oxidation Panel Test Window Created Successfully!")
        print("🔧 Test the following features:")
        print("   1. Switch between control tabs on the left")
        print("   2. Verify visualization tabs switch automatically on the right")
        print("   3. Run processes and check if results appear on the right visualization canvas")
        print("   4. Test 'Show Logs', 'Analytics', and 'Database' buttons")
        print("   5. Verify industrial examples are loaded and functional")
        
        # Run the application
        sys.exit(app.exec_())
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Please ensure PyQt5 is installed and the oxidation module is available")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
