#!/usr/bin/env python3
"""
Test script for Enhanced Lithography GUI
"""

import sys
import os

# Add src/python to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

from PySide6.QtWidgets import QApplication, QMainWindow, QTabWidget
from gui.enhanced_lithography_panel import EnhancedLithographyPanel

def main():
    """Test the enhanced lithography GUI"""
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create main window
    main_window = QMainWindow()
    main_window.setWindowTitle("SemiPRO - Enhanced Lithography Module Test")
    main_window.setGeometry(100, 100, 1200, 800)
    
    # Create tab widget
    tab_widget = QTabWidget()
    main_window.setCentralWidget(tab_widget)
    
    # Create lithography panel
    try:
        lithography_panel = EnhancedLithographyPanel()
        tab_widget.addTab(lithography_panel, "Enhanced Lithography")
        print("Enhanced Lithography panel created successfully")
        
        # Test the manager
        if lithography_panel.lithography_manager:
            processes = lithography_panel.lithography_manager.get_available_processes()
            print(f"Available processes: {processes}")
            
            equipment = lithography_panel.lithography_manager.physics_engine.get_available_equipment()
            print(f"Available equipment: {equipment}")
        else:
            print("Lithography manager not available")
            
    except Exception as e:
        print(f"Error creating lithography panel: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Show window
    main_window.show()
    
    print("GUI started successfully. Close the window to exit.")
    
    # Run application
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
