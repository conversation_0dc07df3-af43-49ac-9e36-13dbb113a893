#!/usr/bin/env python3
"""
Enhanced GUI Smoke Test
=======================

Lightweight smoke test for the Enhanced SemiPRO GUI system.
Tests basic functionality without requiring full GUI interaction.

Author: Dr<PERSON>
"""

import sys
import os
import unittest
import tempfile
import time
from unittest.mock import Mock, patch

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestEnhancedGUISmoke(unittest.TestCase):
    """Smoke tests for Enhanced GUI components"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_start_time = time.time()
        
    def tearDown(self):
        """Clean up after test"""
        test_time = time.time() - self.test_start_time
        print(f"  Test completed in {test_time:.2f}s")
    
    def test_enhanced_simulator_gui_import(self):
        """Test that Enhanced Simulator GUI can be imported"""
        print("\n🔍 Testing Enhanced Simulator GUI import...")
        try:
            from src.python.gui.enhanced_simulator_gui import EnhancedSimulatorGUI
            print("  ✅ Enhanced Simulator GUI imported successfully")
            return True
        except ImportError as e:
            print(f"  ❌ Failed to import Enhanced Simulator GUI: {e}")
            return False
    
    def test_orchestrator_bridge_import(self):
        """Test that Enhanced Orchestrator Bridge can be imported"""
        print("\n🔍 Testing Enhanced Orchestrator Bridge import...")
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            print("  ✅ Enhanced Orchestrator Bridge imported successfully")
            return True
        except ImportError as e:
            print(f"  ❌ Failed to import Enhanced Orchestrator Bridge: {e}")
            return False
    
    def test_orchestrator_bridge_initialization(self):
        """Test Enhanced Orchestrator Bridge initialization"""
        print("\n🔍 Testing Enhanced Orchestrator Bridge initialization...")
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            print("  ✅ Enhanced Orchestrator Bridge initialized")
            
            # Test basic functionality
            applications = bridge.get_industrial_applications()
            print(f"  ✅ Found {len(applications)} industrial applications")
            
            modules = bridge.get_available_modules()
            print(f"  ✅ Found {len(modules)} available modules")
            
            return True
        except Exception as e:
            print(f"  ❌ Failed to initialize Enhanced Orchestrator Bridge: {e}")
            return False
    
    def test_global_log_window(self):
        """Test Global Log Window functionality"""
        print("\n🔍 Testing Global Log Window...")
        try:
            # Test that the class can be imported and has expected methods
            from src.python.gui.enhanced_simulator_gui import GlobalLogWindow

            # Check that the class has expected methods
            expected_methods = ['add_log_entry', 'clear_log', 'export_log']
            for method in expected_methods:
                if hasattr(GlobalLogWindow, method):
                    print(f"  ✅ GlobalLogWindow has {method} method")
                else:
                    print(f"  ⚠️  GlobalLogWindow missing {method} method")

            print("  ✅ Global Log Window class structure verified")
            return True
        except Exception as e:
            print(f"  ❌ Failed to test Global Log Window: {e}")
            return False
    
    def test_status_broadcaster(self):
        """Test Status Broadcaster functionality"""
        print("\n🔍 Testing Status Broadcaster...")
        try:
            # Test that the class can be imported and has expected methods
            from src.python.gui.enhanced_simulator_gui import StatusBroadcaster

            # Check that the class has expected methods
            expected_methods = ['broadcast_status', 'broadcast_progress']
            for method in expected_methods:
                if hasattr(StatusBroadcaster, method):
                    print(f"  ✅ StatusBroadcaster has {method} method")
                else:
                    print(f"  ⚠️  StatusBroadcaster missing {method} method")

            print("  ✅ Status Broadcaster class structure verified")
            return True
        except Exception as e:
            print(f"  ❌ Failed to test Status Broadcaster: {e}")
            return False
    
    def test_module_panels_import(self):
        """Test that module panels can be imported"""
        print("\n🔍 Testing module panels import...")
        
        panels = [
            ('deposition_panel', 'DepositionPanel'),
            ('thermal_panel', 'ThermalPanel'),
            ('cmp_panel', 'CMPPanel'),
            ('inspection_panel', 'InspectionPanel')
        ]
        
        success_count = 0
        for module_name, class_name in panels:
            try:
                module = __import__(f'src.python.gui.{module_name}', fromlist=[class_name])
                panel_class = getattr(module, class_name)
                print(f"  ✅ {class_name} imported successfully")
                success_count += 1
            except Exception as e:
                print(f"  ❌ Failed to import {class_name}: {e}")
        
        print(f"  📊 Successfully imported {success_count}/{len(panels)} module panels")
        return success_count == len(panels)
    
    def test_industrial_applications(self):
        """Test industrial applications functionality"""
        print("\n🔍 Testing industrial applications...")
        try:
            from src.python.enhanced_orchestrator_bridge import EnhancedOrchestratorBridge
            
            bridge = EnhancedOrchestratorBridge()
            applications = bridge.get_industrial_applications()
            
            print(f"  ✅ Found {len(applications)} industrial applications")
            
            # Test application structure
            for i, app in enumerate(applications[:3]):  # Test first 3 applications
                required_fields = ['id', 'name', 'display_name', 'technology_node']
                missing_fields = [field for field in required_fields if field not in app]
                
                if missing_fields:
                    print(f"  ❌ Application {i} missing fields: {missing_fields}")
                    return False
                else:
                    print(f"  ✅ Application '{app['display_name']}' structure valid")
            
            return True
        except Exception as e:
            print(f"  ❌ Failed to test industrial applications: {e}")
            return False
    
    def test_gui_headless_initialization(self):
        """Test GUI initialization in headless mode"""
        print("\n🔍 Testing GUI headless initialization...")
        try:
            # Test that the GUI class can be imported and has expected attributes
            from src.python.gui.enhanced_simulator_gui import EnhancedSimulatorGUI

            # Check that the class has expected attributes and methods
            expected_attributes = ['module_windows', 'global_log', 'status_broadcaster']
            expected_methods = ['setup_ui', 'open_module_window', 'run_process_flow']

            for attr in expected_attributes:
                if hasattr(EnhancedSimulatorGUI, attr):
                    print(f"  ✅ EnhancedSimulatorGUI has {attr} attribute")
                else:
                    print(f"  ⚠️  EnhancedSimulatorGUI missing {attr} attribute")

            for method in expected_methods:
                if hasattr(EnhancedSimulatorGUI, method):
                    print(f"  ✅ EnhancedSimulatorGUI has {method} method")
                else:
                    print(f"  ⚠️  EnhancedSimulatorGUI missing {method} method")

            print("  ✅ GUI class structure verified")
            return True
        except Exception as e:
            print(f"  ❌ Failed to test GUI headless initialization: {e}")
            return False
    
    def test_logging_aggregation(self):
        """Test logging aggregation functionality"""
        print("\n🔍 Testing logging aggregation...")
        try:
            # Test that both classes can be imported
            from src.python.gui.enhanced_simulator_gui import GlobalLogWindow, StatusBroadcaster

            # Verify class structures
            log_methods = ['add_log_entry', 'clear_log']
            broadcaster_methods = ['broadcast_status', 'broadcast_progress']

            for method in log_methods:
                if hasattr(GlobalLogWindow, method):
                    print(f"  ✅ GlobalLogWindow has {method}")

            for method in broadcaster_methods:
                if hasattr(StatusBroadcaster, method):
                    print(f"  ✅ StatusBroadcaster has {method}")

            print("  ✅ Logging aggregation classes verified")
            return True
        except Exception as e:
            print(f"  ❌ Failed to test logging aggregation: {e}")
            return False

def run_smoke_tests():
    """Run all smoke tests"""
    print("🚀 Starting Enhanced SemiPRO GUI Smoke Tests")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedGUISmoke)
    
    # Run tests with custom result handler
    class SmokeTestResult(unittest.TextTestResult):
        def __init__(self, stream, descriptions, verbosity):
            super().__init__(stream, descriptions, verbosity)
            self.success_count = 0
            self.total_count = 0
        
        def addSuccess(self, test):
            super().addSuccess(test)
            self.success_count += 1
            self.total_count += 1
        
        def addError(self, test, err):
            super().addError(test, err)
            self.total_count += 1
        
        def addFailure(self, test, err):
            super().addFailure(test, err)
            self.total_count += 1
    
    # Run tests
    runner = unittest.TextTestRunner(resultclass=SmokeTestResult, verbosity=0)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 Smoke Test Summary:")
    print(f"  ✅ Passed: {result.success_count}")
    print(f"  ❌ Failed: {len(result.failures)}")
    print(f"  💥 Errors: {len(result.errors)}")
    print(f"  📈 Success Rate: {result.success_count/result.total_count*100:.1f}%")
    
    if result.success_count == result.total_count:
        print("\n🎉 All smoke tests passed! Enhanced GUI system is ready.")
        return True
    else:
        print("\n⚠️  Some smoke tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = run_smoke_tests()
    sys.exit(0 if success else 1)
