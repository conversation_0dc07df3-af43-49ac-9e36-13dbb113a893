"""
Thermal Module Test Suite
========================

Comprehensive test suite for thermal module including unit tests,
integration tests, and industrial application validation.

Author: Dr<PERSON>
"""

import unittest
import numpy as np
import tempfile
import os
import sys
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from python.thermal.database_integration import ThermalDatabaseIntegration
    from python.thermal.thermal_visualization import ThermalVisualizationEngine
    from python.thermal.industrial_applications import IndustrialThermalApplications, IndustrialApplicationType
    from python.semipro_packaging.enhanced_thermal_analysis import EnhancedThermalAnalysisEngine, DatabaseIntegratedThermalEngine
    THERMAL_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Thermal modules not available for testing: {e}")
    THERMAL_MODULES_AVAILABLE = False

class TestThermalDatabaseIntegration(unittest.TestCase):
    """Test thermal database integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
        
        # Mock database manager
        self.mock_db = Mock()
        self.mock_cursor = Mock()
        self.mock_db.get_cursor.return_value.__enter__.return_value = self.mock_cursor
        
        with patch('python.thermal.database_integration.get_database_manager', return_value=self.mock_db):
            self.db_integration = ThermalDatabaseIntegration()
    
    def test_add_thermal_material(self):
        """Test adding thermal material to database"""
        material_data = {
            'material_name': 'Test Silicon',
            'chemical_formula': 'Si',
            'material_class': 'semiconductor',
            'thermal_conductivity_w_mk': 150.0,
            'specific_heat_j_kg_k': 700.0,
            'density_kg_m3': 2330.0,
            'thermal_expansion_per_k': 2.6e-6,
            'temperature_range_c': [25.0, 1400.0]
        }
        
        # Mock cursor return value
        self.mock_cursor.fetchone.return_value = {'material_id': 'test-uuid'}
        
        result = self.db_integration.add_thermal_material(material_data)
        
        self.assertEqual(result, 'test-uuid')
        self.mock_cursor.execute.assert_called_once()
    
    def test_get_thermal_material(self):
        """Test retrieving thermal material from database"""
        # Mock cursor return value
        mock_material = {
            'material_name': 'Silicon',
            'thermal_conductivity_w_mk': 150.0,
            'specific_heat_j_kg_k': 700.0
        }
        self.mock_cursor.fetchone.return_value = mock_material
        
        result = self.db_integration.get_thermal_material('Silicon')
        
        self.assertEqual(result, mock_material)
        self.mock_cursor.execute.assert_called_with(
            "SELECT * FROM thermal.thermal_materials WHERE material_name = %s",
            ('Silicon',)
        )
    
    def test_interpolate_material_properties(self):
        """Test material property interpolation"""
        # Mock material with temperature-dependent properties
        mock_material = {
            'material_name': 'Silicon',
            'thermal_conductivity_w_mk': 150.0,
            'thermal_conductivity_vs_temp': '[{"temp_c": 25, "value": 150}, {"temp_c": 100, "value": 120}]',
            'temperature_range_c': [25.0, 1400.0]
        }
        self.mock_cursor.fetchone.return_value = mock_material
        
        result = self.db_integration.interpolate_material_properties('Silicon', 50.0)
        
        self.assertIsNotNone(result)
        # Should interpolate between 150 and 120 at 50°C
        expected_conductivity = 150 - (150 - 120) * (50 - 25) / (100 - 25)
        self.assertAlmostEqual(result['thermal_conductivity_w_mk'], expected_conductivity, places=1)
    
    def test_create_thermal_process(self):
        """Test creating thermal process record"""
        process_data = {
            'process_name': 'Test RTP',
            'process_type': 'RTP',
            'target_temperature_c': 1000.0,
            'hold_time_seconds': 30,
            'atmosphere_type': 'N2'
        }
        
        result = self.db_integration.create_thermal_process(process_data)
        
        self.assertIsInstance(result, str)  # Should return UUID string
        self.mock_cursor.execute.assert_called_once()

class TestThermalVisualizationEngine(unittest.TestCase):
    """Test thermal visualization engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
        
        self.viz_engine = ThermalVisualizationEngine()
    
    def test_create_custom_colormaps(self):
        """Test custom colormap creation"""
        colormaps = self.viz_engine._create_custom_colormaps()
        
        self.assertIn('thermal', colormaps)
        self.assertIn('hotspot', colormaps)
        self.assertIn('gradient', colormaps)
    
    def test_create_2d_temperature_map(self):
        """Test 2D temperature map creation"""
        # Create test temperature data
        temperature_data = np.random.rand(50, 50) * 100 + 298.15  # 298-398K
        
        hotspots = [
            {'x': 0.3, 'y': 0.3, 'temperature': 350.0},
            {'x': 0.7, 'y': 0.7, 'temperature': 340.0}
        ]
        
        fig = self.viz_engine.create_2d_temperature_map(
            temperature_data, 
            title="Test Temperature Map",
            hotspots=hotspots
        )
        
        self.assertIsNotNone(fig)
        self.assertEqual(len(fig.axes), 1)
    
    def test_create_3d_temperature_surface(self):
        """Test 3D temperature surface creation"""
        # Create test temperature data
        temperature_data = np.random.rand(30, 30) * 50 + 298.15
        
        fig = self.viz_engine.create_3d_temperature_surface(
            temperature_data,
            title="Test 3D Surface"
        )
        
        self.assertIsNotNone(fig)
        self.assertEqual(len(fig.axes), 1)
    
    def test_create_device_cross_section(self):
        """Test device cross-section visualization"""
        device_data = {
            'width_um': 100.0,
            'height_um': 50.0,
            'layers': [
                {'thickness_um': 10.0, 'material': 'Silicon'},
                {'thickness_um': 5.0, 'material': 'SiO2'},
                {'thickness_um': 2.0, 'material': 'Metal'}
            ],
            'features': [
                {'type': 'contact', 'position': [10, 15], 'size': [5, 2]},
                {'type': 'gate', 'position': [50, 10], 'size': [8, 3]}
            ]
        }
        
        temperature_profile = np.random.rand(20, 40) * 50 + 298.15
        
        fig = self.viz_engine.create_device_cross_section(
            device_data,
            temperature_profile,
            title="Test Device Cross-Section"
        )
        
        self.assertIsNotNone(fig)
        self.assertEqual(len(fig.axes), 1)
    
    def test_create_wafer_thermal_map(self):
        """Test wafer thermal map creation"""
        wafer_data = {
            'diameter_mm': 300.0,
            'die_size_mm': [10.0, 10.0],
            'exclusion_edge_mm': 3.0
        }
        
        temperature_data = np.random.rand(30, 30) * 30 + 298.15
        
        fig = self.viz_engine.create_wafer_thermal_map(
            wafer_data,
            temperature_data,
            title="Test Wafer Map"
        )
        
        self.assertIsNotNone(fig)
        self.assertEqual(len(fig.axes), 1)

class TestIndustrialThermalApplications(unittest.TestCase):
    """Test industrial thermal applications"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
        
        self.industrial_apps = IndustrialThermalApplications()
    
    def test_get_application_by_type(self):
        """Test getting applications by type"""
        cpu_apps = self.industrial_apps.get_application_by_type(IndustrialApplicationType.CPU_COOLING)
        
        self.assertIsInstance(cpu_apps, list)
        self.assertGreater(len(cpu_apps), 0)
        
        # Check first application structure
        app = cpu_apps[0]
        self.assertIn('name', app)
        self.assertIn('specifications', app)
        self.assertIn('thermal_characteristics', app)
        self.assertIn('cooling_solutions', app)
    
    def test_get_application_by_name(self):
        """Test getting specific application by name"""
        app = self.industrial_apps.get_application_by_name("Intel Core i9-13900K Desktop CPU")
        
        self.assertIsNotNone(app)
        self.assertEqual(app['name'], "Intel Core i9-13900K Desktop CPU")
        self.assertEqual(app['manufacturer'], "Intel")
    
    def test_calculate_thermal_performance(self):
        """Test thermal performance calculation"""
        app = self.industrial_apps.get_application_by_name("Intel Core i9-13900K Desktop CPU")
        
        performance = self.industrial_apps.calculate_thermal_performance(
            app, 
            cooling_solution="liquid_cooler_240mm",
            ambient_temp=25.0
        )
        
        self.assertIn('junction_temperature', performance)
        self.assertIn('thermal_margin', performance)
        self.assertIn('meets_requirements', performance)
        self.assertIn('hotspots', performance)
        
        # Check reasonable values
        self.assertGreater(performance['junction_temperature'], 25.0)
        self.assertLess(performance['junction_temperature'], 200.0)
    
    def test_generate_thermal_report(self):
        """Test thermal report generation"""
        report = self.industrial_apps.generate_thermal_report(
            "Intel Core i9-13900K Desktop CPU",
            "liquid_cooler_240mm",
            ambient_temp=25.0
        )
        
        self.assertIsInstance(report, str)
        self.assertIn("THERMAL ANALYSIS REPORT", report)
        self.assertIn("Intel Core i9-13900K Desktop CPU", report)
        self.assertIn("liquid_cooler_240mm", report)

class TestEnhancedThermalAnalysisEngine(unittest.TestCase):
    """Test enhanced thermal analysis engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
        
        self.thermal_engine = EnhancedThermalAnalysisEngine()
    
    def test_run_thermal_analysis(self):
        """Test basic thermal analysis"""
        device_specs = {
            'power_dissipation': 100.0,  # Watts
            'thermal_resistance': 1.0,   # K/W
            'ambient_temperature': 25.0  # °C
        }
        
        results = self.thermal_engine.run_thermal_analysis(device_specs)
        
        self.assertIn('max_temperature', results)
        self.assertIn('thermal_resistance', results)
        self.assertIn('meets_requirements', results)
        
        # Check calculated temperature
        expected_temp = 25.0 + (100.0 * 1.0)  # Ambient + (Power * Resistance)
        self.assertAlmostEqual(results['max_temperature'], expected_temp, places=1)
    
    def test_calculate_thermal_resistance(self):
        """Test thermal resistance calculation"""
        # Test with known values
        resistance = self.thermal_engine.calculate_thermal_resistance(
            power=50.0,
            temp_rise=25.0
        )
        
        expected_resistance = 25.0 / 50.0  # 0.5 K/W
        self.assertAlmostEqual(resistance, expected_resistance, places=2)
    
    def test_analyze_hotspots(self):
        """Test hotspot analysis"""
        temperature_field = np.random.rand(50, 50) * 100 + 298.15
        
        # Add some artificial hotspots
        temperature_field[10:15, 10:15] += 50  # Hotspot 1
        temperature_field[35:40, 35:40] += 30  # Hotspot 2
        
        hotspots = self.thermal_engine.analyze_hotspots(temperature_field, threshold=350.0)
        
        self.assertIsInstance(hotspots, list)
        self.assertGreater(len(hotspots), 0)
        
        # Check hotspot structure
        if hotspots:
            hotspot = hotspots[0]
            self.assertIn('x', hotspot)
            self.assertIn('y', hotspot)
            self.assertIn('temperature', hotspot)
            self.assertIn('area', hotspot)

class TestDatabaseIntegratedThermalEngine(unittest.TestCase):
    """Test database-integrated thermal engine"""
    
    def setUp(self):
        """Set up test fixtures"""
        if not THERMAL_MODULES_AVAILABLE:
            self.skipTest("Thermal modules not available")
        
        # Mock database integration
        with patch('python.thermal.database_integration.ThermalDatabaseIntegration'):
            self.db_thermal_engine = DatabaseIntegratedThermalEngine(enable_database=False)
    
    def test_initialization_without_database(self):
        """Test initialization without database"""
        engine = DatabaseIntegratedThermalEngine(enable_database=False)
        
        self.assertFalse(engine.enable_database)
        self.assertIsNone(engine.db_integration)
    
    @patch('python.thermal.database_integration.ThermalDatabaseIntegration')
    def test_initialization_with_database(self, mock_db_class):
        """Test initialization with database"""
        mock_db_instance = Mock()
        mock_db_class.return_value = mock_db_instance
        
        engine = DatabaseIntegratedThermalEngine(enable_database=True)
        
        self.assertTrue(engine.enable_database)
        self.assertIsNotNone(engine.db_integration)

if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    if THERMAL_MODULES_AVAILABLE:
        test_suite.addTest(unittest.makeSuite(TestThermalDatabaseIntegration))
        test_suite.addTest(unittest.makeSuite(TestThermalVisualizationEngine))
        test_suite.addTest(unittest.makeSuite(TestIndustrialThermalApplications))
        test_suite.addTest(unittest.makeSuite(TestEnhancedThermalAnalysisEngine))
        test_suite.addTest(unittest.makeSuite(TestDatabaseIntegratedThermalEngine))
    else:
        print("Skipping thermal module tests - modules not available")
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\nTest Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
