#!/usr/bin/env python3
"""
Test Geometry-Simulation Integration
===================================

Comprehensive test for the integrated geometry-simulation pipeline.

Author: Dr<PERSON>
"""

import sys
import os
import logging
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('geometry_integration_test.log')
        ]
    )

def test_integration_imports():
    """Test if integration modules can be imported"""
    print("🔍 Testing integration module imports...")
    
    try:
        from src.python.integration import GeometrySimulationPipeline
        print("✅ GeometrySimulationPipeline imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import integration modules: {e}")
        return False

def test_pipeline_initialization():
    """Test pipeline initialization"""
    print("\n🏗️  Testing pipeline initialization...")
    
    try:
        from src.python.integration import GeometrySimulationPipeline
        
        pipeline = GeometrySimulationPipeline(output_dir="test_integration_output")
        print("✅ Pipeline initialized successfully")
        
        # Check if components are available
        if hasattr(pipeline, 'geometry_manager') and pipeline.geometry_manager:
            print("✅ Geometry manager available")
        else:
            print("⚠️  Geometry manager not available - using fallback")
        
        if hasattr(pipeline, 'simulator') and pipeline.simulator:
            print("✅ Simulator available")
        else:
            print("⚠️  Simulator not available - using fallback")
        
        return pipeline
        
    except Exception as e:
        print(f"❌ Pipeline initialization failed: {e}")
        return None

def test_single_device_workflow(pipeline):
    """Test single device workflow"""
    print("\n🔬 Testing single device workflow...")
    
    if not pipeline:
        print("❌ No pipeline available for testing")
        return False
    
    # Test MOSFET device
    test_device = {
        'name': 'Test_180nm_MOSFET',
        'type': 'MOSFET',
        'parameters': {
            'gate_length': 0.18,
            'gate_width': 2.0,
            'source_drain_length': 1.0,
            'dopant_type': 'boron',
            'dose': 1e15,
            'energy': 30
        },
        'wafer': {
            'diameter': 200.0,
            'thickness': 0.725,
            'material': 'silicon'
        },
        'grid': {
            'x_dim': 100,
            'y_dim': 100
        }
    }
    
    try:
        print(f"   Processing: {test_device['name']}")
        start_time = time.time()
        
        result = pipeline.create_device_with_simulation(test_device)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if result.get('success', False):
            print(f"✅ Device workflow completed successfully in {processing_time:.2f}s")
            
            # Check result components
            if 'geometry' in result:
                print("   ✅ Geometry creation completed")
            if 'process' in result:
                print("   ✅ Process flow applied")
            if 'physics' in result:
                print("   ✅ Physics simulations completed")
            if 'analysis' in result:
                print("   ✅ Performance analysis completed")
            if 'report' in result:
                print("   ✅ Device report generated")
            
            return True
        else:
            print(f"❌ Device workflow failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Single device workflow test failed: {e}")
        return False

def test_industrial_examples_integration():
    """Test integration with industrial examples"""
    print("\n🏭 Testing industrial examples integration...")
    
    try:
        from src.python.examples import IndustrialDeviceExamples
        from src.python.integration import GeometrySimulationPipeline
        
        # Create examples and pipeline
        examples = IndustrialDeviceExamples(output_dir="test_integration_examples")
        pipeline = GeometrySimulationPipeline(output_dir="test_integration_pipeline")
        
        # Test integration with one example
        print("   Testing MOSFET example integration...")
        
        # Get MOSFET example result
        mosfet_result = examples.example_1_mosfet_transistor()
        
        if 'error' not in mosfet_result:
            # Convert to pipeline format
            device_spec = {
                'name': 'Industrial_MOSFET',
                'type': 'MOSFET',
                'parameters': {
                    'gate_length': 0.18,
                    'gate_width': 2.0,
                    'source_drain_length': 1.0
                }
            }
            
            # Run through pipeline
            pipeline_result = pipeline.create_device_with_simulation(device_spec)
            
            if pipeline_result.get('success', False):
                print("✅ Industrial example integration successful")
                return True
            else:
                print(f"❌ Pipeline processing failed: {pipeline_result.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ Industrial example failed: {mosfet_result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Industrial examples integration test failed: {e}")
        return False

def test_complete_workflow():
    """Test complete workflow with multiple devices"""
    print("\n🚀 Testing complete workflow...")
    
    try:
        from src.python.integration import GeometrySimulationPipeline
        
        pipeline = GeometrySimulationPipeline(output_dir="test_complete_workflow")
        
        # Define test devices (subset of industrial devices)
        test_devices = [
            {
                'name': 'Test_MOSFET',
                'type': 'MOSFET',
                'parameters': {
                    'gate_length': 0.18,
                    'gate_width': 2.0,
                    'source_drain_length': 1.0
                }
            },
            {
                'name': 'Test_Memory',
                'type': 'Memory',
                'parameters': {
                    'cell_type': 'SRAM',
                    'cell_size': 0.1,
                    'array_x': 4,
                    'array_y': 4
                }
            },
            {
                'name': 'Test_Power',
                'type': 'Power',
                'parameters': {
                    'breakdown_voltage': 1200,
                    'current_rating': 25
                }
            }
        ]
        
        workflow_results = {}
        successful_devices = 0
        
        for device_spec in test_devices:
            print(f"   Processing: {device_spec['name']}")
            
            try:
                result = pipeline.create_device_with_simulation(device_spec)
                workflow_results[device_spec['name']] = result
                
                if result.get('success', False):
                    successful_devices += 1
                    print(f"   ✅ {device_spec['name']} completed")
                else:
                    print(f"   ❌ {device_spec['name']} failed: {result.get('error', 'Unknown')}")
                    
            except Exception as e:
                print(f"   ❌ {device_spec['name']} exception: {e}")
                workflow_results[device_spec['name']] = {'error': str(e), 'success': False}
        
        success_rate = successful_devices / len(test_devices) * 100
        print(f"\n📊 Complete workflow results: {successful_devices}/{len(test_devices)} devices successful ({success_rate:.1f}%)")
        
        # Generate summary
        pipeline.generate_workflow_summary(workflow_results)
        
        return success_rate > 0  # At least one device should succeed
        
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        return False

def test_gui_integration():
    """Test GUI integration with geometry pipeline"""
    print("\n🖥️  Testing GUI integration...")
    
    try:
        from src.python.gui.geometry_panel import GeometryPanel
        from PySide6.QtWidgets import QApplication
        
        # Test if GUI can be created (without showing)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        panel = GeometryPanel()
        print("✅ Geometry GUI panel created successfully")
        
        # Test if panel has integration capabilities
        if hasattr(panel, 'geometry_manager'):
            print("✅ GUI has geometry manager integration")
        
        if hasattr(panel, 'run_simulation'):
            print("✅ GUI has simulation integration")
        else:
            print("⚠️  GUI simulation integration not available")
        
        return True
        
    except ImportError as e:
        print(f"⚠️  GUI integration test skipped: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        return False

def generate_integration_report(test_results):
    """Generate comprehensive integration test report"""
    report_path = Path("geometry_integration_test_report.md")
    
    with open(report_path, 'w') as f:
        f.write("# Geometry-Simulation Integration Test Report\n\n")
        f.write(f"**Test Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Test Environment\n\n")
        f.write(f"- Python Version: {sys.version}\n")
        f.write(f"- Platform: {sys.platform}\n")
        f.write(f"- Working Directory: {os.getcwd()}\n\n")
        
        f.write("## Test Results\n\n")
        f.write("| Test | Result | Details |\n")
        f.write("|------|--------|----------|\n")
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            details = result.get('details', 'No details')
            f.write(f"| {test_name} | {status} | {details} |\n")
        
        f.write("\n## Summary\n\n")
        passed = sum(1 for result in test_results.values() if result['success'])
        total = len(test_results)
        f.write(f"**Overall Success Rate:** {passed}/{total} ({passed/total*100:.1f}%)\n\n")
        
        if passed == total:
            f.write("🎉 All integration tests passed! The geometry-simulation pipeline is fully functional.\n\n")
        elif passed > total / 2:
            f.write("✅ Most integration tests passed. Minor issues may need attention.\n\n")
        else:
            f.write("⚠️  Several integration tests failed. Review implementation and dependencies.\n\n")
        
        f.write("## Next Steps\n\n")
        f.write("1. Address any failed tests\n")
        f.write("2. Run full industrial device workflow\n")
        f.write("3. Validate visualization outputs\n")
        f.write("4. Perform performance optimization\n")
    
    print(f"\n📄 Integration test report generated: {report_path}")

def main():
    """Main integration test function"""
    print("🧪 SemiPRO Geometry-Simulation Integration Test Suite")
    print("=" * 60)
    
    setup_logging()
    
    test_results = {}
    
    # Test 1: Module imports
    imports_success = test_integration_imports()
    test_results['Module Imports'] = {
        'success': imports_success,
        'details': 'Integration modules import successfully' if imports_success else 'Import failures detected'
    }
    
    # Test 2: Pipeline initialization
    pipeline = test_pipeline_initialization()
    init_success = pipeline is not None
    test_results['Pipeline Initialization'] = {
        'success': init_success,
        'details': 'Pipeline components initialized' if init_success else 'Initialization failed'
    }
    
    # Test 3: Single device workflow
    if pipeline:
        single_device_success = test_single_device_workflow(pipeline)
        test_results['Single Device Workflow'] = {
            'success': single_device_success,
            'details': 'Complete device simulation workflow' if single_device_success else 'Workflow failed'
        }
    else:
        test_results['Single Device Workflow'] = {
            'success': False,
            'details': 'Pipeline not available'
        }
    
    # Test 4: Industrial examples integration
    examples_success = test_industrial_examples_integration()
    test_results['Industrial Examples Integration'] = {
        'success': examples_success,
        'details': 'Examples integrate with pipeline' if examples_success else 'Integration failed'
    }
    
    # Test 5: Complete workflow
    workflow_success = test_complete_workflow()
    test_results['Complete Workflow'] = {
        'success': workflow_success,
        'details': 'Multi-device workflow completed' if workflow_success else 'Workflow failed'
    }
    
    # Test 6: GUI integration
    gui_success = test_gui_integration()
    test_results['GUI Integration'] = {
        'success': gui_success,
        'details': 'GUI integrates with geometry pipeline' if gui_success else 'GUI integration issues'
    }
    
    # Generate comprehensive report
    generate_integration_report(test_results)
    
    # Final summary
    print("\n🎯 Integration Test Suite Complete")
    print("=" * 40)
    
    passed = sum(1 for result in test_results.values() if result['success'])
    total = len(test_results)
    print(f"Overall Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    if passed == total:
        print("\n🎉 All integration tests passed!")
        print("The geometry-simulation pipeline is fully functional.")
    elif passed > total / 2:
        print("\n✅ Most tests passed - minor issues may need attention.")
    else:
        print("\n⚠️  Several tests failed - review implementation.")
    
    return test_results

if __name__ == "__main__":
    main()
