#!/usr/bin/env python3
"""
Test Phase 2 Module Completion
Real testing of all enhanced modules with wafer management and simulation methods
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_module_completion():
    """Test all enhanced modules have proper wafer management and simulation methods"""
    print("Testing Phase 2 Module Completion")
    print("=" * 50)
    
    # Import Qt application
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    
    results = {}
    
    # Test all enhanced modules
    modules_to_test = [
        ('deposition', 'src.python.gui.enhanced_deposition_panel', 'EnhancedDepositionPanel', 'run_deposition_simulation'),
        ('lithography', 'src.python.gui.enhanced_lithography_panel', 'EnhancedLithographyPanel', 'run_lithography_simulation'),
        ('etching', 'src.python.gui.enhanced_etching_panel', 'EnhancedEtchingPanel', 'run_etching_simulation'),
        ('metallization', 'src.python.gui.enhanced_metallization_panel', 'EnhancedMetallizationPanel', 'run_metallization_simulation'),
        ('thermal', 'src.python.gui.enhanced_thermal_panel', 'EnhancedThermalPanel', 'run_thermal_simulation'),
        ('packaging', 'src.python.gui.enhanced_packaging_panel', 'EnhancedPackagingPanel', 'run_packaging_simulation'),
        ('reliability', 'src.python.gui.enhanced_reliability_panel', 'EnhancedReliabilityPanel', 'run_reliability_simulation')
    ]
    
    for module_name, module_path, class_name, sim_method in modules_to_test:
        print(f"\nTesting {module_name.title()} Module...")
        
        try:
            # Import module
            module = __import__(module_path, fromlist=[class_name])
            panel_class = getattr(module, class_name)
            
            # Create panel instance
            panel = panel_class()
            print(f"  Panel created: SUCCESS")
            
            # Test wafer management methods
            wafer_methods = ['set_wafer', 'get_wafer', 'get_results']
            wafer_ok = all(hasattr(panel, method) for method in wafer_methods)
            print(f"  Wafer methods: {'SUCCESS' if wafer_ok else 'MISSING'}")
            
            # Test simulation method
            sim_ok = hasattr(panel, sim_method)
            print(f"  Simulation method: {'SUCCESS' if sim_ok else 'MISSING'}")
            
            # Test wafer setting and getting
            if wafer_ok:
                try:
                    # Create mock wafer
                    mock_wafer = type('MockWafer', (), {
                        'diameter': 200.0, 'thickness': 775.0, 'material': 'silicon'
                    })()
                    
                    panel.set_wafer(mock_wafer)
                    retrieved_wafer = panel.get_wafer()
                    wafer_test_ok = retrieved_wafer is not None
                    print(f"  Wafer set/get: {'SUCCESS' if wafer_test_ok else 'FAILED'}")
                except Exception as e:
                    print(f"  Wafer set/get: FAILED - {e}")
                    wafer_test_ok = False
            else:
                wafer_test_ok = False
            
            # Test simulation execution
            if sim_ok:
                try:
                    result = getattr(panel, sim_method)("Test Process")
                    sim_test_ok = isinstance(result, dict) and 'success' in result
                    print(f"  Simulation execution: {'SUCCESS' if sim_test_ok else 'FAILED'}")
                    
                    if sim_test_ok and result.get('success'):
                        print(f"    Process: {result.get('process_name', 'Unknown')}")
                        print(f"    Result keys: {list(result.keys())}")
                except Exception as e:
                    print(f"  Simulation execution: FAILED - {e}")
                    sim_test_ok = False
            else:
                sim_test_ok = False
            
            # Overall module status
            module_ok = wafer_ok and sim_ok and wafer_test_ok and sim_test_ok
            results[module_name] = {
                'panel_created': True,
                'wafer_methods': wafer_ok,
                'simulation_method': sim_ok,
                'wafer_test': wafer_test_ok,
                'simulation_test': sim_test_ok,
                'overall': module_ok
            }
            
            print(f"  Overall: {'SUCCESS' if module_ok else 'NEEDS WORK'}")
            
        except Exception as e:
            print(f"  ERROR: {e}")
            results[module_name] = {
                'panel_created': False,
                'error': str(e),
                'overall': False
            }
    
    # Generate summary
    print("\n" + "=" * 50)
    print("Phase 2 Module Completion Summary")
    print("=" * 50)
    
    total_modules = len(results)
    working_modules = sum(1 for r in results.values() if r.get('overall', False))
    
    print(f"Total Modules: {total_modules}")
    print(f"Fully Working: {working_modules}")
    print(f"Success Rate: {working_modules/total_modules*100:.1f}%")
    
    print(f"\nDetailed Results:")
    for module_name, result in results.items():
        if result.get('overall'):
            status = "COMPLETE"
        elif result.get('panel_created'):
            status = "PARTIAL"
        else:
            status = "FAILED"
        
        print(f"  {module_name.title()}: {status}")
        
        if not result.get('overall') and result.get('panel_created'):
            missing = []
            if not result.get('wafer_methods'): missing.append("wafer methods")
            if not result.get('simulation_method'): missing.append("simulation method")
            if not result.get('wafer_test'): missing.append("wafer functionality")
            if not result.get('simulation_test'): missing.append("simulation execution")
            if missing:
                print(f"    Missing: {', '.join(missing)}")
    
    if working_modules >= total_modules * 0.8:
        print(f"\nPhase 2 SUCCESS: Module completion ready for Phase 3")
        print(f"All modules have proper wafer management and simulation methods")
    else:
        print(f"\nPhase 2 PARTIAL: Some modules need additional work")
    
    # Clean up
    app.quit()
    
    return working_modules >= total_modules * 0.8

if __name__ == "__main__":
    success = test_module_completion()
    sys.exit(0 if success else 1)
