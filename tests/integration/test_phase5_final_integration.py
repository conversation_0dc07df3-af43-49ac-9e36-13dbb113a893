#!/usr/bin/env python3
"""
Test Phase 5 Final Integration
Test all modules including the newly integrated basic modules
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_final_integration():
    """Test final integration of all modules"""
    print("Testing Phase 5 Final Integration")
    print("=" * 70)
    
    # Import Qt application
    from PySide6.QtWidgets import QApplication
    app = QApplication([])
    
    results = {}
    
    # Test all modules
    modules_to_test = [
        # Enhanced modules (Phases 1-3)
        ('enhanced_deposition', 'src.python.gui.enhanced_deposition_panel', 'EnhancedDepositionPanel', 'run_deposition_simulation'),
        ('enhanced_lithography', 'src.python.gui.enhanced_lithography_panel', 'EnhancedLithographyPanel', 'run_lithography_simulation'),
        ('enhanced_etching', 'src.python.gui.enhanced_etching_panel', 'EnhancedEtchingPanel', 'run_etching_simulation'),
        ('enhanced_metallization', 'src.python.gui.enhanced_metallization_panel', 'EnhancedMetallizationPanel', 'run_metallization_simulation'),
        ('enhanced_thermal', 'src.python.gui.enhanced_thermal_panel', 'EnhancedThermalPanel', 'run_thermal_simulation'),
        ('enhanced_packaging', 'src.python.gui.enhanced_packaging_panel', 'EnhancedPackagingPanel', 'run_packaging_simulation'),
        ('enhanced_reliability', 'src.python.gui.enhanced_reliability_panel', 'EnhancedReliabilityPanel', 'run_reliability_simulation'),
        
        # Phase 4 integrated modules
        ('geometry', 'src.python.gui.geometry_panel', 'GeometryPanel', 'run_geometry_simulation'),
        ('oxidation', 'src.python.gui.oxidation_panel', 'OxidationPanel', 'run_oxidation_simulation'),
        ('doping', 'src.python.gui.doping_panel', 'DopingPanel', 'run_doping_simulation'),
        ('cmp', 'src.python.gui.cmp_panel', 'CMPPanel', 'run_cmp_simulation'),
        
        # Phase 5 newly integrated modules
        ('etching', 'src.python.gui.etching_panel', 'EtchingPanel', 'run_etching_simulation'),
        ('metallization', 'src.python.gui.metallization_panel', 'MetallizationPanel', 'run_metallization_simulation'),
        ('packaging', 'src.python.gui.packaging_panel', 'PackagingPanel', 'run_packaging_simulation'),
        ('reliability', 'src.python.gui.reliability_panel', 'ReliabilityPanel', 'run_reliability_simulation'),
        
        # Basic modules with wafer management only
        ('deposition', 'src.python.gui.deposition_panel', 'DepositionPanel', None),
        ('thermal', 'src.python.gui.thermal_panel', 'ThermalPanel', None),
    ]
    
    for module_name, module_path, class_name, sim_method in modules_to_test:
        print(f"\nTesting {module_name.title()} Module...")
        
        try:
            # Import module
            module = __import__(module_path, fromlist=[class_name])
            panel_class = getattr(module, class_name)
            
            # Create panel instance
            panel = panel_class()
            print(f"  Panel created: SUCCESS")
            
            # Test wafer management methods
            wafer_methods = ['set_wafer', 'get_wafer', 'get_results']
            wafer_ok = all(hasattr(panel, method) for method in wafer_methods)
            print(f"  Wafer methods: {'SUCCESS' if wafer_ok else 'MISSING'}")
            
            # Test simulation method if specified
            if sim_method:
                sim_ok = hasattr(panel, sim_method)
                print(f"  Simulation method: {'SUCCESS' if sim_ok else 'MISSING'}")
            else:
                sim_ok = True  # Not required for basic modules
                print(f"  Simulation method: NOT REQUIRED")
            
            # Test wafer setting and getting
            if wafer_ok:
                try:
                    # Create mock wafer
                    mock_wafer = type('MockWafer', (), {
                        'diameter': 200.0, 'thickness': 775.0, 'material': 'silicon'
                    })()
                    
                    panel.set_wafer(mock_wafer)
                    retrieved_wafer = panel.get_wafer()
                    wafer_test_ok = retrieved_wafer is not None
                    print(f"  Wafer set/get: {'SUCCESS' if wafer_test_ok else 'FAILED'}")
                except Exception as e:
                    print(f"  Wafer set/get: FAILED - {e}")
                    wafer_test_ok = False
            else:
                wafer_test_ok = False
            
            # Test simulation execution if method exists
            if sim_method and sim_ok:
                try:
                    result = getattr(panel, sim_method)("Test Process")
                    sim_test_ok = isinstance(result, dict) and 'success' in result
                    print(f"  Simulation execution: {'SUCCESS' if sim_test_ok else 'FAILED'}")
                    
                    if sim_test_ok and result.get('success'):
                        print(f"    Process: {result.get('process_name', 'Unknown')}")
                except Exception as e:
                    print(f"  Simulation execution: FAILED - {e}")
                    sim_test_ok = False
            else:
                sim_test_ok = True  # Not required
            
            # Overall module status
            module_ok = wafer_ok and sim_ok and wafer_test_ok and sim_test_ok
            results[module_name] = {
                'panel_created': True,
                'wafer_methods': wafer_ok,
                'simulation_method': sim_ok,
                'wafer_test': wafer_test_ok,
                'simulation_test': sim_test_ok,
                'overall': module_ok,
                'phase': 'enhanced' if 'enhanced' in module_name else ('phase4' if module_name in ['geometry', 'oxidation', 'doping', 'cmp'] else ('phase5' if module_name in ['etching', 'metallization', 'packaging', 'reliability'] else 'basic'))
            }
            
            print(f"  Overall: {'SUCCESS' if module_ok else 'NEEDS WORK'}")
            
        except Exception as e:
            print(f"  ERROR: {e}")
            results[module_name] = {
                'panel_created': False,
                'error': str(e),
                'overall': False,
                'phase': 'enhanced' if 'enhanced' in module_name else 'basic'
            }
    
    # Generate comprehensive summary
    print("\n" + "=" * 70)
    print("Phase 5 Final Integration Summary")
    print("=" * 70)
    
    # Categorize results by phase
    enhanced_modules = {k: v for k, v in results.items() if v.get('phase') == 'enhanced'}
    phase4_modules = {k: v for k, v in results.items() if v.get('phase') == 'phase4'}
    phase5_modules = {k: v for k, v in results.items() if v.get('phase') == 'phase5'}
    basic_modules = {k: v for k, v in results.items() if v.get('phase') == 'basic'}
    
    total_modules = len(results)
    working_modules = sum(1 for r in results.values() if r.get('overall', False))
    
    print(f"TOTAL MODULES TESTED: {total_modules}")
    print(f"FULLY WORKING: {working_modules}")
    print(f"SUCCESS RATE: {working_modules/total_modules*100:.1f}%")
    
    print(f"\nENHANCED MODULES (Phases 1-3): {len(enhanced_modules)}")
    enhanced_working = sum(1 for r in enhanced_modules.values() if r.get('overall', False))
    print(f"  Working: {enhanced_working}/{len(enhanced_modules)} ({enhanced_working/len(enhanced_modules)*100:.1f}%)")
    
    print(f"\nPHASE 4 MODULES: {len(phase4_modules)}")
    phase4_working = sum(1 for r in phase4_modules.values() if r.get('overall', False))
    print(f"  Working: {phase4_working}/{len(phase4_modules)} ({phase4_working/len(phase4_modules)*100:.1f}%)")
    
    print(f"\nPHASE 5 MODULES (Newly Integrated): {len(phase5_modules)}")
    phase5_working = sum(1 for r in phase5_modules.values() if r.get('overall', False))
    print(f"  Working: {phase5_working}/{len(phase5_modules)} ({phase5_working/len(phase5_modules)*100:.1f}%)")
    
    for module_name, result in phase5_modules.items():
        status = "COMPLETE" if result.get('overall') else "PARTIAL" if result.get('panel_created') else "FAILED"
        print(f"    {module_name.title()}: {status}")
        if result.get('overall'):
            print(f"      ✅ Full wafer management and simulation integration")
    
    print(f"\nBASIC MODULES: {len(basic_modules)}")
    basic_working = sum(1 for r in basic_modules.values() if r.get('overall', False))
    print(f"  Working: {basic_working}/{len(basic_modules)} ({basic_working/len(basic_modules)*100:.1f}%)")
    
    if working_modules >= total_modules * 0.85:
        print(f"\n🎉 PHASE 5 SUCCESS: Final integration achieved!")
        print(f"✅ {working_modules}/{total_modules} modules fully integrated")
        print(f"✅ All critical process modules have wafer management")
        print(f"✅ All modules ready for PostgreSQL device fabrication flow")
        print(f"✅ System ready for production use")
    else:
        print(f"\n⚠️  Phase 5 PARTIAL: Good progress, minor issues remain")
        print(f"📊 {working_modules}/{total_modules} modules working")
    
    # Clean up
    app.quit()
    
    return working_modules >= total_modules * 0.8

if __name__ == "__main__":
    success = test_final_integration()
    print(f"\n{'='*70}")
    print("Phase 5 Final Integration Test Complete!")
    sys.exit(0 if success else 1)
