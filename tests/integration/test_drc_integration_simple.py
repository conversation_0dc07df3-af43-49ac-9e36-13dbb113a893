#!/usr/bin/env python3
"""
Simple DRC Integration Test
===========================

Simple test to verify DRC module integration from backend to frontend.

Author: Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
"""

import sys
import os
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_drc_bridge():
    """Test DRC bridge functionality"""
    print("Testing DRC Bridge...")
    
    try:
        from enhanced_drc_bridge import EnhancedDR<PERSON>Bridge
        
        bridge = EnhancedDRCBridge()
        print("  DRC bridge created successfully")
        
        # Test getting applications
        applications = bridge.get_industrial_applications()
        print(f"  Found {len(applications)} industrial applications")
        
        # Test creating a system
        result = bridge.create_industrial_drc_system('advanced_logic', 'default')
        if result.get('success', False):
            print(f"  Created advanced logic system with {result.get('rule_count', 0)} rules")
            
            # Test DRC analysis
            drc_result = bridge.perform_comprehensive_drc()
            if 'error' not in drc_result:
                print(f"  DRC analysis found {drc_result.get('total_violations', 0)} violations")
                print(f"  Overall score: {drc_result.get('overall_score', 0):.1f}")
                return True
            else:
                print(f"  DRC analysis failed: {drc_result.get('error', 'Unknown')}")
        else:
            print(f"  System creation failed: {result.get('error', 'Unknown')}")
        
        return False
        
    except Exception as e:
        print(f"  DRC bridge test failed: {e}")
        return False

def test_drc_examples():
    """Test DRC examples"""
    print("Testing DRC Examples...")
    
    try:
        from industrial_drc_examples import IndustrialDRCExamples
        
        examples = IndustrialDRCExamples()
        print("  DRC examples created successfully")
        
        # Test advanced logic example
        result = examples.run_advanced_logic_example()
        if result.get('success', False):
            print(f"  Advanced logic example completed in {result.get('execution_time_s', 0):.3f}s")
            
            drc_analysis = result.get('drc_analysis', {})
            readiness = result.get('industrial_readiness', {})
            
            print(f"  Violations: {drc_analysis.get('total_violations', 0)}")
            print(f"  Readiness: {readiness.get('readiness_level', 'Unknown')}")
            return True
        else:
            print(f"  Example failed: {result.get('error', 'Unknown')}")
        
        return False
        
    except Exception as e:
        print(f"  DRC examples test failed: {e}")
        return False

def test_drc_device_widget():
    """Test DRC device widget (import only)"""
    print("Testing DRC Device Widget...")
    
    try:
        from gui.drc_device_widget import DRCDeviceWidget
        print("  DRC device widget imported successfully")
        return True
        
    except Exception as e:
        print(f"  DRC device widget test failed: {e}")
        return False

def test_drc_panel():
    """Test DRC panel (import only)"""
    print("Testing DRC Panel...")
    
    try:
        from gui.drc_panel import DRCPanel
        print("  DRC panel imported successfully")
        return True
        
    except Exception as e:
        print(f"  DRC panel test failed: {e}")
        return False

def test_gui_integration():
    """Test GUI integration (import only)"""
    print("Testing GUI Integration...")
    
    try:
        from gui.main import MainWindow
        print("  Main window imported successfully")
        return True
        
    except Exception as e:
        print(f"  GUI integration test failed: {e}")
        return False

def main():
    """Run simple DRC integration tests"""
    print("Simple DRC Integration Test")
    print("=" * 40)
    
    start_time = time.time()
    
    tests = [
        ("DRC Bridge", test_drc_bridge),
        ("DRC Examples", test_drc_examples),
        ("DRC Device Widget", test_drc_device_widget),
        ("DRC Panel", test_drc_panel),
        ("GUI Integration", test_gui_integration)
    ]
    
    successful_tests = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"PASS: {test_name}")
                successful_tests += 1
            else:
                print(f"FAIL: {test_name}")
        except Exception as e:
            print(f"ERROR: {test_name} - {e}")
    
    total_time = time.time() - start_time
    success_rate = (successful_tests / len(tests)) * 100
    
    print("\n" + "=" * 40)
    print("Test Summary")
    print("=" * 40)
    print(f"Tests run: {len(tests)}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {len(tests) - successful_tests}")
    print(f"Success rate: {success_rate:.1f}%")
    print(f"Total time: {total_time:.3f}s")
    
    if success_rate >= 80:
        print("\nDRC Integration: SUCCESS")
        print("The DRC module is properly integrated and functional.")
    elif success_rate >= 60:
        print("\nDRC Integration: PARTIAL SUCCESS")
        print("Most components working, some issues need attention.")
    else:
        print("\nDRC Integration: NEEDS WORK")
        print("Significant issues found, requires debugging.")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
