#!/usr/bin/env python3
"""
Test script for backend integration system
Author: Dr<PERSON>
"""

import sys
import os
sys.path.insert(0, 'src/python')

def test_backend_manager():
    """Test the backend integration manager"""
    print("🔧 Testing Backend Integration Manager")
    print("=" * 50)
    
    try:
        from src.python.backend_integration_manager import get_backend_manager
        
        manager = get_backend_manager()
        manager.print_backend_status()
        
        print("\n✅ Backend manager test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Backend manager test failed: {e}")
        return False

def test_enhanced_bridge():
    """Test the enhanced backend bridge"""
    print("\n🌉 Testing Enhanced Backend Bridge")
    print("=" * 50)
    
    try:
        from src.python.enhanced_backend_bridge import get_enhanced_bridge
        
        bridge = get_enhanced_bridge()
        
        # Test geometry operation
        print("\n📐 Testing Geometry Operation:")
        result = bridge.run_geometry_operation("initialize_wafer", {
            'diameter': 300.0,
            'thickness': 775.0,
            'material': 'silicon'
        })
        
        print(f"  Success: {result.success}")
        print(f"  Backend: {result.backend_used}")
        print(f"  Time: {result.processing_time:.3f}s")
        if result.success:
            print(f"  Data: {result.data}")
        else:
            print(f"  Error: {result.error}")
        
        # Test lithography operation
        print("\n📸 Testing Lithography Operation:")
        result = bridge.run_lithography_operation("pattern_exposure", {
            'wavelength': 193,
            'na': 1.35,
            'dose': 30
        })
        
        print(f"  Success: {result.success}")
        print(f"  Backend: {result.backend_used}")
        print(f"  Time: {result.processing_time:.3f}s")
        if result.success:
            print(f"  Data: {result.data}")
        else:
            print(f"  Error: {result.error}")
        
        # Test thermal operation
        print("\n🔥 Testing Thermal Operation:")
        result = bridge.run_thermal_operation("thermal_anneal", {
            'temperature': 1000,
            'time': 30
        })
        
        print(f"  Success: {result.success}")
        print(f"  Backend: {result.backend_used}")
        print(f"  Time: {result.processing_time:.3f}s")
        if result.success:
            print(f"  Data: {result.data}")
        else:
            print(f"  Error: {result.error}")
        
        print("\n✅ Enhanced bridge test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced bridge test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cython_modules():
    """Test individual Cython modules"""
    print("\n🐍 Testing Individual Cython Modules")
    print("=" * 50)
    
    modules_to_test = [
        'simple_geometry',
        'enhanced_lithography',
        'enhanced_lithography_simple',
        'advanced_thermal',
        'enhanced_packaging'
    ]
    
    successful_imports = 0
    
    for module_name in modules_to_test:
        try:
            module = __import__(module_name)
            print(f"  ✅ {module_name}: Successfully imported")
            
            # Try to get some basic info
            if hasattr(module, '__version__'):
                print(f"     Version: {module.__version__}")
            if hasattr(module, '__doc__') and module.__doc__:
                print(f"     Description: {module.__doc__.strip().split('.')[0]}")
            
            successful_imports += 1
            
        except ImportError as e:
            print(f"  ❌ {module_name}: Import failed - {e}")
        except Exception as e:
            print(f"  ⚠️  {module_name}: Imported but error accessing - {e}")
            successful_imports += 1  # Still counts as successful import
    
    print(f"\n📊 Cython Module Summary: {successful_imports}/{len(modules_to_test)} modules available")
    return successful_imports > 0

def main():
    """Main test function"""
    print("🧪 SemiPRO Backend Integration Test Suite")
    print("Author: Dr. Mazharuddin Mohammed")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Backend Manager
    if test_backend_manager():
        tests_passed += 1
    
    # Test 2: Enhanced Bridge
    if test_enhanced_bridge():
        tests_passed += 1
    
    # Test 3: Cython Modules
    if test_cython_modules():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"🏆 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Backend integration is working correctly.")
        return 0
    elif tests_passed > 0:
        print("⚠️  Some tests passed. Backend integration is partially working.")
        return 1
    else:
        print("❌ All tests failed. Backend integration needs attention.")
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
