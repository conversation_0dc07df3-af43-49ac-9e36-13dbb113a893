#!/usr/bin/env python3
"""
Enhanced Deposition Integration Test
Comprehensive test of the enhanced deposition module integration
"""

import sys
import os
import time
import logging

# Add the src/python directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_deposition_manager():
    """Test the enhanced deposition manager"""
    print("\n" + "="*60)
    print("TESTING ENHANCED DEPOSITION MANAGER")
    print("="*60)
    
    try:
        from deposition_manager import Deposition<PERSON>anager, DepositionConditions, DepositionTechnique, MaterialType
        
        # Initialize manager
        manager = DepositionManager()
        print("✓ Deposition manager initialized")
        
        # Test basic deposition simulation
        conditions = DepositionConditions(
            technique=DepositionTechnique.PECVD,
            material=MaterialType.SILICON_DIOXIDE,
            temperature=400.0,
            pressure=2.0,
            target_thickness=0.1,
            flow_rate=100.0,
            power=300.0
        )
        
        results = manager.simulate_deposition(None, conditions)
        print(f"✓ Basic deposition simulation: {results.final_thickness:.3f} μm")
        print(f"  - Uniformity: {results.uniformity:.1f}%")
        print(f"  - Deposition rate: {results.deposition_rate:.3f} μm/min")
        
        # Test industrial process
        processes = manager.get_available_processes()
        print(f"✓ Available industrial processes: {len(processes)}")
        
        if processes:
            process_name = processes[0]
            industrial_result = manager.simulate_industrial_process(None, process_name)
            print(f"✓ Industrial process '{process_name}': {'Success' if industrial_result['success'] else 'Failed'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Deposition manager test failed: {e}")
        return False

def test_industrial_processes():
    """Test industrial deposition processes"""
    print("\n" + "="*60)
    print("TESTING INDUSTRIAL DEPOSITION PROCESSES")
    print("="*60)
    
    try:
        from deposition_manager import DepositionManager
        from industrial_deposition_processes import IndustrialDepositionProcesses
        
        # Initialize
        manager = DepositionManager()
        industrial = IndustrialDepositionProcesses(manager)
        print("✓ Industrial processes initialized")
        
        # Test workflow execution
        workflows = industrial.get_available_workflows()
        print(f"✓ Available workflows: {len(workflows)}")
        
        if workflows:
            workflow_name = workflows[0]
            workflow_result = industrial.run_workflow(None, workflow_name)
            print(f"✓ Workflow '{workflow_name}': {'Success' if workflow_result['overall_success'] else 'Failed'}")
            print(f"  - Steps executed: {len(workflow_result['step_results'])}")
            print(f"  - Quality checks: {len(workflow_result['quality_check_results'])}")
        
        # Test process optimization
        processes = industrial.get_available_processes()
        if processes:
            process_name = processes[0]
            optimization_result = industrial.optimize_process_conditions(
                process_name, 
                {'thickness': 100.0, 'uniformity': 2.0}
            )
            print(f"✓ Process optimization completed: {optimization_result['iterations']} iterations")
        
        return True
        
    except Exception as e:
        print(f"✗ Industrial processes test failed: {e}")
        return False

def test_deposition_examples():
    """Test deposition examples"""
    print("\n" + "="*60)
    print("TESTING DEPOSITION EXAMPLES")
    print("="*60)
    
    try:
        from deposition_examples import DepositionExamples
        
        # Initialize examples
        examples = DepositionExamples()
        print("✓ Deposition examples initialized")
        
        # Get available examples
        available_examples = examples.get_available_examples()
        print(f"✓ Available example cases: {len(available_examples)}")
        
        # Run a specific example
        if available_examples:
            case_name = available_examples[0]
            case_result = examples.run_example_case(case_name)
            print(f"✓ Example case '{case_name}': {'Success' if case_result['success'] else 'Failed'}")
            print(f"  - Simulation time: {case_result['simulation_time']:.2f}s")
            print(f"  - Overall success: {case_result['analysis']['overall_success']}")
            print(f"  - Recommendations: {len(case_result['analysis']['recommendations'])}")
        
        # Run comparative study
        if len(available_examples) >= 2:
            study_cases = available_examples[:2]
            study_result = examples.run_comparative_study(study_cases)
            print(f"✓ Comparative study: {study_result['comparative_analysis']['success_rates']['success_rate']:.1f}% success rate")
        
        return True
        
    except Exception as e:
        print(f"✗ Deposition examples test failed: {e}")
        return False

def test_gui_integration():
    """Test GUI integration"""
    print("\n" + "="*60)
    print("TESTING GUI INTEGRATION")
    print("="*60)

    try:
        # Test GUI module imports (without creating widgets that require Qt)
        try:
            import gui.enhanced_deposition_panel
            print("✓ Enhanced deposition panel module imported")
        except ImportError as e:
            if "Qt" in str(e) or "PyQt" in str(e) or "PySide" in str(e):
                print("⚠ GUI modules require Qt bindings (expected in test environment)")
                return True  # This is expected in test environments
            else:
                raise e

        # Test that the module structure is correct
        from gui.enhanced_deposition_panel import EnhancedDepositionPanel
        print("✓ GUI classes can be imported")

        return True

    except Exception as e:
        print(f"✗ GUI integration test failed: {e}")
        return False

def test_pipeline_integration():
    """Test pipeline integration"""
    print("\n" + "="*60)
    print("TESTING PIPELINE INTEGRATION")
    print("="*60)
    
    try:
        from deposition_pipeline_integration import EnhancedDepositionPipeline
        
        # Initialize pipeline
        pipeline = EnhancedDepositionPipeline()
        print("✓ Enhanced deposition pipeline initialized")
        
        # Get integration status
        status = pipeline.get_integration_status()
        print(f"✓ Integration status:")
        print(f"  - Pipeline available: {status['pipeline_available']}")
        print(f"  - Process simulator: {status['process_simulator_available']}")
        print(f"  - Workflow manager: {status['workflow_manager_available']}")
        print(f"  - Available workflows: {status['available_workflows']}")
        print(f"  - Available examples: {status['available_examples']}")
        
        # Test workflow execution
        workflows = pipeline.get_available_workflows()
        if workflows:
            workflow_id = workflows[0]
            wafer_config = {'name': 'test_wafer', 'diameter': 200.0, 'thickness': 525.0}
            
            workflow_result = pipeline.run_integrated_deposition_workflow(
                workflow_id, wafer_config
            )
            print(f"✓ Integrated workflow '{workflow_id}': {'Success' if workflow_result['overall_success'] else 'Failed'}")
            print(f"  - Execution time: {workflow_result['execution_time']:.2f}s")
        
        # Test example case integration
        examples = pipeline.get_available_examples()
        if examples:
            case_name = examples[0]
            wafer_config = {'name': 'example_wafer', 'diameter': 200.0, 'thickness': 525.0}
            
            case_result = pipeline.run_example_case_integration(case_name, wafer_config)
            print(f"✓ Example case integration '{case_name}': {'Success' if case_result['success'] else 'Failed'}")
        
        # Generate integration report
        report = pipeline.generate_integration_report()
        print("✓ Integration report generated")
        
        return True
        
    except Exception as e:
        print(f"✗ Pipeline integration test failed: {e}")
        return False

def test_complete_workflow():
    """Test complete deposition workflow"""
    print("\n" + "="*60)
    print("TESTING COMPLETE DEPOSITION WORKFLOW")
    print("="*60)
    
    try:
        # Import all components
        from deposition_manager import DepositionManager, DepositionConditions, DepositionTechnique, MaterialType
        from industrial_deposition_processes import IndustrialDepositionProcesses
        from deposition_examples import DepositionExamples
        from deposition_pipeline_integration import EnhancedDepositionPipeline
        
        print("✓ All components imported successfully")
        
        # Initialize complete system
        pipeline = EnhancedDepositionPipeline()
        print("✓ Complete system initialized")
        
        # Simulate a complete CMOS frontend process
        print("\n--- Simulating CMOS Frontend Process ---")
        
        # 1. Gate oxide deposition
        manager = pipeline.deposition_manager
        gate_oxide_conditions = DepositionConditions(
            technique=DepositionTechnique.PECVD,
            material=MaterialType.SILICON_DIOXIDE,
            temperature=400.0,
            pressure=2.0,
            target_thickness=0.005,  # 5nm
            flow_rate=100.0,
            power=300.0
        )
        
        gate_oxide_result = manager.simulate_deposition(None, gate_oxide_conditions)
        print(f"1. ✓ Gate oxide: {gate_oxide_result.final_thickness*1000:.1f}nm, uniformity: {gate_oxide_result.uniformity:.1f}%")
        
        # 2. Polysilicon deposition
        poly_conditions = DepositionConditions(
            technique=DepositionTechnique.LPCVD,
            material=MaterialType.POLYSILICON,
            temperature=620.0,
            pressure=0.5,
            target_thickness=0.2,  # 200nm
            flow_rate=50.0
        )
        
        poly_result = manager.simulate_deposition(None, poly_conditions)
        print(f"2. ✓ Polysilicon: {poly_result.final_thickness*1000:.0f}nm, rate: {poly_result.deposition_rate:.3f} μm/min")
        
        # 3. Metal interconnect
        metal_conditions = DepositionConditions(
            technique=DepositionTechnique.PVD_SPUTTERING,
            material=MaterialType.ALUMINUM,
            temperature=150.0,
            pressure=0.005,
            target_thickness=0.5,  # 500nm
            power=8000.0
        )
        
        metal_result = manager.simulate_deposition(None, metal_conditions)
        print(f"3. ✓ Metal: {metal_result.final_thickness*1000:.0f}nm, step coverage: {metal_result.step_coverage:.1f}%")
        
        # 4. Run industrial example
        examples = pipeline.deposition_examples
        if examples.get_available_examples():
            case_name = "gate_oxide_mosfet"
            case_result = examples.run_example_case(case_name)
            print(f"4. ✓ Industrial example '{case_name}': {'Success' if case_result['success'] else 'Failed'}")
        
        # 5. Generate comprehensive report
        total_thickness = (gate_oxide_result.final_thickness + 
                          poly_result.final_thickness + 
                          metal_result.final_thickness)
        
        print(f"\n--- Process Summary ---")
        print(f"Total deposited thickness: {total_thickness:.3f} μm")
        print(f"Process steps completed: 3")
        print(f"Average uniformity: {(gate_oxide_result.uniformity + poly_result.uniformity + metal_result.uniformity)/3:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"✗ Complete workflow test failed: {e}")
        return False

def main():
    """Run all enhanced deposition integration tests"""
    print("Enhanced Deposition Module Integration Test")
    print("=" * 80)
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        ("Deposition Manager", test_deposition_manager),
        ("Industrial Processes", test_industrial_processes),
        ("Deposition Examples", test_deposition_examples),
        ("GUI Integration", test_gui_integration),
        ("Pipeline Integration", test_pipeline_integration),
        ("Complete Workflow", test_complete_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*80)
    print("ENHANCED DEPOSITION INTEGRATION TEST SUMMARY")
    print("="*80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    print(f"Total execution time: {time.time() - start_time:.2f} seconds")
    
    if passed == total:
        print("\n🎉 All enhanced deposition integration tests PASSED!")
        print("The enhanced deposition module is ready for production use.")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
