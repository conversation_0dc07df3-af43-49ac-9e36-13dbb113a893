#!/usr/bin/env python3
"""
Test script for the integrated patterning module

This script tests the new patterning functionality integrated into the SemiPRO geometry module.
"""

import sys
import os
import logging
import numpy as np
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_patterning_imports():
    """Test that patterning modules can be imported"""
    print("🔍 Testing patterning module imports...")
    
    try:
        from geometry.patterning import PatterningManager, DevicePatterns, IndustrialDeviceExamples, PatternAnalytics
        print("✅ Successfully imported patterning modules")
        return True
    except ImportError as e:
        print(f"❌ Failed to import patterning modules: {e}")
        return False

def test_device_patterns():
    """Test device patterns functionality"""
    print("\n🔍 Testing device patterns...")
    
    try:
        from geometry.patterning import DevicePatterns
        
        device_patterns = DevicePatterns()
        
        # Test device library
        devices = device_patterns.list_available_devices()
        print(f"✅ Found {len(devices)} devices in library")
        
        # Test device specifications
        for device_name in devices[:3]:  # Test first 3 devices
            spec = device_patterns.get_device_specification(device_name)
            if spec:
                print(f"  📱 {spec.name} ({spec.technology_node})")
            
        # Test mask generation
        if devices:
            mask = device_patterns.create_device_pattern_mask(devices[0])
            if mask is not None:
                print(f"✅ Generated mask for {devices[0]}: shape {mask.shape}")
            else:
                print(f"❌ Failed to generate mask for {devices[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Device patterns test failed: {e}")
        return False

def test_industrial_examples():
    """Test industrial examples functionality"""
    print("\n🔍 Testing industrial examples...")
    
    try:
        from geometry.patterning import IndustrialDeviceExamples
        
        examples = IndustrialDeviceExamples()
        
        # Test example listing
        example_names = examples.list_examples()
        print(f"✅ Found {len(example_names)} industrial examples")
        
        # Test example information
        for example_name in example_names[:2]:  # Test first 2 examples
            info = examples.get_example_info(example_name)
            if info:
                print(f"  🏭 {info['name']} ({info['manufacturer']})")
        
        # Test running an example
        if example_names:
            result = examples.run_example(example_names[0])
            if result.get("success"):
                print(f"✅ Successfully ran example: {example_names[0]}")
                print(f"  Processing time: {result['simulation_results'].get('Processing Time', 'N/A')}")
            else:
                print(f"❌ Failed to run example: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Industrial examples test failed: {e}")
        return False

def test_patterning_manager():
    """Test patterning manager functionality"""
    print("\n🔍 Testing patterning manager...")
    
    try:
        from geometry.patterning import PatterningManager
        
        # Initialize without database for testing
        patterning = PatterningManager(use_cython=False, database_config=None)
        
        # Test MOSFET pattern creation
        result = patterning.create_mosfet_pattern(
            0.18,  # gate_length
            2.0,   # gate_width
            0.5    # source_drain_length
        )
        
        if result.get("success"):
            print(f"✅ Created MOSFET pattern: {result.get('pattern_name')}")
            print(f"  Required modules: {', '.join(result.get('required_modules', []))}")
        else:
            print(f"❌ MOSFET pattern creation failed: {result.get('error')}")
        
        # Test FinFET pattern creation
        result = patterning.create_finfet_pattern(
            7.0,   # fin_width (nm)
            53.0,  # fin_height (nm)
            24.0,  # gate_length (nm)
            4      # num_fins
        )
        
        if result.get("success"):
            print(f"✅ Created FinFET pattern: {result.get('pattern_name')}")
        else:
            print(f"❌ FinFET pattern creation failed: {result.get('error')}")
        
        # Test pattern listing
        patterns = patterning.list_patterns()
        print(f"✅ Total patterns created: {len(patterns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Patterning manager test failed: {e}")
        return False

def test_pattern_analytics():
    """Test pattern analytics functionality"""
    print("\n🔍 Testing pattern analytics...")
    
    try:
        from geometry.patterning import PatternAnalytics
        
        analytics = PatternAnalytics()
        
        # Test analytics summary
        summary = analytics.get_summary()
        if "error" in summary:
            print(f"ℹ️  Analytics summary: {summary['error']}")
        else:
            print(f"✅ Analytics summary retrieved")
        
        return True
        
    except Exception as e:
        print(f"❌ Pattern analytics test failed: {e}")
        return False

def test_gui_integration():
    """Test GUI integration (basic import test)"""
    print("\n🔍 Testing GUI integration...")
    
    try:
        # Test if GUI can import patterning modules
        from gui.geometry_panel import GeometryPanel
        print("✅ GUI can import patterning modules")
        
        return True
        
    except ImportError as e:
        print(f"❌ GUI integration test failed: {e}")
        return False
    except Exception as e:
        print(f"ℹ️  GUI integration test skipped (Qt not available): {e}")
        return True

def test_database_schema():
    """Test database schema creation"""
    print("\n🔍 Testing database schema...")
    
    try:
        schema_file = Path("database/schemas/patterning_schema.sql")
        if schema_file.exists():
            print(f"✅ Database schema file exists: {schema_file}")
            
            # Check file size
            size = schema_file.stat().st_size
            print(f"  Schema file size: {size} bytes")
            
            # Check for key tables
            content = schema_file.read_text()
            key_tables = ["patterns", "pattern_dimensions", "mosfet_patterns", "finfet_patterns", "mems_patterns"]
            
            for table in key_tables:
                if f"CREATE TABLE IF NOT EXISTS {table}" in content:
                    print(f"  ✅ Found table: {table}")
                else:
                    print(f"  ❌ Missing table: {table}")
            
            return True
        else:
            print(f"❌ Database schema file not found: {schema_file}")
            return False
            
    except Exception as e:
        print(f"❌ Database schema test failed: {e}")
        return False

def test_cython_bindings():
    """Test Cython bindings availability"""
    print("\n🔍 Testing Cython bindings...")
    
    try:
        from cython.patterning_wrapper import PyPatterningManager, create_patterning_manager
        print("✅ Cython bindings available")
        
        # Test creating manager
        manager = create_patterning_manager()
        print("✅ Cython patterning manager created")
        
        return True
        
    except ImportError as e:
        print(f"ℹ️  Cython bindings not available (expected): {e}")
        return True  # This is expected if not compiled
    except Exception as e:
        print(f"❌ Cython bindings test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 SemiPRO Patterning Module Integration Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_patterning_imports),
        ("Device Patterns", test_device_patterns),
        ("Industrial Examples", test_industrial_examples),
        ("Patterning Manager", test_patterning_manager),
        ("Pattern Analytics", test_pattern_analytics),
        ("GUI Integration", test_gui_integration),
        ("Database Schema", test_database_schema),
        ("Cython Bindings", test_cython_bindings),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status:<10} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Patterning integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
