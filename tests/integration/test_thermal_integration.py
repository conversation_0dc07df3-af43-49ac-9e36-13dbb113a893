#!/usr/bin/env python3
"""
Test Thermal Integration
========================

Test script to verify that the enhanced thermal module is properly integrated
and can create thermal device structures.

Author: Dr<PERSON> <PERSON><PERSON><PERSON><PERSON>
"""

import sys
import os
sys.path.append('src/python')

from src.python.semipro_packaging.enhanced_thermal_analysis import (
    EnhancedThermalAnalysisEngine, ThermalApplicationType
)
from src.python.enhanced_thermal_bridge import EnhancedThermalBridge

def test_thermal_analysis_and_device_creation():
    """Test thermal analysis and device creation"""
    print("Testing Enhanced Thermal Module Integration")
    print("=" * 50)
    
    # Initialize thermal engine
    thermal_engine = EnhancedThermalAnalysisEngine()
    print("✓ Thermal engine initialized")
    
    # Test CPU thermal management
    print("\n1. Testing CPU Thermal Management:")
    cpu_specs = {
        "tdp": 65.0,
        "core_count": 8,
        "base_frequency": 3.2,
        "boost_frequency": 4.5,
        "ambient_temperature": 25.0
    }
    
    cpu_results = thermal_engine.analyze_thermal_performance(
        ThermalApplicationType.CPU_THERMAL_MANAGEMENT,
        cpu_specs
    )
    
    print(f"   Max Temperature: {cpu_results.get('max_temperature', 0):.2f} K")
    print(f"   Thermal Resistance: {cpu_results.get('thermal_resistance', 0):.6f} K/W")
    print(f"   Thermal Efficiency: {cpu_results.get('thermal_efficiency', 0):.1f}%")
    
    # Test device creation
    print("\n2. Testing Device Creation:")
    bridge = EnhancedThermalBridge()
    print("✓ Thermal bridge initialized")
    
    cpu_device = bridge.create_device_from_thermal_results(cpu_results, 'cpu_thermal_device')
    print(f"✓ CPU thermal device created: {cpu_device.name}")
    print(f"   Device Type: {cpu_device.device_type}")
    print(f"   Technology Node: {cpu_device.technology_node}")
    print(f"   Max Temperature: {cpu_device.thermal_properties['max_temperature']:.2f} K")
    print(f"   Thermal Materials: {len(cpu_device.thermal_materials)} layers")
    print(f"   Cooling Elements: {len(cpu_device.cooling_system.get('cooling_elements', []))}")
    
    # Test LED thermal design
    print("\n3. Testing LED Thermal Design:")
    led_specs = {
        "led_power": 7.0,
        "junction_temperature": 85.0,
        "thermal_resistance": 2.5,
        "ambient_temperature": 25.0
    }
    
    led_results = thermal_engine.analyze_thermal_performance(
        ThermalApplicationType.LED_THERMAL_DESIGN,
        led_specs
    )
    
    led_device = bridge.create_device_from_thermal_results(led_results, 'led_thermal_device')
    print(f"✓ LED thermal device created: {led_device.name}")
    print(f"   Max Temperature: {led_device.thermal_properties['max_temperature']:.2f} K")
    print(f"   Heat Sources: {len(led_device.thermal_geometry.get('heat_sources', []))}")
    
    # Test power electronics cooling
    print("\n4. Testing Power Electronics Cooling:")
    power_specs = {
        "power_rating": 50.0,
        "switching_frequency": 20000.0,
        "efficiency": 0.95,
        "ambient_temperature": 25.0
    }
    
    power_results = thermal_engine.analyze_thermal_performance(
        ThermalApplicationType.POWER_ELECTRONICS_COOLING,
        power_specs
    )
    
    power_device = bridge.create_device_from_thermal_results(power_results, 'power_electronics_device')
    print(f"✓ Power electronics device created: {power_device.name}")
    print(f"   Max Temperature: {power_device.thermal_properties['max_temperature']:.2f} K")
    print(f"   MTBF: {power_device.reliability_metrics['mtbf']:.0f} hours")
    
    print("\n" + "=" * 50)
    print("✓ All thermal integration tests passed!")
    print("✓ Enhanced thermal module is properly integrated")
    print("✓ Device creation from thermal results is working")
    
    return True

if __name__ == "__main__":
    try:
        test_thermal_analysis_and_device_creation()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
