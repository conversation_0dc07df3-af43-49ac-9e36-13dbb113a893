#!/usr/bin/env python3
"""
Complete Multi-Die Integration Test
===================================

Test script to validate the complete integration from C++ backend
through Cython to Python frontend with GUI visualization.

Author: <PERSON><PERSON>
"""

import sys
import os
import logging
import time

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_bridge():
    """Test enhanced multi-die bridge functionality"""
    print("\n" + "="*60)
    print("TESTING ENHANCED MULTI-DIE BRIDGE")
    print("="*60)
    
    try:
        from enhanced_multi_die_bridge import Enhanced<PERSON>ultiDieBridge
        
        bridge = EnhancedMultiDieBridge()
        print("✓ Enhanced bridge initialized successfully")
        
        # Test industrial applications
        applications = ["hpc_chiplet", "mobile_soc", "automotive_ecu"]
        
        for app_name in applications:
            print(f"\nTesting {app_name} system creation...")
            
            result = bridge.create_industrial_system(app_name, "default")
            
            if result.get('success', False):
                print(f"✓ {app_name} system created successfully")
                print(f"  Dies: {result['die_count']}")
                print(f"  Interconnects: {result['interconnect_count']}")
                
                if 'dies' in result and result['dies']:
                    print(f"  Die visualization data: {len(result['dies'])} dies extracted")
                    for die in result['dies'][:2]:  # Show first 2 dies
                        print(f"    - {die['name']}: {die['type']} at {die['position']}")
                
                # Test comprehensive analysis
                analysis = bridge.perform_comprehensive_analysis()
                if 'overall_score' in analysis:
                    print(f"  Analysis score: {analysis['overall_score']:.1f}/100")
                
            else:
                print(f"✗ {app_name} system creation failed: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Enhanced bridge test failed: {e}")
        return False

def test_device_visualization():
    """Test device visualization widget"""
    print("\n" + "="*60)
    print("TESTING DEVICE VISUALIZATION WIDGET")
    print("="*60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.multi_die_device_widget import MultiDieDeviceWidget
        
        app = QApplication([])
        
        widget = MultiDieDeviceWidget()
        print("✓ Device visualization widget created")
        
        # Test mock system creation
        applications = ["hpc_chiplet", "mobile_soc", "automotive_ecu"]
        
        for app_name in applications:
            print(f"\nTesting {app_name} visualization...")
            
            widget.create_mock_system(app_name)
            
            if widget.dies:
                print(f"✓ {app_name} visualization created with {len(widget.dies)} dies")
                for die_id, die in list(widget.dies.items())[:2]:  # Show first 2 dies
                    print(f"    - {die['name']}: {die['type']} at {die['position']}")
            else:
                print(f"✗ {app_name} visualization failed - no dies created")
        
        app.quit()
        return True
        
    except ImportError:
        print("✗ PySide6 not available - GUI visualization test skipped")
        return False
    except Exception as e:
        print(f"✗ Device visualization test failed: {e}")
        return False

def test_multi_die_panel():
    """Test multi-die panel integration"""
    print("\n" + "="*60)
    print("TESTING MULTI-DIE PANEL INTEGRATION")
    print("="*60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.multi_die_panel import MultiDiePanel
        
        app = QApplication([])
        
        panel = MultiDiePanel()
        print("✓ Multi-die panel created successfully")
        
        # Test industrial system creation with visualization
        result = panel.create_industrial_system_with_visualization("hpc_chiplet", "default")
        
        if result:
            print("✓ Industrial system created with visualization")
            
            # Check if device widget was updated
            if hasattr(panel, 'device_widget') and panel.device_widget.dies:
                print(f"✓ Device widget updated with {len(panel.device_widget.dies)} dies")
            else:
                print("⚠ Device widget not properly updated")
        else:
            print("✗ Industrial system creation failed")
        
        app.quit()
        return True
        
    except ImportError:
        print("✗ PySide6 not available - GUI panel test skipped")
        return False
    except Exception as e:
        print(f"✗ Multi-die panel test failed: {e}")
        return False

def test_industrial_examples():
    """Test industrial examples functionality"""
    print("\n" + "="*60)
    print("TESTING INDUSTRIAL EXAMPLES")
    print("="*60)
    
    try:
        from industrial_multi_die_examples import IndustrialMultiDieExamples
        
        examples = IndustrialMultiDieExamples()
        print("✓ Industrial examples initialized")
        
        # Test HPC example
        result = examples.run_hpc_chiplet_example()
        
        if result.get('success', False):
            print("✓ HPC chiplet example executed successfully")
            if 'industrial_readiness' in result:
                readiness = result['industrial_readiness']
                print(f"  Readiness: {readiness.get('readiness_level', 'Unknown')} ({readiness.get('readiness_score', 0):.1f}/100)")
        else:
            print(f"✗ HPC example failed: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Industrial examples test failed: {e}")
        return False

def test_complete_workflow():
    """Test complete workflow from backend to frontend"""
    print("\n" + "="*60)
    print("TESTING COMPLETE WORKFLOW")
    print("="*60)
    
    try:
        # Step 1: Create system using enhanced bridge
        from enhanced_multi_die_bridge import EnhancedMultiDieBridge
        
        bridge = EnhancedMultiDieBridge()
        result = bridge.create_industrial_system("mobile_soc", "default")
        
        if not result.get('success', False):
            print("✗ System creation failed")
            return False
        
        print("✓ Step 1: System created successfully")
        
        # Step 2: Extract visualization data
        if 'dies' in result and result['dies']:
            print(f"✓ Step 2: Visualization data extracted ({len(result['dies'])} dies)")
        else:
            print("⚠ Step 2: No visualization data extracted")
        
        # Step 3: Perform analysis
        analysis = bridge.perform_comprehensive_analysis()
        if 'overall_score' in analysis:
            print(f"✓ Step 3: Analysis completed (score: {analysis['overall_score']:.1f}/100)")
        else:
            print("⚠ Step 3: Analysis incomplete")
        
        # Step 4: Test GUI integration (if available)
        try:
            from PySide6.QtWidgets import QApplication
            from gui.multi_die_device_widget import MultiDieDeviceWidget
            
            app = QApplication([])
            widget = MultiDieDeviceWidget()
            widget.load_industrial_system("mobile_soc", result)
            
            if widget.dies:
                print(f"✓ Step 4: GUI visualization updated ({len(widget.dies)} dies)")
            else:
                print("⚠ Step 4: GUI visualization not updated")
            
            app.quit()
            
        except ImportError:
            print("⚠ Step 4: GUI not available - skipped")
        
        print("✓ Complete workflow test successful")
        return True
        
    except Exception as e:
        print(f"✗ Complete workflow test failed: {e}")
        return False

def main():
    """Main test function"""
    print("Multi-Die Integration Complete Test Suite")
    print("Author: Dr. Mazharuddin Mohammed")
    print("="*80)
    
    tests = [
        ("Enhanced Bridge", test_enhanced_bridge),
        ("Device Visualization", test_device_visualization),
        ("Multi-Die Panel", test_multi_die_panel),
        ("Industrial Examples", test_industrial_examples),
        ("Complete Workflow", test_complete_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} test PASSED")
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
    
    # Final summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Multi-die integration is working correctly!")
    elif passed >= total * 0.8:
        print("✅ MOST TESTS PASSED - Integration is mostly functional")
    else:
        print("⚠️ SOME TESTS FAILED - Integration needs attention")
    
    print("\nNext Steps:")
    print("1. Build C++ backend: cd build && make -j$(nproc)")
    print("2. Run GUI: python src/python/main.py")
    print("3. Test multi-die panel in GUI")
    print("4. Create industrial systems and view visualizations")

if __name__ == "__main__":
    main()
