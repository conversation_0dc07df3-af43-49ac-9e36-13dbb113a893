#!/usr/bin/env python3
"""
Geometry Module PostgreSQL Integration Test
==========================================

Comprehensive test for PostgreSQL database integration with the geometry module.
Tests database schema, device creation, industrial examples, and fabrication flows.

Author: <PERSON><PERSON>
"""

import sys
import os
import logging
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "python"))

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('geometry_postgresql_test.log')
        ]
    )

def test_database_connection():
    """Test PostgreSQL database connection"""
    print("🔗 Testing PostgreSQL Database Connection")
    print("=" * 50)
    
    try:
        from database_manager import DatabaseManager
        
        db = DatabaseManager()
        print("✅ PostgreSQL connection established")
        
        # Test basic operations
        with db.get_cursor() as cursor:
            cursor.execute("SELECT version()")
            version = cursor.fetchone()
            print(f"✅ PostgreSQL version: {version[0]}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_geometry_database_schema():
    """Test geometry database schema initialization"""
    print("\n🗄️  Testing Geometry Database Schema")
    print("=" * 50)
    
    try:
        from geometry.database_init import initialize_geometry_database
        
        success = initialize_geometry_database()
        if success:
            print("✅ Geometry database schema initialized successfully")
        else:
            print("⚠️ Geometry database schema initialization had issues")
        
        return success
        
    except Exception as e:
        print(f"❌ Schema initialization failed: {e}")
        return False

def test_geometry_manager_database_integration():
    """Test GeometryManager with database integration"""
    print("\n🔧 Testing GeometryManager Database Integration")
    print("=" * 50)
    
    try:
        from geometry.geometry_manager import GeometryManager
        from database_manager import DatabaseManager
        
        # Initialize with database support
        db_manager = DatabaseManager()
        geometry_manager = GeometryManager(database_manager=db_manager)
        
        print("✅ GeometryManager initialized with database support")
        
        # Test device creation with database storage
        print("  📱 Testing MOSFET creation...")
        geometry_manager.create_mosfet_pattern(0.18, 2.0, 1.0)
        print("  ✅ MOSFET created and stored in database")
        
        print("  🔬 Testing FinFET creation...")
        geometry_manager.create_finfet_pattern(0.007, 0.053, 0.024, 4)
        print("  ✅ FinFET created and stored in database")
        
        # Test database statistics
        if hasattr(geometry_manager, 'db_manager') and geometry_manager.db_manager:
            stats = geometry_manager.db_manager.get_device_statistics()
            print(f"  📊 Database statistics: {stats.get('total_devices', 0)} devices")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ GeometryManager database integration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_industrial_examples_database():
    """Test industrial examples database integration"""
    print("\n🏭 Testing Industrial Examples Database")
    print("=" * 50)
    
    try:
        from geometry.database_integration import GeometryDatabaseManager
        from database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        geo_db = GeometryDatabaseManager(db_manager)
        
        # Test retrieving industrial examples
        logic_examples = geo_db.get_industrial_examples_by_category('logic_devices')
        power_examples = geo_db.get_industrial_examples_by_category('power_devices')
        mems_examples = geo_db.get_industrial_examples_by_category('mems_devices')
        
        print(f"✅ Logic devices: {len(logic_examples)} examples")
        print(f"✅ Power devices: {len(power_examples)} examples")
        print(f"✅ MEMS devices: {len(mems_examples)} examples")
        
        # Test creating a device from industrial example
        if logic_examples:
            example = logic_examples[0]
            print(f"  📱 Testing device creation from example: {example.get('example_name', 'Unknown')}")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Industrial examples database test failed: {e}")
        return False

def test_fabrication_flow_database():
    """Test fabrication flow database integration"""
    print("\n⚙️  Testing Fabrication Flow Database")
    print("=" * 50)
    
    try:
        from geometry.database_integration import GeometryDatabaseManager
        from database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        geo_db = GeometryDatabaseManager(db_manager)
        
        # Test creating a simple fabrication flow
        flow_steps = [
            {
                "step_name": "Substrate Preparation",
                "module": "geometry",
                "operation": "initialize_wafer",
                "parameters": {"material": "silicon"},
                "duration_minutes": 30
            },
            {
                "step_name": "Device Patterning",
                "module": "geometry",
                "operation": "create_mosfet_pattern",
                "parameters": {"gate_length": 0.18, "gate_width": 2.0},
                "duration_minutes": 60
            }
        ]
        
        flow_id = geo_db.create_fabrication_flow(
            flow_name="Test MOSFET Flow",
            device_type="MOSFET",
            flow_steps=flow_steps,
            required_modules=["geometry"]
        )
        
        print(f"✅ Fabrication flow created with ID: {flow_id}")
        
        # Test starting flow execution - create a proper wafer first
        from database_manager import DatabaseManager
        db_manager_raw = DatabaseManager()
        wafer_id = db_manager_raw.create_wafer(
            wafer_name=f"Test_Flow_Wafer_{int(time.time())}",
            diameter_mm=200.0,
            thickness_um=725.0,
            material="silicon",
            batch_id="TEST_BATCH",
            operator_id="test_operator"
        )
        db_manager_raw.close()

        execution_id = geo_db.start_flow_execution(flow_id, wafer_id)
        print(f"✅ Flow execution started with ID: {execution_id}")
        
        # Test completing flow execution
        success = geo_db.complete_flow_execution(execution_id, success=True, yield_percentage=95.0)
        if success:
            print("✅ Flow execution completed successfully")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Fabrication flow database test failed: {e}")
        return False

def test_gui_database_integration():
    """Test GUI database integration"""
    print("\n🖥️  Testing GUI Database Integration")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from gui.geometry_panel import GeometryPanel
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Create geometry panel with database integration
        panel = GeometryPanel()
        
        # Check if database integration is available
        if hasattr(panel, 'db_manager') and panel.db_manager:
            print("✅ GUI has database manager integration")
        else:
            print("⚠️ GUI database integration not available")
        
        # Check if geometry manager has database support
        if (hasattr(panel, 'geometry_manager') and panel.geometry_manager and 
            hasattr(panel.geometry_manager, 'db_manager') and panel.geometry_manager.db_manager):
            print("✅ GeometryManager in GUI has database support")
        else:
            print("⚠️ GeometryManager database support not available in GUI")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI database integration test failed: {e}")
        return False

def test_performance_and_caching():
    """Test database performance and caching"""
    print("\n⚡ Testing Database Performance and Caching")
    print("=" * 50)
    
    try:
        from geometry.database_integration import GeometryDatabaseManager
        from database_manager import DatabaseManager
        import numpy as np
        
        db_manager = DatabaseManager()
        geo_db = GeometryDatabaseManager(db_manager)
        
        # Test visualization caching
        import uuid
        device_id = str(uuid.uuid4())  # Use proper UUID format
        viz_data = np.random.rand(100, 100)
        viz_options = {'color_scheme': 'viridis', 'show_grid': True}
        
        # Cache visualization
        cache_id = geo_db.cache_visualization(
            device_id, 'cross_section', viz_options, viz_data
        )
        
        if cache_id:
            print("✅ Visualization data cached successfully")
            
            # Retrieve cached data
            cached_data, cached_image = geo_db.get_cached_visualization(
                device_id, 'cross_section', viz_options
            )
            
            if cached_data is not None:
                print("✅ Cached visualization data retrieved successfully")
            else:
                print("⚠️ Failed to retrieve cached visualization data")
        
        # Test cache cleanup
        cleaned = geo_db.cleanup_old_cache(days_old=0)  # Clean all for testing
        print(f"✅ Cache cleanup completed: {cleaned} entries removed")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Performance and caching test failed: {e}")
        return False

def run_complete_postgresql_integration_test():
    """Run complete PostgreSQL integration test suite"""
    print("🚀 Starting Geometry Module PostgreSQL Integration Tests")
    print("=" * 70)
    
    setup_logging()
    
    test_results = {
        'Database Connection': test_database_connection(),
        'Database Schema': test_geometry_database_schema(),
        'GeometryManager Integration': test_geometry_manager_database_integration(),
        'Industrial Examples': test_industrial_examples_database(),
        'Fabrication Flows': test_fabrication_flow_database(),
        'GUI Integration': test_gui_database_integration(),
        'Performance & Caching': test_performance_and_caching()
    }
    
    print("\n" + "=" * 70)
    print("📊 PostgreSQL Integration Test Results:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"🎯 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All PostgreSQL integration tests passed!")
        print("   Geometry module is fully integrated with PostgreSQL database.")
        return True
    else:
        print(f"⚠️ {total-passed} test(s) failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_complete_postgresql_integration_test()
    sys.exit(0 if success else 1)
