#!/usr/bin/env python3
"""
Test script for Lithography Integration with Simulation Pipeline
Validates integration with ProcessSimulator and workflow management
"""

import sys
import os
import time

# Add src/python to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

def test_lithography_integration():
    """Test lithography integration with simulation pipeline"""
    
    print("=" * 70)
    print("SemiPRO Lithography Integration Test")
    print("=" * 70)
    
    try:
        from enhanced_bindings import ProcessSimulator
        print("✓ ProcessSimulator imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ProcessSimulator: {e}")
        return False
    
    # Create simulator
    try:
        sim = ProcessSimulator()
        print("✓ ProcessSimulator created successfully")
        
        # Check if enhanced lithography is available
        if sim.enhanced_lithography:
            print("✓ Enhanced lithography manager loaded")
            processes = sim.enhanced_lithography.get_available_processes()
            print(f"  Available processes: {processes}")
        else:
            print("⚠ Enhanced lithography manager not available (using fallback)")
            
    except Exception as e:
        print(f"✗ Failed to create ProcessSimulator: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("BASIC LITHOGRAPHY SIMULATION TEST")
    print("=" * 70)
    
    # Create wafer
    try:
        wafer = sim.create_wafer("litho_test", diameter=200, thickness=525)
        print("✓ Test wafer created")
    except Exception as e:
        print(f"✗ Failed to create wafer: {e}")
        return False
    
    # Test basic lithography simulation
    try:
        result = sim.simulate_lithography(
            "litho_test", 
            technique="Optical DUV",
            wavelength=193.0,
            numerical_aperture=1.35,
            dose=25.0
        )
        print("✓ Basic lithography simulation completed")
        print(f"  Technique: {result['technique']}")
        print(f"  Wavelength: {result['wavelength']} nm")
        print(f"  Critical Dimension: {result['critical_dimension']:.1f} nm")
        print(f"  Line Edge Roughness: {result['line_edge_roughness']:.1f} nm")
        print(f"  Uniformity: {result['uniformity']:.1f}%")
        print(f"  Throughput: {result['throughput']:.0f} WPH")
        
    except Exception as e:
        print(f"✗ Basic lithography simulation failed: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("INDUSTRIAL PROCESS SIMULATION TEST")
    print("=" * 70)
    
    # Test industrial processes
    industrial_processes = ["cmos_gate_22nm", "euv_memory_1x", "finfet_7nm"]
    
    for process_name in industrial_processes:
        try:
            print(f"\n--- Testing {process_name} ---")
            result = sim.simulate_lithography("litho_test", process_name=process_name)
            print(f"✓ {process_name} simulation completed")
            print(f"  Process: {result.get('process_name', 'N/A')}")
            print(f"  Technique: {result.get('technique', 'N/A')}")
            print(f"  Critical Dimension: {result['critical_dimension']:.1f} nm")
            print(f"  Uniformity: {result['uniformity']:.1f}%")
            
        except Exception as e:
            print(f"✗ {process_name} simulation failed: {e}")
    
    print("\n" + "=" * 70)
    print("LITHOGRAPHY WORKFLOW TEST")
    print("=" * 70)
    
    # Test complete lithography workflows
    workflows = ["cmos_gate_22nm", "euv_memory_1x", "finfet_7nm"]
    
    for workflow_name in workflows:
        try:
            print(f"\n--- Testing {workflow_name} workflow ---")
            workflow_result = sim.simulate_lithography_workflow("litho_test", workflow_name)
            print(f"✓ {workflow_name} workflow completed")
            print(f"  Total steps: {workflow_result['quality_metrics']['total_steps']}")
            print(f"  Successful steps: {workflow_result['quality_metrics']['completed_steps']}")
            print(f"  Success rate: {workflow_result['quality_metrics']['success_rate']:.1%}")
            print(f"  Total time: {workflow_result['total_time']:.3f} seconds")
            
            # Show workflow steps
            print("  Workflow steps:")
            for i, step in enumerate(workflow_result['steps'], 1):
                status = "✓" if step['success'] else "✗"
                print(f"    {i}. {status} {step['step_type']}")
                
        except Exception as e:
            print(f"✗ {workflow_name} workflow failed: {e}")
    
    print("\n" + "=" * 70)
    print("COMPLETE PROCESS FLOW TEST")
    print("=" * 70)
    
    # Test complete semiconductor process flow with lithography
    try:
        # Create new wafer for complete flow
        flow_wafer = sim.create_wafer("complete_flow", diameter=200, thickness=525)
        print("✓ Complete flow wafer created")
        
        # Complete CMOS process flow
        print("\n📋 Complete CMOS Process Flow:")
        
        # 1. Thermal oxidation (gate oxide)
        ox_result = sim.simulate_oxidation("complete_flow", 1000.0, 1.0, "dry")
        print(f"1. ✓ Gate oxidation: {ox_result['oxide_thickness']:.1f}nm")
        
        # 2. Gate lithography
        litho_result = sim.simulate_lithography("complete_flow", process_name="cmos_gate_22nm")
        print(f"2. ✓ Gate lithography: {litho_result['technique']} (CD={litho_result['critical_dimension']:.1f}nm)")
        
        # 3. Gate deposition
        gate_result = sim.simulate_deposition("complete_flow", "polysilicon", 0.2, "LPCVD")
        print(f"3. ✓ Gate deposition: {gate_result['material']} {gate_result['thickness']}μm")
        
        # 4. Source/drain implantation
        implant_result = sim.simulate_ion_implantation("complete_flow", "boron", 30.0, 1e15)
        print(f"4. ✓ S/D implantation: {implant_result['species']} implant")
        
        # 5. Metal deposition
        metal_result = sim.simulate_deposition("complete_flow", "Al", 0.5, "Sputtering")
        print(f"5. ✓ Metal deposition: {metal_result['material']} {metal_result['thickness']}μm")
        
        # 6. Metal lithography
        metal_litho_result = sim.simulate_lithography("complete_flow", technique="Optical DUV")
        print(f"6. ✓ Metal lithography: {metal_litho_result['technique']}")
        
        # Generate process flow summary
        process_flow = sim.generate_process_flow("complete_flow")
        print(f"\n✓ Complete process flow: {len(process_flow)} steps")
        
        # Show final layer stack
        print(f"✓ Final layer stack: {len(flow_wafer.layers)} layers")
        for i, layer in enumerate(flow_wafer.layers):
            print(f"  Layer {i+1}: {layer['material']} ({layer['thickness']:.3f}μm)")
        
        print(f"✓ Total wafer thickness: {flow_wafer.get_total_thickness():.3f}μm")
        
    except Exception as e:
        print(f"✗ Complete process flow failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 70)
    print("SIMULATION SUMMARY")
    print("=" * 70)
    
    # Generate summary report
    try:
        summary = sim.create_summary_report()
        print(summary)
        
        # Export results
        sim.export_results("lithography_integration_results.json", "json")
        print("\n✓ Results exported to lithography_integration_results.json")
        
    except Exception as e:
        print(f"⚠ Summary generation failed: {e}")
    
    print("\n" + "=" * 70)
    print("LITHOGRAPHY INTEGRATION TEST COMPLETED SUCCESSFULLY")
    print("=" * 70)
    print("\nSummary:")
    print("✓ Basic lithography simulation working")
    print("✓ Industrial process simulation working")
    print("✓ Lithography workflow execution working")
    print("✓ Complete process flow integration working")
    print("✓ ProcessSimulator integration complete")
    print("\nThe SemiPRO Enhanced Lithography Module is fully integrated!")
    
    return True

if __name__ == "__main__":
    success = test_lithography_integration()
    sys.exit(0 if success else 1)
