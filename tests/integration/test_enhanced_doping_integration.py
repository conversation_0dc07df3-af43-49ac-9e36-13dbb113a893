#!/usr/bin/env python3
"""
Enhanced Doping Integration Test
===============================

Comprehensive test suite for enhanced doping module integration
following the same pattern as metallization integration tests.
"""

import sys
import os
import unittest
import logging
from typing import Dict, Any

# Add src/python to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'python'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestEnhancedDopingIntegration(unittest.TestCase):
    """Test enhanced doping integration across all layers"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_results = {}
        
    def test_enhanced_doping_bridge_creation(self):
        """Test enhanced doping bridge creation and initialization"""
        logger.info("Testing enhanced doping bridge creation...")
        
        try:
            from enhanced_doping_bridge import create_enhanced_doping_bridge
            bridge = create_enhanced_doping_bridge()
            
            self.assertIsNotNone(bridge)
            self.assertTrue(hasattr(bridge, 'simulate_ion_implantation'))
            self.assertTrue(hasattr(bridge, 'simulate_annealing'))
            self.assertTrue(hasattr(bridge, 'simulate_industrial_process'))
            
            # Test database initialization
            self.assertGreater(len(bridge.get_ion_species()), 0)
            self.assertGreater(len(bridge.get_available_equipment()), 0)
            self.assertGreater(len(bridge.get_available_processes()), 0)
            
            logger.info("✓ Enhanced doping bridge created successfully")
            self.test_results['bridge_creation'] = True
            
        except Exception as e:
            logger.error(f"✗ Enhanced doping bridge creation failed: {e}")
            self.test_results['bridge_creation'] = False
            self.fail(f"Bridge creation failed: {e}")
    
    def test_simulator_integration(self):
        """Test enhanced doping integration in main simulator"""
        logger.info("Testing simulator integration...")
        
        try:
            from simulator import Simulator
            from wafer import Wafer
            
            # Create simulator and wafer
            simulator = Simulator()
            wafer = Wafer("test_wafer", 200.0)
            simulator.wafer = wafer
            
            # Test enhanced ion implantation
            implant_result = simulator.run_ion_implantation(
                energy=40.0, dose=5e15, species="arsenic", tilt_angle=7.0, temperature=25.0)
            
            self.assertIsNotNone(implant_result)
            if isinstance(implant_result, dict):
                self.assertIn('species', implant_result)
                self.assertIn('energy', implant_result)
                self.assertIn('dose', implant_result)
            
            # Test enhanced annealing
            anneal_result = simulator.run_diffusion(
                temperature=1000.0, time=10.0, atmosphere="N2", rapid_thermal=True)
            
            self.assertIsNotNone(anneal_result)
            
            # Test industrial process
            industrial_result = simulator.run_industrial_doping_process("mosfet_source_drain")
            self.assertIsNotNone(industrial_result)
            
            logger.info("✓ Simulator integration successful")
            self.test_results['simulator_integration'] = True
            
        except Exception as e:
            logger.error(f"✗ Simulator integration failed: {e}")
            self.test_results['simulator_integration'] = False
            self.fail(f"Simulator integration failed: {e}")
    
    def test_enhanced_bindings_integration(self):
        """Test enhanced doping integration in ProcessSimulator"""
        logger.info("Testing enhanced bindings integration...")
        
        try:
            from enhanced_bindings import ProcessSimulator
            
            # Create process simulator
            simulator = ProcessSimulator()
            
            # Create test wafer
            simulator.create_wafer("test_wafer", 200.0)
            
            # Test ion implantation
            implant_result = simulator.simulate_ion_implantation(
                "test_wafer", "arsenic", 40.0, 5e15, 7.0, 25.0)
            
            self.assertIsNotNone(implant_result)
            self.assertIn('species', implant_result)
            self.assertEqual(implant_result['species'], 'arsenic')
            self.assertEqual(implant_result['energy'], 40.0)
            
            # Test annealing
            anneal_result = simulator.simulate_annealing(
                "test_wafer", 1000.0, 10.0, "N2", True)
            
            self.assertIsNotNone(anneal_result)
            self.assertIn('temperature', anneal_result)
            self.assertEqual(anneal_result['temperature'], 1000.0)
            
            # Test industrial process
            industrial_result = simulator.simulate_industrial_doping_process(
                "test_wafer", "mosfet_source_drain")
            
            self.assertIsNotNone(industrial_result)
            self.assertIn('process_name', industrial_result)
            
            logger.info("✓ Enhanced bindings integration successful")
            self.test_results['enhanced_bindings'] = True
            
        except Exception as e:
            logger.error(f"✗ Enhanced bindings integration failed: {e}")
            self.test_results['enhanced_bindings'] = False
            self.fail(f"Enhanced bindings integration failed: {e}")
    
    def test_industrial_processes(self):
        """Test all available industrial doping processes"""
        logger.info("Testing industrial doping processes...")
        
        try:
            from enhanced_doping_bridge import create_enhanced_doping_bridge
            from wafer import Wafer
            
            bridge = create_enhanced_doping_bridge()
            wafer = Wafer("test_wafer", 200.0)
            
            # Get available processes
            processes = bridge.get_available_processes()
            self.assertGreater(len(processes), 0)
            
            # Test each process
            successful_processes = 0
            for process_name in processes[:3]:  # Test first 3 processes
                try:
                    result = bridge.simulate_industrial_process(wafer, process_name)
                    self.assertIsNotNone(result)
                    self.assertIn('process_name', result)
                    self.assertIn('process_success', result)
                    
                    if result.get('process_success'):
                        successful_processes += 1
                        logger.info(f"  ✓ Process '{process_name}' completed successfully")
                    
                except Exception as e:
                    logger.warning(f"  ⚠ Process '{process_name}' failed: {e}")
            
            self.assertGreater(successful_processes, 0)
            logger.info(f"✓ Industrial processes tested: {successful_processes}/{len(processes[:3])} successful")
            self.test_results['industrial_processes'] = True
            
        except Exception as e:
            logger.error(f"✗ Industrial processes test failed: {e}")
            self.test_results['industrial_processes'] = False
            self.fail(f"Industrial processes test failed: {e}")
    
    def test_device_structure_creation(self):
        """Test device structure creation from doping results"""
        logger.info("Testing device structure creation...")
        
        try:
            from enhanced_doping_bridge import create_enhanced_doping_bridge
            from wafer import Wafer
            
            bridge = create_enhanced_doping_bridge()
            wafer = Wafer("test_wafer", 200.0)
            
            # Run industrial process that creates device structure
            result = bridge.simulate_industrial_process(wafer, "mosfet_source_drain")
            
            self.assertIsNotNone(result)
            self.assertIn('device_structure', result)
            
            device_structure = result['device_structure']
            self.assertIsNotNone(device_structure)
            self.assertTrue(hasattr(device_structure, 'name'))
            self.assertTrue(hasattr(device_structure, 'layers'))
            self.assertTrue(hasattr(device_structure, 'features'))
            self.assertTrue(hasattr(device_structure, 'critical_dimensions'))
            
            # Verify device structure content
            self.assertGreater(len(device_structure.layers), 0)
            self.assertGreater(len(device_structure.features), 0)
            self.assertGreater(len(device_structure.critical_dimensions), 0)
            
            logger.info("✓ Device structure creation successful")
            self.test_results['device_structure'] = True
            
        except Exception as e:
            logger.error(f"✗ Device structure creation failed: {e}")
            self.test_results['device_structure'] = False
            self.fail(f"Device structure creation failed: {e}")
    
    def test_parameter_validation(self):
        """Test parameter validation in enhanced doping bridge"""
        logger.info("Testing parameter validation...")
        
        try:
            from enhanced_doping_bridge import create_enhanced_doping_bridge
            
            bridge = create_enhanced_doping_bridge()
            
            # Test valid parameters
            valid_result = bridge.validate_parameters("arsenic", 40.0, 5e15)
            self.assertTrue(valid_result['valid'])
            self.assertEqual(len(valid_result['errors']), 0)
            
            # Test invalid species
            invalid_species = bridge.validate_parameters("invalid_species", 40.0, 5e15)
            self.assertFalse(invalid_species['valid'])
            self.assertGreater(len(invalid_species['errors']), 0)
            
            # Test invalid energy
            invalid_energy = bridge.validate_parameters("arsenic", 10000.0, 5e15)
            self.assertFalse(invalid_energy['valid'])
            self.assertGreater(len(invalid_energy['errors']), 0)
            
            # Test invalid dose
            invalid_dose = bridge.validate_parameters("arsenic", 40.0, 1e20)
            self.assertFalse(invalid_dose['valid'])
            self.assertGreater(len(invalid_dose['errors']), 0)
            
            logger.info("✓ Parameter validation working correctly")
            self.test_results['parameter_validation'] = True
            
        except Exception as e:
            logger.error(f"✗ Parameter validation failed: {e}")
            self.test_results['parameter_validation'] = False
            self.fail(f"Parameter validation failed: {e}")
    
    def tearDown(self):
        """Clean up after tests"""
        pass
    
    @classmethod
    def tearDownClass(cls):
        """Print final test summary"""
        print("\n" + "="*60)
        print("ENHANCED DOPING INTEGRATION TEST SUMMARY")
        print("="*60)
        
        # This will be populated by individual tests
        # We'll print a summary in the main execution

def run_integration_tests():
    """Run all integration tests and print summary"""
    suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedDopingIntegration)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "="*60)
    print("ENHANCED DOPING INTEGRATION TEST SUMMARY")
    print("="*60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")
    
    print("="*60)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
