-- =====================================================================
-- SemiPRO PostgreSQL Database Schema Design
-- =====================================================================
-- Comprehensive database schema for tracking wafer state, module progress,
-- and inter-module connectivity in the SemiPRO semiconductor simulator
-- 
-- Author: Dr. <PERSON><PERSON><PERSON> Mohammed
-- =====================================================================

-- Enable UUID extension for unique identifiers
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================================
-- CORE WAFER MANAGEMENT TABLES
-- =====================================================================

-- Wafers table - Central wafer tracking
CREATE TABLE wafers (
    wafer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wafer_name VARCHAR(255) NOT NULL UNIQUE,
    diameter_mm DECIMAL(10,3) NOT NULL DEFAULT 200.0,
    thickness_um DECIMAL(10,3) NOT NULL DEFAULT 775.0,
    material VARCHAR(100) NOT NULL DEFAULT 'silicon',
    crystal_orientation VARCHAR(20) DEFAULT '100',
    doping_type VARCHAR(20) DEFAULT 'p-type',
    resistivity_ohm_cm DECIMAL(15,6),
    
    -- Wafer state tracking
    current_status VARCHAR(50) DEFAULT 'initialized',
    current_module VARCHAR(50),
    process_step_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Grid and physical properties
    grid_rows INTEGER DEFAULT 100,
    grid_cols INTEGER DEFAULT 100,
    temperature_k DECIMAL(10,3) DEFAULT 300.0,
    
    -- Quality metrics
    defect_count INTEGER DEFAULT 0,
    quality_score DECIMAL(5,2) DEFAULT 100.0,
    
    -- Metadata
    batch_id VARCHAR(100),
    lot_id VARCHAR(100),
    operator_id VARCHAR(100),
    notes TEXT
);

-- Wafer layers table - Track all deposited/etched layers
CREATE TABLE wafer_layers (
    layer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wafer_id UUID NOT NULL REFERENCES wafers(wafer_id) ON DELETE CASCADE,
    layer_sequence INTEGER NOT NULL,
    
    -- Layer properties
    material VARCHAR(100) NOT NULL,
    thickness_nm DECIMAL(15,6) NOT NULL,
    deposition_technique VARCHAR(100),
    
    -- Layer quality
    uniformity_percent DECIMAL(5,2),
    stress_mpa DECIMAL(15,6),
    grain_size_nm DECIMAL(10,3),
    surface_roughness_nm DECIMAL(10,3),
    
    -- Process conditions
    temperature_c DECIMAL(10,3),
    pressure_torr DECIMAL(15,6),
    time_seconds DECIMAL(10,3),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Module that created this layer
    created_by_module VARCHAR(50) NOT NULL,
    process_step_id UUID
);

-- =====================================================================
-- MODULE PROGRESS TRACKING TABLES
-- =====================================================================

-- Modules table - Define all available modules
CREATE TABLE modules (
    module_id VARCHAR(50) PRIMARY KEY,
    module_name VARCHAR(255) NOT NULL,
    module_type VARCHAR(100) NOT NULL,
    version VARCHAR(50),
    backend_type VARCHAR(50) DEFAULT 'python', -- 'cython', 'cpp', 'python'
    is_active BOOLEAN DEFAULT true,
    
    -- Module capabilities
    supports_wafer_input BOOLEAN DEFAULT true,
    supports_wafer_output BOOLEAN DEFAULT true,
    supports_simulation BOOLEAN DEFAULT true,
    
    -- Configuration
    config_schema JSONB,
    default_parameters JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Process steps table - Track individual simulation steps
CREATE TABLE process_steps (
    step_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wafer_id UUID NOT NULL REFERENCES wafers(wafer_id) ON DELETE CASCADE,
    module_id VARCHAR(50) NOT NULL REFERENCES modules(module_id),
    
    -- Step identification
    step_name VARCHAR(255) NOT NULL,
    step_sequence INTEGER NOT NULL,
    step_type VARCHAR(100) NOT NULL, -- 'deposition', 'etching', 'lithography', etc.
    
    -- Execution status
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed'
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds DECIMAL(10,3),
    
    -- Process parameters (stored as JSON for flexibility)
    input_parameters JSONB,
    output_results JSONB,
    
    -- Quality metrics
    success_rate DECIMAL(5,2),
    quality_metrics JSONB,
    
    -- Error handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Relationships
    parent_step_id UUID REFERENCES process_steps(step_id),
    workflow_id UUID,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================================
-- INTER-MODULE CONNECTIVITY TABLES
-- =====================================================================

-- Wafer transfers table - Track wafer movement between modules
CREATE TABLE wafer_transfers (
    transfer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wafer_id UUID NOT NULL REFERENCES wafers(wafer_id) ON DELETE CASCADE,
    
    -- Transfer details
    from_module VARCHAR(50) REFERENCES modules(module_id),
    to_module VARCHAR(50) NOT NULL REFERENCES modules(module_id),
    transfer_type VARCHAR(50) DEFAULT 'simulation_complete', -- 'manual', 'automatic', 'simulation_complete'
    
    -- Wafer state at transfer
    wafer_state_before JSONB,
    wafer_state_after JSONB,
    
    -- Transfer metadata
    transfer_reason TEXT,
    operator_id VARCHAR(100),
    
    -- Timestamps
    initiated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR(50) DEFAULT 'pending' -- 'pending', 'in_progress', 'completed', 'failed'
);

-- Module dependencies table - Define module execution order
CREATE TABLE module_dependencies (
    dependency_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    dependent_module VARCHAR(50) NOT NULL REFERENCES modules(module_id),
    required_module VARCHAR(50) NOT NULL REFERENCES modules(module_id),
    dependency_type VARCHAR(50) DEFAULT 'sequential', -- 'sequential', 'parallel', 'optional'
    
    -- Conditions for dependency
    condition_parameters JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(dependent_module, required_module)
);

-- =====================================================================
-- SIMULATION RESULTS AND ANALYTICS TABLES
-- =====================================================================

-- Simulation sessions table - Track complete simulation runs
CREATE TABLE simulation_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_name VARCHAR(255) NOT NULL,
    
    -- Session details
    wafer_id UUID NOT NULL REFERENCES wafers(wafer_id) ON DELETE CASCADE,
    workflow_type VARCHAR(100),
    total_steps INTEGER DEFAULT 0,
    completed_steps INTEGER DEFAULT 0,
    
    -- Session status
    status VARCHAR(50) DEFAULT 'active', -- 'active', 'completed', 'paused', 'failed'
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Results summary
    overall_quality_score DECIMAL(5,2),
    success_rate DECIMAL(5,2),
    total_processing_time_seconds DECIMAL(15,6),
    
    -- Configuration
    session_parameters JSONB,
    
    -- Metadata
    operator_id VARCHAR(100),
    notes TEXT
);

-- Device structures table - Track final device outcomes
CREATE TABLE device_structures (
    device_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    wafer_id UUID NOT NULL REFERENCES wafers(wafer_id) ON DELETE CASCADE,
    session_id UUID REFERENCES simulation_sessions(session_id),
    
    -- Device identification
    device_name VARCHAR(255) NOT NULL,
    device_type VARCHAR(100) NOT NULL, -- 'transistor', 'capacitor', 'interconnect', etc.
    
    -- Device properties
    total_layers INTEGER,
    total_thickness_nm DECIMAL(15,6),
    active_area_um2 DECIMAL(15,6),
    
    -- Electrical properties
    electrical_properties JSONB,
    
    -- Performance metrics
    performance_metrics JSONB,
    
    -- Quality assessment
    meets_specifications BOOLEAN,
    quality_score DECIMAL(5,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================================

-- Wafer tracking indexes
CREATE INDEX idx_wafers_status ON wafers(current_status);
CREATE INDEX idx_wafers_module ON wafers(current_module);
CREATE INDEX idx_wafers_created ON wafers(created_at);
CREATE INDEX idx_wafers_batch ON wafers(batch_id);

-- Layer tracking indexes
CREATE INDEX idx_layers_wafer ON wafer_layers(wafer_id);
CREATE INDEX idx_layers_sequence ON wafer_layers(wafer_id, layer_sequence);
CREATE INDEX idx_layers_module ON wafer_layers(created_by_module);

-- Process step indexes
CREATE INDEX idx_steps_wafer ON process_steps(wafer_id);
CREATE INDEX idx_steps_module ON process_steps(module_id);
CREATE INDEX idx_steps_status ON process_steps(status);
CREATE INDEX idx_steps_sequence ON process_steps(wafer_id, step_sequence);
CREATE INDEX idx_steps_created ON process_steps(created_at);

-- Transfer tracking indexes
CREATE INDEX idx_transfers_wafer ON wafer_transfers(wafer_id);
CREATE INDEX idx_transfers_from ON wafer_transfers(from_module);
CREATE INDEX idx_transfers_to ON wafer_transfers(to_module);
CREATE INDEX idx_transfers_status ON wafer_transfers(status);

-- Session tracking indexes
CREATE INDEX idx_sessions_wafer ON simulation_sessions(wafer_id);
CREATE INDEX idx_sessions_status ON simulation_sessions(status);
CREATE INDEX idx_sessions_started ON simulation_sessions(started_at);

-- =====================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================================

-- Update wafer updated_at timestamp
CREATE OR REPLACE FUNCTION update_wafer_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER wafer_update_timestamp
    BEFORE UPDATE ON wafers
    FOR EACH ROW
    EXECUTE FUNCTION update_wafer_timestamp();

-- Update wafer process step count
CREATE OR REPLACE FUNCTION update_wafer_step_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE wafers 
        SET process_step_count = process_step_count + 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE wafer_id = NEW.wafer_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE wafers 
        SET process_step_count = process_step_count - 1,
            updated_at = CURRENT_TIMESTAMP
        WHERE wafer_id = OLD.wafer_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER wafer_step_count_trigger
    AFTER INSERT OR DELETE ON process_steps
    FOR EACH ROW
    EXECUTE FUNCTION update_wafer_step_count();

-- =====================================================================
-- INITIAL DATA SETUP
-- =====================================================================

-- Insert default modules
INSERT INTO modules (module_id, module_name, module_type, backend_type, supports_simulation) VALUES
('deposition', 'Enhanced Deposition', 'process', 'python', true),
('lithography', 'Enhanced Lithography', 'process', 'python', true),
('etching', 'Enhanced Etching', 'process', 'python', true),
('metallization', 'Enhanced Metallization', 'process', 'python', true),
('thermal', 'Enhanced Thermal', 'analysis', 'python', true),
('packaging', 'Enhanced Packaging', 'assembly', 'python', true),
('reliability', 'Enhanced Reliability', 'analysis', 'python', true),
('oxidation', 'Oxidation', 'process', 'python', true),
('doping', 'Ion Implantation & Doping', 'process', 'python', true),
('geometry', 'Geometry Management', 'utility', 'python', false),
('annealing', 'Thermal Annealing', 'process', 'python', true),
('cmp', 'Chemical Mechanical Polishing', 'process', 'python', true);

-- =====================================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================================

-- Wafer status overview
CREATE VIEW wafer_status_overview AS
SELECT 
    w.wafer_id,
    w.wafer_name,
    w.current_status,
    w.current_module,
    w.process_step_count,
    w.quality_score,
    COUNT(ps.step_id) as total_steps,
    COUNT(CASE WHEN ps.status = 'completed' THEN 1 END) as completed_steps,
    COUNT(CASE WHEN ps.status = 'failed' THEN 1 END) as failed_steps,
    w.created_at,
    w.updated_at
FROM wafers w
LEFT JOIN process_steps ps ON w.wafer_id = ps.wafer_id
GROUP BY w.wafer_id, w.wafer_name, w.current_status, w.current_module, 
         w.process_step_count, w.quality_score, w.created_at, w.updated_at;

-- Module utilization statistics
CREATE VIEW module_utilization_stats AS
SELECT 
    m.module_id,
    m.module_name,
    COUNT(ps.step_id) as total_steps,
    COUNT(CASE WHEN ps.status = 'completed' THEN 1 END) as completed_steps,
    COUNT(CASE WHEN ps.status = 'failed' THEN 1 END) as failed_steps,
    AVG(ps.duration_seconds) as avg_duration_seconds,
    AVG(CASE WHEN ps.success_rate IS NOT NULL THEN ps.success_rate END) as avg_success_rate
FROM modules m
LEFT JOIN process_steps ps ON m.module_id = ps.module_id
GROUP BY m.module_id, m.module_name;

-- =====================================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================================

COMMENT ON TABLE wafers IS 'Central wafer tracking with physical properties and current state';
COMMENT ON TABLE wafer_layers IS 'Individual layers deposited or modified on wafers';
COMMENT ON TABLE modules IS 'Available simulation modules and their capabilities';
COMMENT ON TABLE process_steps IS 'Individual simulation steps executed on wafers';
COMMENT ON TABLE wafer_transfers IS 'Track wafer movement between modules for inter-module connectivity';
COMMENT ON TABLE module_dependencies IS 'Define execution order and dependencies between modules';
COMMENT ON TABLE simulation_sessions IS 'Complete simulation runs spanning multiple modules';
COMMENT ON TABLE device_structures IS 'Final device outcomes and performance metrics';

-- =====================================================================
-- END OF SCHEMA
-- =====================================================================
